export interface ConsumptionRatio {
  id: string;
  name: string;
  unit: string;
  ratioPerSqm: number;
  category: string;
  formula: string;
  source: string;
  materialType?: string;
}

// Comprehensive consumption ratios based on IS codes and engineering standards
// ALL RATIOS IN SI UNITS (per square meter)
export const consumptionRatios: ConsumptionRatio[] = [
  // Masonry Work (per sqm of wall area)
  { 
    id: 'red_brick_230mm', 
    name: 'Red Clay Bricks (230mm wall)', 
    unit: 'pieces', 
    ratioPerSqm: 110,
    category: 'masonry',
    formula: '110 bricks per sqm for 230mm (9-inch) wall',
    source: 'IS 1077:1992',
    materialType: 'red_brick'
  },
  { 
    id: 'red_brick_115mm', 
    name: 'Red Clay Bricks (115mm wall)', 
    unit: 'pieces', 
    ratioPerSqm: 55,
    category: 'masonry',
    formula: '55 bricks per sqm for 115mm (4.5-inch) wall',
    source: 'IS 1077:1992',
    materialType: 'red_brick'
  },
  { 
    id: 'aac_blocks_200mm', 
    name: 'AAC Blocks (200mm wall)', 
    unit: 'pieces', 
    ratioPerSqm: 8.33,
    category: 'masonry',
    formula: '8.33 blocks per sqm (600x200x200mm blocks)',
    source: 'Manufacturer Standards',
    materialType: 'aac_blocks'
  },
  { 
    id: 'aac_blocks_150mm', 
    name: 'AAC Blocks (150mm wall)', 
    unit: 'pieces', 
    ratioPerSqm: 11.11,
    category: 'masonry',
    formula: '11.11 blocks per sqm (600x200x150mm blocks)',
    source: 'Manufacturer Standards',
    materialType: 'aac_blocks'
  },

  // Plastering Work (per sqm of surface area)
  { 
    id: 'cement_plaster_12mm', 
    name: 'Cement Plaster (12mm thick)', 
    unit: 'kg cement', 
    ratioPerSqm: 3.6,
    category: 'plastering',
    formula: '3.6 kg cement per sqm for 12mm thick plaster (1:4 ratio)',
    source: 'IS 1661:1972'
  },
  { 
    id: 'sand_plaster_12mm', 
    name: 'Sand for Plaster (12mm thick)', 
    unit: 'cft', 
    ratioPerSqm: 0.43,
    category: 'plastering',
    formula: '0.43 cft sand per sqm for 12mm thick plaster',
    source: 'IS 1661:1972'
  },

  // Flooring Work (per sqm of floor area)
  { 
    id: 'ceramic_tiles_flooring', 
    name: 'Ceramic Tiles Flooring', 
    unit: 'sqm', 
    ratioPerSqm: 1.05,
    category: 'flooring',
    formula: '5% wastage included',
    source: 'Industry Standard'
  },
  { 
    id: 'vitrified_tiles_flooring', 
    name: 'Vitrified Tiles Flooring', 
    unit: 'sqm', 
    ratioPerSqm: 1.08,
    category: 'flooring',
    formula: '8% wastage for large format tiles',
    source: 'Industry Standard'
  },

  // Painting Work (per sqm of surface area)
  { 
    id: 'interior_paint_coverage', 
    name: 'Interior Paint Coverage', 
    unit: 'liters', 
    ratioPerSqm: 0.13,
    category: 'painting',
    formula: '130 sqm per liter coverage (2 coats)',
    source: 'Manufacturer Specifications'
  },
  { 
    id: 'exterior_paint_coverage', 
    name: 'Exterior Paint Coverage', 
    unit: 'liters', 
    ratioPerSqm: 0.15,
    category: 'painting',
    formula: '100 sqm per liter coverage (2 coats)',
    source: 'Manufacturer Specifications'
  },
  { 
    id: 'wall_putty_coverage', 
    name: 'Wall Putty Coverage', 
    unit: 'kg', 
    ratioPerSqm: 0.77,
    category: 'painting',
    formula: '1.3 sqm per kg coverage',
    source: 'Manufacturer Specifications'
  },

  // Electrical Work (per sqm of built-up area)
  { 
    id: 'electrical_points_residential', 
    name: 'Electrical Points (Residential)', 
    unit: 'points', 
    ratioPerSqm: 0.86,
    category: 'electrical',
    formula: '8 points per 100 sqft = 0.86 points per sqm',
    source: 'IS 732:1989'
  },
  { 
    id: 'electrical_wire_per_point', 
    name: 'Electrical Wire per Point', 
    unit: 'meters', 
    ratioPerSqm: 10.3,
    category: 'electrical',
    formula: 'Average 12 meters wire per electrical point',
    source: 'Industry Practice'
  },

  // Plumbing Work (per sqm of built-up area)
  { 
    id: 'plumbing_points_residential', 
    name: 'Plumbing Points (Residential)', 
    unit: 'points', 
    ratioPerSqm: 0.43,
    category: 'plumbing',
    formula: '4 points per 100 sqft = 0.43 points per sqm',
    source: 'IS 1172:1993'
  },
  { 
    id: 'cpvc_pipe_per_point', 
    name: 'CPVC Pipe per Point', 
    unit: 'meters', 
    ratioPerSqm: 3.44,
    category: 'plumbing',
    formula: 'Average 8 meters pipe per plumbing point',
    source: 'Industry Practice'
  },

  // Doors & Windows (based on architectural norms per sqm)
  { 
    id: 'main_door_area', 
    name: 'Main Door Area', 
    unit: 'sqm', 
    ratioPerSqm: 0.0032,
    category: 'doors_windows',
    formula: '3.25 sqm main door per 1000 sqm built-up',
    source: 'Architectural Standards'
  },
  { 
    id: 'internal_door_area', 
    name: 'Internal Door Area', 
    unit: 'sqm', 
    ratioPerSqm: 0.014,
    category: 'doors_windows',
    formula: '1.86 sqm per door, 1 door per 133 sqm',
    source: 'Architectural Standards'
  },
  { 
    id: 'window_area', 
    name: 'Window Area', 
    unit: 'sqm', 
    ratioPerSqm: 0.15,
    category: 'doors_windows',
    formula: '15% of floor area as per NBC',
    source: 'National Building Code'
  }
];

// Helper function to get consumption ratio by ID
export function getConsumptionRatio(id: string): ConsumptionRatio | undefined {
  return consumptionRatios.find(ratio => ratio.id === id);
}

// Helper function to get consumption ratios by category
export function getConsumptionRatiosByCategory(category: string): ConsumptionRatio[] {
  return consumptionRatios.filter(ratio => ratio.category === category);
}

// Helper function to convert sqft to sqm
export function convertSqftToSqm(sqft: number): number {
  return sqft / 10.764;
}

// Helper function to convert sqm to sqft
export function convertSqmToSqft(sqm: number): number {
  return sqm * 10.764;
}