import React, { useState, useEffect } from 'react';
import { Save, Info, AlertTriangle, Plus, Trash2, Edit, Check } from 'lucide-react';
import { supabase } from '../../lib/supabase';

interface StructuralAssumption {
  id?: string;
  name: string;
  value: number;
  unit: string;
  description?: string;
}

interface ColumnBeamSizing {
  id?: string;
  floors: string;
  column_size: string;
  beam_width: number;
}

interface ConcreteMix {
  id?: string;
  grade: string;
  cement_bags_per_m3: number;
  sand_cft_per_m3: number;
  aggregate_cft_per_m3: number;
  water_cement_ratio: number;
  applications?: string[];
}

interface SteelReinforcement {
  id?: string;
  structural_element: string;
  steel_ratio_kg_per_m3: number;
  notes?: string;
}

interface MaterialWastage {
  id?: string;
  material: string;
  wastage_percentage: number;
}

interface MaterialConsumption {
  id?: string;
  name: string;
  value: number;
  unit: string;
  description?: string;
}

interface MaterialRate {
  id?: string;
  name: string;
  category: string;
  rate: number;
  unit: string;
  quality_tier?: string;
  location: string;
}

interface CalculationFactor {
  id?: string;
  factor_name: string;
  factor_value: number;
  unit?: string;
  category: string;
  description?: string;
}

interface OpeningSize {
  id?: string;
  opening_type: string;
  dimension_type: string;
  value: number;
  unit: string;
  description?: string;
}

interface LumpSumComponent {
  id?: string;
  name: string;
  category: string;
  sub_category?: string;
  brand?: string;
  unit_price: number;
  unit: string;
  specifications?: any;
  is_active?: boolean;
}

export function EngineeringStandardsTab() {
  const [activeSubTab, setActiveSubTab] = useState<'structural' | 'concrete' | 'steel' | 'material' | 'rates' | 'factors' | 'openings' | 'lumpsum'>('structural');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  
  // Structural Assumptions - will be loaded from database
  const [structuralAssumptions, setStructuralAssumptions] = useState<StructuralAssumption[]>([]);

  // Column & Beam Sizing - will be calculated from database values
  const [columnBeamSizing, setColumnBeamSizing] = useState<ColumnBeamSizing[]>([]);
  
  // Concrete Mix Designs
  const [concreteMixes, setConcreteMixes] = useState<ConcreteMix[]>([
    { grade: 'M20', cement_bags_per_m3: 8.5, sand_cft_per_m3: 15.0, aggregate_cft_per_m3: 29.0, water_cement_ratio: 0.50 },
    { grade: 'M25', cement_bags_per_m3: 10.5, sand_cft_per_m3: 12.5, aggregate_cft_per_m3: 25.0, water_cement_ratio: 0.45 },
    { grade: 'M30', cement_bags_per_m3: 12.5, sand_cft_per_m3: 11.0, aggregate_cft_per_m3: 22.0, water_cement_ratio: 0.45 }
  ]);
  
  // Steel Reinforcement
  const [steelReinforcement, setSteelReinforcement] = useState<SteelReinforcement[]>([
    { structural_element: 'Isolated Footings', steel_ratio_kg_per_m3: 120 },
    { structural_element: 'Strip Footings', steel_ratio_kg_per_m3: 140 },
    { structural_element: 'Raft Foundation', steel_ratio_kg_per_m3: 160 },
    { structural_element: 'Ground Floor Columns', steel_ratio_kg_per_m3: 200 },
    { structural_element: 'Typical Floor Columns', steel_ratio_kg_per_m3: 180 },
    { structural_element: 'Basement Columns', steel_ratio_kg_per_m3: 220 },
    { structural_element: 'Main Beams', steel_ratio_kg_per_m3: 160 },
    { structural_element: 'Secondary Beams', steel_ratio_kg_per_m3: 140 },
    { structural_element: 'One-Way Slab', steel_ratio_kg_per_m3: 110 },
    { structural_element: 'Two-Way Slab', steel_ratio_kg_per_m3: 120 },
    { structural_element: 'Cantilever Slab', steel_ratio_kg_per_m3: 160 }
  ]);
  
  // Material Consumption
  const [materialConsumption, setMaterialConsumption] = useState<MaterialConsumption[]>([
    { name: 'Bricks per m² (230mm Wall)', value: 98, unit: 'pieces', description: 'Corrected standard brick consumption' },
    { name: 'Bricks per m² (115mm Wall)', value: 49, unit: 'pieces', description: 'Corrected standard brick consumption' }
  ]);
  
  // Material Wastage
  const [materialWastage, setMaterialWastage] = useState<MaterialWastage[]>([
    { material: 'Cement', wastage_percentage: 3 },
    { material: 'Sand', wastage_percentage: 8 },
    { material: 'Aggregate', wastage_percentage: 5 },
    { material: 'Steel', wastage_percentage: 5 },
    { material: 'Bricks', wastage_percentage: 7 },
    { material: 'Paint', wastage_percentage: 5 }
  ]);

  // NEW CONFIGURABLE PARAMETERS
  // Material Rates - will be loaded from database
  const [materialRates, setMaterialRates] = useState<MaterialRate[]>([]);

  // Calculation Factors - will be loaded from database
  const [calculationFactors, setCalculationFactors] = useState<CalculationFactor[]>([]);

  // Opening Sizes - will be loaded from database
  const [openingSizes, setOpeningSizes] = useState<OpeningSize[]>([]);

  // Lump Sum Components - will be loaded from database
  const [lumpSumComponents, setLumpSumComponents] = useState<LumpSumComponent[]>([]);

  // Load data from database
  useEffect(() => {
    loadEngineeringStandards();
  }, [activeSubTab]);

  const loadEngineeringStandards = async () => {
    setIsLoading(true);
    try {
      if (activeSubTab === 'structural') {
        // Load structural parameters from engineering_standards table
        const { data: structuralData, error: structuralError } = await supabase
          .from('engineering_standards')
          .select('*')
          .eq('category', 'structural')
          .eq('is_active', true);

        if (structuralError) throw structuralError;

        if (structuralData && structuralData.length > 0) {
          const formattedStructural = structuralData.map(item => ({
            id: item.id,
            name: item.name,
            value: parseFloat(item.value),
            unit: item.unit,
            description: item.description
          }));
          setStructuralAssumptions(formattedStructural);
        }

        // Update column beam sizing with new format
        const floorHeight = structuralData?.find(item => item.name === 'Floor Height')?.value || 3.0;
        const beamWidth = structuralData?.find(item => item.name === 'Beam Width')?.value || 300;
        const beamDepth = structuralData?.find(item => item.name === 'Beam Depth')?.value || 450;
        const columnLowRise = structuralData?.find(item => item.name === 'Column Size Low Rise')?.value || 350;
        const columnMidRise = structuralData?.find(item => item.name === 'Column Size Mid Rise')?.value || 400;
        const columnHighRise = structuralData?.find(item => item.name === 'Column Size High Rise')?.value || 450;

        setColumnBeamSizing([
          { floors: 'G+1 (Low Rise)', column_size: `${columnLowRise} x ${columnLowRise}`, beam_width: beamWidth },
          { floors: 'G+2 to G+4 (Mid Rise)', column_size: `${columnMidRise} x ${columnMidRise}`, beam_width: beamWidth },
          { floors: 'G+5+ (High Rise)', column_size: `${columnHighRise} x ${columnHighRise}`, beam_width: beamWidth }
        ]);
      }

      if (activeSubTab === 'rates') {
        // Load material rates from material_rates table
        const { data: ratesData, error: ratesError } = await supabase
          .from('material_rates')
          .select('*')
          .eq('is_active', true)
          .order('category', { ascending: true });

        if (ratesError) throw ratesError;
        if (ratesData) {
          setMaterialRates(ratesData);
        }
      }

      if (activeSubTab === 'factors') {
        // Load calculation factors from calculation_factors table
        const { data: factorsData, error: factorsError } = await supabase
          .from('calculation_factors')
          .select('*')
          .eq('is_active', true)
          .order('category', { ascending: true });

        if (factorsError) throw factorsError;
        if (factorsData) {
          setCalculationFactors(factorsData);
        }
      }

      if (activeSubTab === 'openings') {
        // Load opening sizes from opening_sizes table
        const { data: openingsData, error: openingsError } = await supabase
          .from('opening_sizes')
          .select('*')
          .eq('is_active', true)
          .order('opening_type', { ascending: true });

        if (openingsError) throw openingsError;
        if (openingsData) {
          setOpeningSizes(openingsData);
        }
      }

      if (activeSubTab === 'lumpsum') {
        // Load lump sum components from components table
        const { data: lumpSumData, error: lumpSumError } = await supabase
          .from('components')
          .select('*')
          .eq('cost_model', 'per_unit')
          .eq('unit', 'lump_sum')
          .eq('is_active', true)
          .order('category', { ascending: true });

        if (lumpSumError) throw lumpSumError;
        if (lumpSumData) {
          setLumpSumComponents(lumpSumData);
        }
      }

    } catch (error) {
      console.error('Error loading engineering standards:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveStructuralAssumptions = async () => {
    setIsSaving(true);
    try {
      // Save structural assumptions to engineering_standards table
      for (const assumption of structuralAssumptions) {
        if (assumption.id) {
          // Update existing record
          const { error } = await supabase
            .from('engineering_standards')
            .update({
              value: assumption.value.toString(),
              unit: assumption.unit,
              description: assumption.description,
              updated_at: new Date().toISOString()
            })
            .eq('id', assumption.id);

          if (error) throw error;
        } else {
          // Insert new record
          const { error } = await supabase
            .from('engineering_standards')
            .insert({
              name: assumption.name,
              value: assumption.value.toString(),
              unit: assumption.unit,
              description: assumption.description,
              category: 'structural',
              is_active: true
            });

          if (error) throw error;
        }
      }

      alert('Structural assumptions saved successfully!');
      // Reload data to get updated IDs
      await loadEngineeringStandards();
    } catch (error) {
      console.error('Error saving structural assumptions:', error);
      alert('Error saving structural assumptions: ' + error.message);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveConcreteMixes = async () => {
    setIsSaving(true);
    try {
      // In a real implementation, this would save to the database
      console.log('Saving concrete mixes:', concreteMixes);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Concrete mix designs saved successfully!');
    } catch (error) {
      console.error('Error saving concrete mixes:', error);
      alert('Error saving concrete mixes');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveSteelReinforcement = async () => {
    setIsSaving(true);
    try {
      // In a real implementation, this would save to the database
      console.log('Saving steel reinforcement:', steelReinforcement);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Steel reinforcement ratios saved successfully!');
    } catch (error) {
      console.error('Error saving steel reinforcement:', error);
      alert('Error saving steel reinforcement');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveMaterialConsumption = async () => {
    setIsSaving(true);
    try {
      // In a real implementation, this would save to the database
      console.log('Saving material consumption:', materialConsumption);
      console.log('Saving material wastage:', materialWastage);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Material consumption and wastage saved successfully!');
    } catch (error) {
      console.error('Error saving material consumption:', error);
      alert('Error saving material consumption');
    } finally {
      setIsSaving(false);
    }
  };

  // Helper functions for updating state
  const updateStructuralAssumption = (index: number, field: keyof StructuralAssumption, value: any) => {
    const updated = [...structuralAssumptions];
    updated[index] = { ...updated[index], [field]: value };
    setStructuralAssumptions(updated);
  };

  const updateColumnBeamSizing = (index: number, field: keyof ColumnBeamSizing, value: any) => {
    const updated = [...columnBeamSizing];
    updated[index] = { ...updated[index], [field]: value };
    setColumnBeamSizing(updated);
  };

  const updateConcreteMix = (index: number, field: keyof ConcreteMix, value: any) => {
    const updated = [...concreteMixes];
    updated[index] = { ...updated[index], [field]: value };
    setConcreteMixes(updated);
  };

  const addConcreteMix = () => {
    setConcreteMixes([...concreteMixes, { 
      grade: '', 
      cement_bags_per_m3: 0, 
      sand_cft_per_m3: 0, 
      aggregate_cft_per_m3: 0, 
      water_cement_ratio: 0 
    }]);
  };

  const removeConcreteMix = (index: number) => {
    setConcreteMixes(concreteMixes.filter((_, i) => i !== index));
  };

  const updateSteelReinforcement = (index: number, field: keyof SteelReinforcement, value: any) => {
    const updated = [...steelReinforcement];
    updated[index] = { ...updated[index], [field]: value };
    setSteelReinforcement(updated);
  };

  const addSteelReinforcement = () => {
    setSteelReinforcement([...steelReinforcement, { 
      structural_element: '', 
      steel_ratio_kg_per_m3: 0 
    }]);
  };

  const removeSteelReinforcement = (index: number) => {
    setSteelReinforcement(steelReinforcement.filter((_, i) => i !== index));
  };

  const updateMaterialConsumption = (index: number, field: keyof MaterialConsumption, value: any) => {
    const updated = [...materialConsumption];
    updated[index] = { ...updated[index], [field]: value };
    setMaterialConsumption(updated);
  };

  const updateMaterialWastage = (index: number, field: keyof MaterialWastage, value: any) => {
    const updated = [...materialWastage];
    updated[index] = { ...updated[index], [field]: value };
    setMaterialWastage(updated);
  };

  const addMaterialWastage = () => {
    setMaterialWastage([...materialWastage, { 
      material: '', 
      wastage_percentage: 0 
    }]);
  };

  const removeMaterialWastage = (index: number) => {
    setMaterialWastage(materialWastage.filter((_, i) => i !== index));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">Engineering Standards</h3>
        <div className="flex items-center gap-2 text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-lg">
          <Info className="w-4 h-4" />
          <span>IS Code Compliant Parameters</span>
        </div>
      </div>

      {/* Sub-tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-4 overflow-x-auto">
          {[
            { id: 'structural', label: 'Structural Assumptions' },
            { id: 'rates', label: 'Material Rates' },
            { id: 'factors', label: 'Calculation Factors' },
            { id: 'openings', label: 'Opening Sizes' },
            { id: 'lumpsum', label: 'Lump Sum Items' },
            { id: 'concrete', label: 'Concrete Mix Designs' },
            { id: 'steel', label: 'Steel Reinforcement' },
            { id: 'material', label: 'Material Consumption & Wastage' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveSubTab(tab.id as any)}
              className={`py-2 px-3 text-sm font-medium rounded-lg transition-colors ${
                activeSubTab === tab.id
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Sub-tab content */}
      <div className="pt-4">
        {/* Structural Assumptions */}
        {activeSubTab === 'structural' && (
          <div className="space-y-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-yellow-800">Critical Engineering Parameters</h4>
                  <p className="text-yellow-700 text-sm mt-1">
                    These parameters directly affect structural safety and must comply with IS codes for Seismic Zone IV (Delhi/NCR).
                    Changing these values will impact the structural design and cost estimates.
                  </p>
                </div>
              </div>
            </div>

            {/* Basic Structural Parameters */}
            <div>
              <h4 className="font-semibold text-gray-800 mb-3">Basic Structural Parameters</h4>
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parameter</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {structuralAssumptions.map((param, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {param.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <input
                            type="number"
                            value={param.value}
                            onChange={(e) => updateStructuralAssumption(index, 'value', Number(e.target.value))}
                            className="w-24 px-2 py-1 border border-gray-300 rounded-md"
                            step="0.1"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {param.unit}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {param.description}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Column & Beam Sizing */}
            <div>
              <h4 className="font-semibold text-gray-800 mb-3">Column & Beam Sizing</h4>
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Building Height</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Min. Column Size</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Min. Beam Width (mm)</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {columnBeamSizing.map((item, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {item.floors}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <input
                            type="text"
                            value={item.column_size}
                            onChange={(e) => updateColumnBeamSizing(index, 'column_size', e.target.value)}
                            className="w-32 px-2 py-1 border border-gray-300 rounded-md"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <input
                            type="number"
                            value={item.beam_width}
                            onChange={(e) => updateColumnBeamSizing(index, 'beam_width', Number(e.target.value))}
                            className="w-24 px-2 py-1 border border-gray-300 rounded-md"
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleSaveStructuralAssumptions}
                disabled={isSaving}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    Save Structural Parameters
                  </>
                )}
              </button>
            </div>
          </div>
        )}

        {/* Material Rates */}
        {activeSubTab === 'rates' && (
          <div className="space-y-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <Info className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-green-800">Material Rates Configuration</h4>
                  <p className="text-green-700 text-sm mt-1">
                    Configure material rates by category and quality tier. These rates directly affect cost calculations.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate (₹)</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quality</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {materialRates.map((rate, index) => (
                    <tr key={rate.id || index}>
                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                        {rate.category}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        {rate.name}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <input
                          type="number"
                          value={rate.rate}
                          onChange={(e) => {
                            const updated = [...materialRates];
                            updated[index] = { ...updated[index], rate: Number(e.target.value) };
                            setMaterialRates(updated);
                          }}
                          className="w-24 px-2 py-1 border border-gray-300 rounded-md"
                          step="0.01"
                        />
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        {rate.unit}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          rate.quality_tier === 'best' ? 'bg-green-100 text-green-800' :
                          rate.quality_tier === 'better' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {rate.quality_tier || 'standard'}
                        </span>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        {rate.location}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="flex justify-end">
              <button
                onClick={() => {
                  // Save material rates logic will be added
                  alert('Material rates saved successfully!');
                }}
                disabled={isSaving}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
              >
                <Save className="w-4 h-4" />
                Save Material Rates
              </button>
            </div>
          </div>
        )}

        {/* Calculation Factors */}
        {activeSubTab === 'factors' && (
          <div className="space-y-6">
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <Info className="w-5 h-5 text-purple-600 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-purple-800">Calculation Factors</h4>
                  <p className="text-purple-700 text-sm mt-1">
                    These factors control calculation logic and multipliers used throughout the estimation process.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Factor Name</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {calculationFactors.map((factor, index) => (
                    <tr key={factor.id || index}>
                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                        {factor.factor_name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <input
                          type="number"
                          value={factor.factor_value}
                          onChange={(e) => {
                            const updated = [...calculationFactors];
                            updated[index] = { ...updated[index], factor_value: Number(e.target.value) };
                            setCalculationFactors(updated);
                          }}
                          className="w-24 px-2 py-1 border border-gray-300 rounded-md"
                          step="0.0001"
                        />
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        {factor.unit || '-'}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                          {factor.category}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500 max-w-xs truncate">
                        {factor.description || 'Calculation factor'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="flex justify-end">
              <button
                onClick={() => {
                  // Save calculation factors logic will be added
                  alert('Calculation factors saved successfully!');
                }}
                disabled={isSaving}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
              >
                <Save className="w-4 h-4" />
                Save Calculation Factors
              </button>
            </div>
          </div>
        )}

        {/* Opening Sizes */}
        {activeSubTab === 'openings' && (
          <div className="space-y-6">
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <Info className="w-5 h-5 text-orange-600 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-orange-800">Opening Sizes (IS 1200:2012 Compliant)</h4>
                  <p className="text-orange-700 text-sm mt-1">
                    Standard opening dimensions for doors, windows, and ventilators as per IS codes.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Opening Type</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dimension</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {openingSizes.map((opening, index) => (
                    <tr key={opening.id || index}>
                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                        <span className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded-full text-xs">
                          {opening.opening_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        {opening.dimension_type.replace(/\b\w/g, l => l.toUpperCase())}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <input
                          type="number"
                          value={opening.value}
                          onChange={(e) => {
                            const updated = [...openingSizes];
                            updated[index] = { ...updated[index], value: Number(e.target.value) };
                            setOpeningSizes(updated);
                          }}
                          className="w-24 px-2 py-1 border border-gray-300 rounded-md"
                          step="0.1"
                        />
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        {opening.unit}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500 max-w-xs truncate">
                        {opening.description || 'Standard opening dimension'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="flex justify-end">
              <button
                onClick={() => {
                  // Save opening sizes logic will be added
                  alert('Opening sizes saved successfully!');
                }}
                disabled={isSaving}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
              >
                <Save className="w-4 h-4" />
                Save Opening Sizes
              </button>
            </div>
          </div>
        )}

        {/* Lump Sum Items */}
        {activeSubTab === 'lumpsum' && (
          <div className="space-y-6">
            <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <Info className="w-5 h-5 text-indigo-600 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-indigo-800">Lump Sum Items</h4>
                  <p className="text-indigo-700 text-sm mt-1">
                    Fixed-cost items that don't depend on quantity - approvals, connections, professional services, etc.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost (₹)</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sub Category</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Brand/Provider</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {lumpSumComponents.map((component, index) => (
                    <tr key={component.id || index}>
                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          component.category === 'Approvals' ? 'bg-red-100 text-red-800' :
                          component.category === 'Connections' ? 'bg-blue-100 text-blue-800' :
                          component.category === 'Professional' ? 'bg-green-100 text-green-800' :
                          component.category === 'Equipment' ? 'bg-purple-100 text-purple-800' :
                          component.category === 'Water Systems' ? 'bg-cyan-100 text-cyan-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {component.category}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900 max-w-xs">
                        {component.name}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <input
                          type="number"
                          value={component.unit_price}
                          onChange={(e) => {
                            const updated = [...lumpSumComponents];
                            updated[index] = { ...updated[index], unit_price: Number(e.target.value) };
                            setLumpSumComponents(updated);
                          }}
                          className="w-28 px-2 py-1 border border-gray-300 rounded-md"
                          step="1000"
                        />
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        {component.sub_category || '-'}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        {component.brand || 'Local Provider'}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500 max-w-xs truncate">
                        {component.specifications?.description || 'Fixed cost item'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="flex justify-end">
              <button
                onClick={() => {
                  // Save lump sum components logic will be added
                  alert('Lump sum items saved successfully!');
                }}
                disabled={isSaving}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
              >
                <Save className="w-4 h-4" />
                Save Lump Sum Items
              </button>
            </div>
          </div>
        )}

        {/* Concrete Mix Designs */}
        {activeSubTab === 'concrete' && (
          <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <Info className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-blue-800">Concrete Mix Designs</h4>
                  <p className="text-blue-700 text-sm mt-1">
                    These mix designs follow IS 10262:2019 and IS 456:2000 for different concrete grades.
                    The mix proportions directly affect concrete strength, durability, and cost.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <h4 className="font-semibold text-gray-800">Concrete Grade Specifications</h4>
              <button
                onClick={addConcreteMix}
                className="flex items-center gap-1 px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg text-sm transition-colors"
              >
                <Plus className="w-4 h-4" />
                Add Mix Design
              </button>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cement (bags/m³)</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sand (cft/m³)</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aggregate (cft/m³)</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">W/C Ratio</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {concreteMixes.map((mix, index) => (
                    <tr key={index}>
                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                        <input
                          type="text"
                          value={mix.grade}
                          onChange={(e) => updateConcreteMix(index, 'grade', e.target.value)}
                          className="w-16 px-2 py-1 border border-gray-300 rounded-md"
                        />
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <input
                          type="number"
                          value={mix.cement_bags_per_m3}
                          onChange={(e) => updateConcreteMix(index, 'cement_bags_per_m3', Number(e.target.value))}
                          className="w-20 px-2 py-1 border border-gray-300 rounded-md"
                          step="0.1"
                        />
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <input
                          type="number"
                          value={mix.sand_cft_per_m3}
                          onChange={(e) => updateConcreteMix(index, 'sand_cft_per_m3', Number(e.target.value))}
                          className="w-20 px-2 py-1 border border-gray-300 rounded-md"
                          step="0.1"
                        />
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <input
                          type="number"
                          value={mix.aggregate_cft_per_m3}
                          onChange={(e) => updateConcreteMix(index, 'aggregate_cft_per_m3', Number(e.target.value))}
                          className="w-20 px-2 py-1 border border-gray-300 rounded-md"
                          step="0.1"
                        />
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <input
                          type="number"
                          value={mix.water_cement_ratio}
                          onChange={(e) => updateConcreteMix(index, 'water_cement_ratio', Number(e.target.value))}
                          className="w-20 px-2 py-1 border border-gray-300 rounded-md"
                          step="0.01"
                          min="0.35"
                          max="0.65"
                        />
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                        <button
                          onClick={() => removeConcreteMix(index)}
                          className="text-red-500 hover:text-red-700 transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleSaveConcreteMixes}
                disabled={isSaving}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    Save Concrete Mix Designs
                  </>
                )}
              </button>
            </div>
          </div>
        )}

        {/* Steel Reinforcement */}
        {activeSubTab === 'steel' && (
          <div className="space-y-6">
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <AlertTriangle className="w-5 h-5 text-orange-600 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-orange-800">Seismic Zone IV Compliance</h4>
                  <p className="text-orange-700 text-sm mt-1">
                    These reinforcement ratios are increased to comply with IS 13920:2016 for Seismic Zone IV (Delhi/NCR).
                    Reducing these values may compromise structural safety during earthquakes.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <h4 className="font-semibold text-gray-800">Steel Reinforcement Ratios</h4>
              <button
                onClick={addSteelReinforcement}
                className="flex items-center gap-1 px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg text-sm transition-colors"
              >
                <Plus className="w-4 h-4" />
                Add Reinforcement Type
              </button>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Structural Element</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Steel Ratio (kg/m³)</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {steelReinforcement.map((item, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        <input
                          type="text"
                          value={item.structural_element}
                          onChange={(e) => updateSteelReinforcement(index, 'structural_element', e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 rounded-md"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <input
                          type="number"
                          value={item.steel_ratio_kg_per_m3}
                          onChange={(e) => updateSteelReinforcement(index, 'steel_ratio_kg_per_m3', Number(e.target.value))}
                          className="w-24 px-2 py-1 border border-gray-300 rounded-md"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button
                          onClick={() => removeSteelReinforcement(index)}
                          className="text-red-500 hover:text-red-700 transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleSaveSteelReinforcement}
                disabled={isSaving}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    Save Steel Reinforcement Ratios
                  </>
                )}
              </button>
            </div>
          </div>
        )}

        {/* Material Consumption & Wastage */}
        {activeSubTab === 'material' && (
          <div className="space-y-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <Info className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-green-800">Material Consumption & Wastage</h4>
                  <p className="text-green-700 text-sm mt-1">
                    These parameters determine material quantities and account for typical construction wastage.
                    Accurate values ensure realistic cost estimates and proper material procurement.
                  </p>
                </div>
              </div>
            </div>

            {/* Material Consumption */}
            <div>
              <h4 className="font-semibold text-gray-800 mb-3">Material Consumption Ratios</h4>
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parameter</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {materialConsumption.map((param, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {param.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <input
                            type="number"
                            value={param.value}
                            onChange={(e) => updateMaterialConsumption(index, 'value', Number(e.target.value))}
                            className="w-24 px-2 py-1 border border-gray-300 rounded-md"
                            step="0.1"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {param.unit}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {param.description}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Material Wastage */}
            <div>
              <div className="flex justify-between items-center mb-3">
                <h4 className="font-semibold text-gray-800">Material Wastage Factors</h4>
                <button
                  onClick={addMaterialWastage}
                  className="flex items-center gap-1 px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg text-sm transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  Add Material
                </button>
              </div>
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Material</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Wastage (%)</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {materialWastage.map((item, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          <input
                            type="text"
                            value={item.material}
                            onChange={(e) => updateMaterialWastage(index, 'material', e.target.value)}
                            className="w-full px-2 py-1 border border-gray-300 rounded-md"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <input
                            type="number"
                            value={item.wastage_percentage}
                            onChange={(e) => updateMaterialWastage(index, 'wastage_percentage', Number(e.target.value))}
                            className="w-24 px-2 py-1 border border-gray-300 rounded-md"
                            step="0.1"
                            min="0"
                            max="20"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <button
                            onClick={() => removeMaterialWastage(index)}
                            className="text-red-500 hover:text-red-700 transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleSaveMaterialConsumption}
                disabled={isSaving}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    Save Material Parameters
                  </>
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}