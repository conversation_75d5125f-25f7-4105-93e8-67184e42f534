export interface ConcreteMix {
  grade: string;
  cementBags_per_m3: number;
  sand_cft_per_m3: number;
  aggregate_cft_per_m3: number;
  compressiveStrength_mpa: number;
  applications: string[];
  waterCementRatio: number;
}

export const concreteMixes: Record<string, ConcreteMix> = {
  M20: {
    grade: 'M20',
    cementBags_per_m3: 7.29,
    sand_cft_per_m3: 16.2,
    aggregate_cft_per_m3: 32.4,
    compressiveStrength_mpa: 20,
    applications: ['Non-structural elements', 'Plain concrete'],
    waterCementRatio: 0.55
  },
  M25: {
    grade: 'M25',
    cementBags_per_m3: 8.13,
    sand_cft_per_m3: 15.4,
    aggregate_cft_per_m3: 30.8,
    compressiveStrength_mpa: 25,
    applications: ['Residential RCC', 'Standard construction'],
    waterCementRatio: 0.50
  },
  M30: {
    grade: 'M30',
    cementBags_per_m3: 9.50,
    sand_cft_per_m3: 14.0,
    aggregate_cft_per_m3: 28.0,
    compressiveStrength_mpa: 30,
    applications: ['High-rise buildings', 'Heavy load structures'],
    waterCementRatio: 0.45
  },
  M35: {
    grade: 'M35',
    cementBags_per_m3: 10.67,
    sand_cft_per_m3: 13.2,
    aggregate_cft_per_m3: 26.4,
    compressiveStrength_mpa: 35,
    applications: ['Commercial buildings', 'Industrial structures'],
    waterCementRatio: 0.42
  }
};

// Steel reinforcement ratios for different structural elements (kg per m³ of concrete)
export const steelReinforcement = {
  foundations: {
    isolated_footings: 80,
    strip_footings: 100,
    raft_foundation: 120,
    pile_caps: 150
  },
  columns: {
    ground_floor: 180,
    typical_floor: 160,
    top_floor: 140,
    basement: 200
  },
  beams: {
    main_beams: 140,
    secondary_beams: 120,
    lintels: 100
  },
  slabs: {
    one_way_slab: 80,
    two_way_slab: 90,
    flat_slab: 110,
    cantilever: 120
  }
};