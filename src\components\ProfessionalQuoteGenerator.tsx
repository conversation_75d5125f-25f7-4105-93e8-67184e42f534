import React, { useState, useEffect } from 'react';
import { FileText, Calendar, TrendingUp, AlertTriangle, CheckCircle, Download } from 'lucide-react';
import { CalculationResult, UserInputs } from '../types/calculator';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface ProfessionalQuoteGeneratorProps {
  result: CalculationResult;
  inputs: UserInputs;
  isOpen: boolean;
  onClose: () => void;
}

export function ProfessionalQuoteGenerator({ result, inputs, isOpen, onClose }: ProfessionalQuoteGeneratorProps) {
  const [quoteSettings, setQuoteSettings] = useState({
    projectDuration: 12, // months
    escalationClause: true,
    warrantyPeriod: 12, // months
    paymentTerms: 'progressive',
    contingencyPercentage: 8,
    profitMargin: 15
  });
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [finalQuoteAmount, setFinalQuoteAmount] = useState(0);

  // Define helper functions before useEffect
  const calculateEscalatedCost = () => {
    const monthlyEscalation = 0.025; // 2.5% monthly average
    const escalationFactor = Math.pow(1 + monthlyEscalation, quoteSettings.projectDuration);
    return result.totalCost * escalationFactor;
  };

  const calculateFinalQuote = () => {
    const baseCost = result.totalCost;
    const escalatedCost = quoteSettings.escalationClause ? calculateEscalatedCost() : baseCost;
    const contingency = escalatedCost * (quoteSettings.contingencyPercentage / 100);
    const profit = (escalatedCost + contingency) * (quoteSettings.profitMargin / 100);
    return escalatedCost + contingency + profit;
  };

  // Calculate final quote amount whenever settings or result changes
  useEffect(() => {
    setFinalQuoteAmount(calculateFinalQuote());
  }, [quoteSettings, result.totalCost]);

  if (!isOpen) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const paymentSchedule = [
    { milestone: 'Advance Payment', percentage: 10, description: 'Site mobilization and material procurement' },
    { milestone: 'Foundation Complete', percentage: 20, description: 'Excavation, foundation, and plinth level' },
    { milestone: 'Structure Complete', percentage: 25, description: 'RCC work, columns, beams, and slabs' },
    { milestone: 'Masonry & Roofing', percentage: 20, description: 'Brick work, plastering, and roof completion' },
    { milestone: 'Finishing 50%', percentage: 15, description: 'Flooring, electrical, and plumbing rough-in' },
    { milestone: 'Final Completion', percentage: 10, description: 'Painting, fixtures, and handover' }
  ];

  const generatePDF = async () => {
    setIsGeneratingPDF(true);
    
    try {
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      let yPosition = margin;

      // Helper function to add text with word wrap
      const addText = (text: string, x: number, y: number, options: any = {}) => {
        const fontSize = options.fontSize || 10;
        const maxWidth = options.maxWidth || pageWidth - 2 * margin;
        const lineHeight = options.lineHeight || fontSize * 0.35;
        
        pdf.setFontSize(fontSize);
        if (options.bold) pdf.setFont(undefined, 'bold');
        else pdf.setFont(undefined, 'normal');
        
        const lines = pdf.splitTextToSize(text, maxWidth);
        lines.forEach((line: string, index: number) => {
          pdf.text(line, x, y + (index * lineHeight));
        });
        
        return y + (lines.length * lineHeight);
      };

      // Header with NirmaanAI branding
      pdf.setFillColor(59, 130, 246); // Blue color
      pdf.rect(0, 0, pageWidth, 40, 'F');
      
      pdf.setTextColor(255, 255, 255);
      yPosition = addText('NirmaanAI', margin, 25, { fontSize: 24, bold: true });
      addText('Professional Construction Cost Calculator', margin, 32, { fontSize: 12 });
      
      // Reset text color
      pdf.setTextColor(0, 0, 0);
      yPosition = 60;

      // Quote title and date
      yPosition = addText('PROFESSIONAL CONSTRUCTION QUOTE', margin, yPosition, { fontSize: 18, bold: true });
      yPosition += 10;
      yPosition = addText(`Generated on: ${new Date().toLocaleDateString('en-IN')}`, margin, yPosition, { fontSize: 10 });
      yPosition += 15;

      // Project overview
      yPosition = addText('PROJECT OVERVIEW', margin, yPosition, { fontSize: 14, bold: true });
      yPosition += 5;
      yPosition = addText(`Plot Size: ${inputs.plotSize.toLocaleString()} sq ft`, margin, yPosition);
      yPosition = addText(`Built-up Area: ${result.quantities.totalBuiltUpArea.toLocaleString()} sq ft`, margin, yPosition);
      yPosition = addText(`Number of Floors: ${inputs.numberOfFloors}`, margin, yPosition);
      yPosition = addText(`Location: ${inputs.location}`, margin, yPosition);
      yPosition += 10;

      // Cost breakdown
      yPosition = addText('COST BREAKDOWN', margin, yPosition, { fontSize: 14, bold: true });
      yPosition += 5;

      result.sections.forEach(section => {
        yPosition = addText(`${section.title}: ${formatCurrency(section.subtotal)}`, margin, yPosition);
      });
      yPosition += 5;

      // Final quote calculation
      yPosition = addText('QUOTE CALCULATION', margin, yPosition, { fontSize: 14, bold: true });
      yPosition += 5;
      yPosition = addText(`Base Construction Cost: ${formatCurrency(result.totalCost)}`, margin, yPosition);
      
      if (quoteSettings.escalationClause) {
        yPosition = addText(`Escalation (${quoteSettings.projectDuration} months): ${formatCurrency(calculateEscalatedCost() - result.totalCost)}`, margin, yPosition);
      }
      
      yPosition = addText(`Contingency (${quoteSettings.contingencyPercentage}%): ${formatCurrency((quoteSettings.escalationClause ? calculateEscalatedCost() : result.totalCost) * (quoteSettings.contingencyPercentage / 100))}`, margin, yPosition);
      yPosition = addText(`Contractor Margin (${quoteSettings.profitMargin}%): ${formatCurrency(((quoteSettings.escalationClause ? calculateEscalatedCost() : result.totalCost) * (1 + quoteSettings.contingencyPercentage / 100)) * (quoteSettings.profitMargin / 100))}`, margin, yPosition);
      yPosition += 5;

      // Total quote amount (highlighted)
      pdf.setFillColor(59, 130, 246);
      pdf.rect(margin - 5, yPosition - 5, pageWidth - 2 * margin + 10, 15, 'F');
      pdf.setTextColor(255, 255, 255);
      yPosition = addText(`TOTAL QUOTE AMOUNT: ${formatCurrency(finalQuoteAmount)}`, margin, yPosition + 5, { fontSize: 14, bold: true });
      pdf.setTextColor(0, 0, 0);
      yPosition += 15;

      // Payment schedule
      yPosition = addText('PAYMENT SCHEDULE', margin, yPosition, { fontSize: 14, bold: true });
      yPosition += 5;

      paymentSchedule.forEach(payment => {
        const amount = finalQuoteAmount * (payment.percentage / 100);
        yPosition = addText(`${payment.milestone} (${payment.percentage}%): ${formatCurrency(amount)}`, margin, yPosition);
        yPosition = addText(`  ${payment.description}`, margin + 5, yPosition, { fontSize: 9 });
      });
      yPosition += 10;

      // Terms and conditions
      yPosition = addText('TERMS & CONDITIONS', margin, yPosition, { fontSize: 14, bold: true });
      yPosition += 5;

      const terms = [
        'All materials as per specifications mentioned in the quote',
        'Labor and supervision included for all mentioned work',
        'Basic electrical and plumbing work as per standard practices',
        `Structural warranty: ${quoteSettings.warrantyPeriod} months from completion`,
        'Quote valid for 30 days from date of issue',
        'Payment as per agreed schedule',
        'Any additional work will be charged separately',
        'Material escalation beyond agreed percentage will be extra'
      ];

      terms.forEach(term => {
        yPosition = addText(`• ${term}`, margin, yPosition, { fontSize: 9 });
      });

      // Footer
      yPosition = pageHeight - 30;
      pdf.setFillColor(240, 240, 240);
      pdf.rect(0, yPosition - 5, pageWidth, 25, 'F');
      addText('This quote is generated by NirmaanAI Professional Construction Cost Calculator', margin, yPosition, { fontSize: 8 });
      addText('For any queries, please contact our support team', margin, yPosition + 5, { fontSize: 8 });

      // Save the PDF
      pdf.save(`NirmaanAI_Quote_${new Date().toISOString().split('T')[0]}.pdf`);
      
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Professional Construction Quote</h2>
            <p className="text-gray-600 mt-1">Comprehensive estimate for client presentation</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <span className="sr-only">Close</span>
            ×
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Quote Settings */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="font-semibold text-blue-800 mb-3 flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Project Timeline
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Duration (months)
                  </label>
                  <input
                    type="number"
                    value={quoteSettings.projectDuration}
                    onChange={(e) => setQuoteSettings(prev => ({ ...prev, projectDuration: Number(e.target.value) }))}
                    className="form-input"
                    min="6"
                    max="24"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="escalation"
                    checked={quoteSettings.escalationClause}
                    onChange={(e) => setQuoteSettings(prev => ({ ...prev, escalationClause: e.target.checked }))}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="escalation" className="text-sm text-gray-700">
                    Include escalation clause
                  </label>
                </div>
              </div>
            </div>

            <div className="bg-green-50 rounded-lg p-4">
              <h3 className="font-semibold text-green-800 mb-3 flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Financial Terms
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Contingency (%)
                  </label>
                  <input
                    type="number"
                    value={quoteSettings.contingencyPercentage}
                    onChange={(e) => setQuoteSettings(prev => ({ ...prev, contingencyPercentage: Number(e.target.value) }))}
                    className="form-input"
                    min="5"
                    max="15"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Profit Margin (%)
                  </label>
                  <input
                    type="number"
                    value={quoteSettings.profitMargin}
                    onChange={(e) => setQuoteSettings(prev => ({ ...prev, profitMargin: Number(e.target.value) }))}
                    className="form-input"
                    min="10"
                    max="25"
                  />
                </div>
              </div>
            </div>

            <div className="bg-orange-50 rounded-lg p-4">
              <h3 className="font-semibold text-orange-800 mb-3 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Risk Factors
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>Material price volatility covered</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>Labor escalation included</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>Regulatory compliance ensured</span>
                </div>
              </div>
            </div>
          </div>

          {/* Cost Breakdown */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Cost Analysis */}
            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Cost Analysis</h3>
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-600">Base Construction Cost</span>
                    <span className="font-semibold">{formatCurrency(result.totalCost)}</span>
                  </div>
                  {quoteSettings.escalationClause && (
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-600">Escalation ({quoteSettings.projectDuration} months)</span>
                      <span className="font-semibold text-orange-600">
                        {formatCurrency(calculateEscalatedCost() - result.totalCost)}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-600">Contingency ({quoteSettings.contingencyPercentage}%)</span>
                    <span className="font-semibold text-blue-600">
                      {formatCurrency((quoteSettings.escalationClause ? calculateEscalatedCost() : result.totalCost) * (quoteSettings.contingencyPercentage / 100))}
                    </span>
                  </div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-600">Contractor Margin ({quoteSettings.profitMargin}%)</span>
                    <span className="font-semibold text-green-600">
                      {formatCurrency(((quoteSettings.escalationClause ? calculateEscalatedCost() : result.totalCost) * (1 + quoteSettings.contingencyPercentage / 100)) * (quoteSettings.profitMargin / 100))}
                    </span>
                  </div>
                  <div className="border-t border-gray-300 pt-2 mt-2">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-gray-800">Total Quote Amount</span>
                      <span className="text-2xl font-bold text-blue-600">
                        {formatCurrency(finalQuoteAmount)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Rate per sq ft */}
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-800">
                      {formatCurrency(finalQuoteAmount / result.quantities.totalBuiltUpArea)}
                    </div>
                    <div className="text-sm text-blue-600">Per sq ft (Built-up Area)</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Payment Schedule */}
            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Payment Schedule</h3>
              <div className="space-y-3">
                {paymentSchedule.map((payment, index) => {
                  const amount = finalQuoteAmount * (payment.percentage / 100);
                  return (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <div className="font-semibold text-gray-800">{payment.milestone}</div>
                          <div className="text-sm text-gray-600">{payment.description}</div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-blue-600">{payment.percentage}%</div>
                          <div className="text-sm text-gray-600">{formatCurrency(amount)}</div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Terms & Conditions */}
          <div className="mt-8 bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Terms & Conditions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">Inclusions</h4>
                <ul className="space-y-1">
                  <li>• All materials as per specifications</li>
                  <li>• Labor and supervision</li>
                  <li>• Basic electrical and plumbing</li>
                  <li>• Structural warranty: {quoteSettings.warrantyPeriod} months</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">Exclusions</h4>
                <ul className="space-y-1">
                  <li>• Furniture and furnishing</li>
                  <li>• Landscaping and external works</li>
                  <li>• Additional electrical appliances</li>
                  <li>• Cost escalation beyond agreed percentage</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            Quote valid for 30 days from date of issue
          </div>
          <div className="flex gap-3">
            <button
              onClick={generatePDF}
              disabled={isGeneratingPDF}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
            >
              <Download className="w-4 h-4" />
              {isGeneratingPDF ? 'Generating PDF...' : 'Generate PDF Quote'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}