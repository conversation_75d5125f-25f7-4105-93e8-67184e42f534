/**
 * ADMIN PANEL: CONFIGURABLE PARAMETERS INTERFACE
 * 
 * This component shows where the 15+ critical parameters should be configured
 * in the admin panel for complete control over calculations.
 */

import React, { useState, useEffect } from 'react';
import { Settings, Building, Calculator, Ruler, DollarSign } from 'lucide-react';

interface ConfigurableParameter {
  id: string;
  name: string;
  value: number;
  unit: string;
  category: string;
  description: string;
  min?: number;
  max?: number;
  step?: number;
}

export function ConfigurableParameters() {
  const [parameters, setParameters] = useState<ConfigurableParameter[]>([]);
  const [activeCategory, setActiveCategory] = useState('structural');

  // These are the 15+ critical parameters that should be admin configurable
  const defaultParameters: ConfigurableParameter[] = [
    // STRUCTURAL PARAMETERS
    {
      id: 'floor_height',
      name: 'Floor Height',
      value: 3.0,
      unit: 'meters',
      category: 'structural',
      description: 'Standard floor height for all calculations',
      min: 2.8,
      max: 3.5,
      step: 0.1
    },
    {
      id: 'beam_width',
      name: '<PERSON>am Width',
      value: 300,
      unit: 'mm',
      category: 'structural',
      description: 'Standard beam width for structural calculations',
      min: 250,
      max: 400,
      step: 25
    },
    {
      id: 'beam_depth',
      name: 'Beam Depth',
      value: 450,
      unit: 'mm',
      category: 'structural',
      description: 'Standard beam depth for structural calculations',
      min: 350,
      max: 600,
      step: 25
    },
    {
      id: 'column_size_low_rise',
      name: 'Column Size - Low Rise (≤2 floors)',
      value: 350,
      unit: 'mm',
      category: 'structural',
      description: 'Column size for buildings with 2 floors or less',
      min: 300,
      max: 450,
      step: 25
    },
    {
      id: 'column_size_mid_rise',
      name: 'Column Size - Mid Rise (3-4 floors)',
      value: 400,
      unit: 'mm',
      category: 'structural',
      description: 'Column size for buildings with 3-4 floors',
      min: 350,
      max: 500,
      step: 25
    },
    {
      id: 'column_size_high_rise',
      name: 'Column Size - High Rise (5+ floors)',
      value: 450,
      unit: 'mm',
      category: 'structural',
      description: 'Column size for buildings with 5+ floors',
      min: 400,
      max: 600,
      step: 25
    },

    // MATERIAL RATES
    {
      id: 'excavation_rate',
      name: 'Excavation Rate',
      value: 450,
      unit: '₹/m³',
      category: 'rates',
      description: 'Base rate for manual excavation work',
      min: 300,
      max: 800,
      step: 25
    },
    {
      id: 'plaster_rate',
      name: 'Plaster Rate',
      value: 85,
      unit: '₹/sqm',
      category: 'rates',
      description: 'Base rate for cement plaster 12mm thick',
      min: 60,
      max: 150,
      step: 5
    },
    {
      id: 'electrical_rate',
      name: 'Electrical Point Rate',
      value: 350,
      unit: '₹/point',
      category: 'rates',
      description: 'Rate per electrical point installation',
      min: 250,
      max: 600,
      step: 25
    },
    {
      id: 'plumbing_rate',
      name: 'Plumbing Point Rate',
      value: 500,
      unit: '₹/point',
      category: 'rates',
      description: 'Rate per plumbing point installation',
      min: 350,
      max: 800,
      step: 25
    },
    {
      id: 'waterproofing_good',
      name: 'Waterproofing - Good Quality',
      value: 60,
      unit: '₹/sqm',
      category: 'rates',
      description: 'Waterproofing rate for good quality tier',
      min: 40,
      max: 100,
      step: 5
    },
    {
      id: 'waterproofing_better',
      name: 'Waterproofing - Better Quality',
      value: 85,
      unit: '₹/sqm',
      category: 'rates',
      description: 'Waterproofing rate for better quality tier',
      min: 60,
      max: 120,
      step: 5
    },
    {
      id: 'waterproofing_best',
      name: 'Waterproofing - Best Quality',
      value: 120,
      unit: '₹/sqm',
      category: 'rates',
      description: 'Waterproofing rate for best quality tier',
      min: 80,
      max: 200,
      step: 10
    },

    // CALCULATION FACTORS
    {
      id: 'plaster_coverage_factor',
      name: 'Plaster Coverage Factor',
      value: 3.0,
      unit: 'multiplier',
      category: 'factors',
      description: 'Multiplier for internal plaster area calculation',
      min: 2.5,
      max: 4.0,
      step: 0.1
    },
    {
      id: 'door_density',
      name: 'Door Density',
      value: 200,
      unit: 'sqm/door',
      category: 'factors',
      description: 'Square meters of built-up area per door',
      min: 150,
      max: 300,
      step: 25
    },
    {
      id: 'window_density',
      name: 'Window Density',
      value: 150,
      unit: 'sqm/window',
      category: 'factors',
      description: 'Square meters of built-up area per window',
      min: 100,
      max: 250,
      step: 25
    },

    // OPENING SIZES (IS 1200:2012 COMPLIANT)
    {
      id: 'door_height',
      name: 'Door Height',
      value: 2.1,
      unit: 'meters',
      category: 'openings',
      description: 'Standard door height (IS 1200:2012)',
      min: 2.0,
      max: 2.4,
      step: 0.1
    },
    {
      id: 'door_width',
      name: 'Door Width',
      value: 1.0,
      unit: 'meters',
      category: 'openings',
      description: 'Standard door width (IS 1200:2012)',
      min: 0.8,
      max: 1.2,
      step: 0.1
    },
    {
      id: 'window_height',
      name: 'Window Height',
      value: 1.5,
      unit: 'meters',
      category: 'openings',
      description: 'Standard window height (IS 1200:2012)',
      min: 1.2,
      max: 2.0,
      step: 0.1
    },
    {
      id: 'window_width',
      name: 'Window Width',
      value: 1.2,
      unit: 'meters',
      category: 'openings',
      description: 'Standard window width (IS 1200:2012)',
      min: 0.9,
      max: 1.8,
      step: 0.1
    }
  ];

  useEffect(() => {
    // Fetch parameters from Supabase
    loadParameters();
  }, []);

  const loadParameters = async () => {
    try {
      // In a real implementation, this would fetch from the new Supabase tables
      // For now, using default values
      setParameters(defaultParameters);

      // TODO: Implement actual Supabase fetching
      // const materialRates = await materialRatesAPI.getAll();
      // const calculationFactors = await calculationFactorsAPI.getAll();
      // const openingSizes = await openingSizesAPI.getAll();
      // Process and set parameters from database
    } catch (error) {
      console.error('Error loading parameters:', error);
      setParameters(defaultParameters);
    }
  };

  const categories = [
    { id: 'structural', name: 'Structural Parameters', icon: Building, color: 'blue' },
    { id: 'rates', name: 'Material Rates', icon: DollarSign, color: 'green' },
    { id: 'factors', name: 'Calculation Factors', icon: Calculator, color: 'purple' },
    { id: 'openings', name: 'Opening Sizes', icon: Ruler, color: 'orange' }
  ];

  const filteredParameters = parameters.filter(p => p.category === activeCategory);

  const handleParameterChange = (id: string, value: number) => {
    setParameters(prev => prev.map(p => 
      p.id === id ? { ...p, value } : p
    ));
    // In real implementation, this would save to Supabase
    console.log(`Updated ${id} to ${value}`);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center gap-3 mb-6">
        <Settings className="w-6 h-6 text-blue-600" />
        <h2 className="text-2xl font-bold text-gray-800">Configurable Parameters</h2>
        <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
          {parameters.length} Parameters
        </span>
      </div>

      {/* Category Tabs */}
      <div className="flex flex-wrap gap-2 mb-6">
        {categories.map(category => {
          const Icon = category.icon;
          const isActive = activeCategory === category.id;
          const count = parameters.filter(p => p.category === category.id).length;
          
          return (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                isActive
                  ? `bg-${category.color}-100 text-${category.color}-800 border-2 border-${category.color}-200`
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <Icon className="w-4 h-4" />
              {category.name}
              <span className="bg-white px-2 py-0.5 rounded-full text-xs">
                {count}
              </span>
            </button>
          );
        })}
      </div>

      {/* Parameters List */}
      <div className="space-y-4">
        {filteredParameters.map(parameter => (
          <div key={parameter.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h3 className="font-semibold text-gray-800">{parameter.name}</h3>
                <p className="text-sm text-gray-600">{parameter.description}</p>
              </div>
              <div className="text-right">
                <div className="flex items-center gap-2">
                  <input
                    type="number"
                    value={parameter.value}
                    onChange={(e) => handleParameterChange(parameter.id, parseFloat(e.target.value))}
                    min={parameter.min}
                    max={parameter.max}
                    step={parameter.step}
                    className="w-24 px-3 py-1 border border-gray-300 rounded text-right"
                  />
                  <span className="text-sm text-gray-500 min-w-[60px]">{parameter.unit}</span>
                </div>
                {parameter.min && parameter.max && (
                  <div className="text-xs text-gray-400 mt-1">
                    Range: {parameter.min} - {parameter.max}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-semibold text-blue-800 mb-2">💡 Implementation Status</h4>
        <p className="text-blue-700 text-sm">
          These parameters are currently using fallback values in the code. To make them fully configurable:
        </p>
        <ol className="text-blue-700 text-sm mt-2 ml-4 list-decimal">
          <li>Add database tables for each parameter category</li>
          <li>Create admin interface forms (like this mockup)</li>
          <li>Update API to fetch these values</li>
          <li>Test that calculations use admin panel values</li>
        </ol>
      </div>
    </div>
  );
}
