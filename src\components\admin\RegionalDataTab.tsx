import React, { useState, useEffect } from 'react';
import { Save, Map, DollarSign, Building2, Plus, Trash2, Edit, AlertTriangle } from 'lucide-react';
import { supabase } from '../../lib/supabase';

interface LocationMultiplier {
  id?: string;
  location: string;
  material_multiplier: number;
  labor_multiplier: number;
  notes?: string;
}

interface ProfessionalFee {
  id?: string;
  service: string;
  tier1_percentage: number;
  tier2_percentage: number;
  tier3_percentage: number;
}

interface RegulatoryFee {
  id?: string;
  fee_name: string;
  authority: string;
  rate: number;
  unit: string;
}

export function RegionalDataTab() {
  const [activeSubTab, setActiveSubTab] = useState<'locations' | 'fees'>('locations');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  
  // Location Multipliers
  const [locationMultipliers, setLocationMultipliers] = useState<LocationMultiplier[]>([
    { location: 'Delhi', material_multiplier: 1.0, labor_multiplier: 1.0, notes: 'Baseline' },
    { location: 'Gurgaon', material_multiplier: 1.15, labor_multiplier: 1.30, notes: 'Higher transportation and premium labor market' },
    { location: 'Noida', material_multiplier: 1.05, labor_multiplier: 1.10, notes: 'Competitive but slightly higher than Delhi' },
    { location: 'Ghaziabad', material_multiplier: 0.95, labor_multiplier: 0.90, notes: 'Lower material and labor costs' }
  ]);
  
  // Professional Fees
  const [professionalFees, setProfessionalFees] = useState<ProfessionalFee[]>([
    { service: 'Architect (Design + Supervision)', tier1_percentage: 4.0, tier2_percentage: 6.0, tier3_percentage: 8.0 },
    { service: 'Structural Engineer', tier1_percentage: 1.5, tier2_percentage: 2.0, tier3_percentage: 2.5 },
    { service: 'MEP Consultant', tier1_percentage: 1.0, tier2_percentage: 1.5, tier3_percentage: 2.0 }
  ]);
  
  // Regulatory Fees
  const [regulatoryFees, setRegulatoryFees] = useState<RegulatoryFee[]>([
    { fee_name: 'Plan Approval', authority: 'MCD', rate: 550, unit: 'per sqm' },
    { fee_name: 'Fire NOC', authority: 'Fire Dept.', rate: 20000, unit: 'Lump Sum' },
    { fee_name: 'Environment Clearance', authority: 'MoEF', rate: 15000, unit: 'Lump Sum' }
  ]);

  // Load data from database
  useEffect(() => {
    loadRegionalData();
  }, [activeSubTab]);

  const loadRegionalData = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, this would fetch from the database
      // For now, we'll use the default values
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // In a real implementation, we would update state with fetched data
      // setLocationMultipliers(data.locations);
      // setProfessionalFees(data.professionalFees);
      // setRegulatoryFees(data.regulatoryFees);
    } catch (error) {
      console.error('Error loading regional data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveLocationMultipliers = async () => {
    setIsSaving(true);
    try {
      // In a real implementation, this would save to the database
      console.log('Saving location multipliers:', locationMultipliers);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Location multipliers saved successfully!');
    } catch (error) {
      console.error('Error saving location multipliers:', error);
      alert('Error saving location multipliers');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveFees = async () => {
    setIsSaving(true);
    try {
      // In a real implementation, this would save to the database
      console.log('Saving professional fees:', professionalFees);
      console.log('Saving regulatory fees:', regulatoryFees);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Fees saved successfully!');
    } catch (error) {
      console.error('Error saving fees:', error);
      alert('Error saving fees');
    } finally {
      setIsSaving(false);
    }
  };

  // Helper functions for updating state
  const updateLocationMultiplier = (index: number, field: keyof LocationMultiplier, value: any) => {
    const updated = [...locationMultipliers];
    updated[index] = { ...updated[index], [field]: value };
    setLocationMultipliers(updated);
  };

  const addLocationMultiplier = () => {
    setLocationMultipliers([...locationMultipliers, { 
      location: '', 
      material_multiplier: 1.0, 
      labor_multiplier: 1.0 
    }]);
  };

  const removeLocationMultiplier = (index: number) => {
    setLocationMultipliers(locationMultipliers.filter((_, i) => i !== index));
  };

  const updateProfessionalFee = (index: number, field: keyof ProfessionalFee, value: any) => {
    const updated = [...professionalFees];
    updated[index] = { ...updated[index], [field]: value };
    setProfessionalFees(updated);
  };

  const addProfessionalFee = () => {
    setProfessionalFees([...professionalFees, { 
      service: '', 
      tier1_percentage: 0, 
      tier2_percentage: 0, 
      tier3_percentage: 0 
    }]);
  };

  const removeProfessionalFee = (index: number) => {
    setProfessionalFees(professionalFees.filter((_, i) => i !== index));
  };

  const updateRegulatoryFee = (index: number, field: keyof RegulatoryFee, value: any) => {
    const updated = [...regulatoryFees];
    updated[index] = { ...updated[index], [field]: value };
    setRegulatoryFees(updated);
  };

  const addRegulatoryFee = () => {
    setRegulatoryFees([...regulatoryFees, { 
      fee_name: '', 
      authority: '', 
      rate: 0, 
      unit: 'per sqm' 
    }]);
  };

  const removeRegulatoryFee = (index: number) => {
    setRegulatoryFees(regulatoryFees.filter((_, i) => i !== index));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">Regional Data</h3>
        <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 px-3 py-1 rounded-lg">
          <Map className="w-4 h-4" />
          <span>Location-Specific Parameters</span>
        </div>
      </div>

      {/* Sub-tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-4">
          {[
            { id: 'locations', label: 'Location Multipliers' },
            { id: 'fees', label: 'Professional & Regulatory Fees' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveSubTab(tab.id as any)}
              className={`py-2 px-3 text-sm font-medium rounded-lg transition-colors ${
                activeSubTab === tab.id
                  ? 'bg-green-100 text-green-700'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Sub-tab content */}
      <div className="pt-4">
        {/* Location Multipliers */}
        {activeSubTab === 'locations' && (
          <div className="space-y-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-yellow-800">Location Cost Variations</h4>
                  <p className="text-yellow-700 text-sm mt-1">
                    Material and labor costs vary significantly across the Delhi/NCR region.
                    These multipliers adjust the base costs to reflect location-specific market conditions.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <h4 className="font-semibold text-gray-800">Location Cost Multipliers</h4>
              <button
                onClick={addLocationMultiplier}
                className="flex items-center gap-1 px-3 py-1 bg-green-100 hover:bg-green-200 text-green-700 rounded-lg text-sm transition-colors"
              >
                <Plus className="w-4 h-4" />
                Add Location
              </button>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Material Multiplier</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Labor Multiplier</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {locationMultipliers.map((location, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        <input
                          type="text"
                          value={location.location}
                          onChange={(e) => updateLocationMultiplier(index, 'location', e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 rounded-md"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <input
                          type="number"
                          value={location.material_multiplier}
                          onChange={(e) => updateLocationMultiplier(index, 'material_multiplier', Number(e.target.value))}
                          className="w-24 px-2 py-1 border border-gray-300 rounded-md"
                          step="0.01"
                          min="0.5"
                          max="2.0"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <input
                          type="number"
                          value={location.labor_multiplier}
                          onChange={(e) => updateLocationMultiplier(index, 'labor_multiplier', Number(e.target.value))}
                          className="w-24 px-2 py-1 border border-gray-300 rounded-md"
                          step="0.01"
                          min="0.5"
                          max="2.0"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <input
                          type="text"
                          value={location.notes || ''}
                          onChange={(e) => updateLocationMultiplier(index, 'notes', e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 rounded-md"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button
                          onClick={() => removeLocationMultiplier(index)}
                          className="text-red-500 hover:text-red-700 transition-colors"
                          disabled={index < 4} // Prevent removing the default locations
                        >
                          <Trash2 className={`w-4 h-4 ${index < 4 ? 'opacity-30' : ''}`} />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleSaveLocationMultipliers}
                disabled={isSaving}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white rounded-lg font-medium transition-colors"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    Save Location Multipliers
                  </>
                )}
              </button>
            </div>
          </div>
        )}

        {/* Professional & Regulatory Fees */}
        {activeSubTab === 'fees' && (
          <div className="space-y-6">
            {/* Professional Fees */}
            <div>
              <div className="flex justify-between items-center mb-3">
                <h4 className="font-semibold text-gray-800">Professional Fees</h4>
                <button
                  onClick={addProfessionalFee}
                  className="flex items-center gap-1 px-3 py-1 bg-green-100 hover:bg-green-200 text-green-700 rounded-lg text-sm transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  Add Service
                </button>
              </div>
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tier 1 (%)</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tier 2 (%)</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tier 3 (%)</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {professionalFees.map((fee, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          <input
                            type="text"
                            value={fee.service}
                            onChange={(e) => updateProfessionalFee(index, 'service', e.target.value)}
                            className="w-full px-2 py-1 border border-gray-300 rounded-md"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <input
                            type="number"
                            value={fee.tier1_percentage}
                            onChange={(e) => updateProfessionalFee(index, 'tier1_percentage', Number(e.target.value))}
                            className="w-20 px-2 py-1 border border-gray-300 rounded-md"
                            step="0.1"
                            min="0"
                            max="20"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <input
                            type="number"
                            value={fee.tier2_percentage}
                            onChange={(e) => updateProfessionalFee(index, 'tier2_percentage', Number(e.target.value))}
                            className="w-20 px-2 py-1 border border-gray-300 rounded-md"
                            step="0.1"
                            min="0"
                            max="20"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <input
                            type="number"
                            value={fee.tier3_percentage}
                            onChange={(e) => updateProfessionalFee(index, 'tier3_percentage', Number(e.target.value))}
                            className="w-20 px-2 py-1 border border-gray-300 rounded-md"
                            step="0.1"
                            min="0"
                            max="20"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <button
                            onClick={() => removeProfessionalFee(index)}
                            className="text-red-500 hover:text-red-700 transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Regulatory Fees */}
            <div>
              <div className="flex justify-between items-center mb-3">
                <h4 className="font-semibold text-gray-800">Regulatory Fees</h4>
                <button
                  onClick={addRegulatoryFee}
                  className="flex items-center gap-1 px-3 py-1 bg-green-100 hover:bg-green-200 text-green-700 rounded-lg text-sm transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  Add Fee
                </button>
              </div>
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fee Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authority</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate (INR)</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {regulatoryFees.map((fee, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          <input
                            type="text"
                            value={fee.fee_name}
                            onChange={(e) => updateRegulatoryFee(index, 'fee_name', e.target.value)}
                            className="w-full px-2 py-1 border border-gray-300 rounded-md"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <input
                            type="text"
                            value={fee.authority}
                            onChange={(e) => updateRegulatoryFee(index, 'authority', e.target.value)}
                            className="w-full px-2 py-1 border border-gray-300 rounded-md"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <input
                            type="number"
                            value={fee.rate}
                            onChange={(e) => updateRegulatoryFee(index, 'rate', Number(e.target.value))}
                            className="w-24 px-2 py-1 border border-gray-300 rounded-md"
                            min="0"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <select
                            value={fee.unit}
                            onChange={(e) => updateRegulatoryFee(index, 'unit', e.target.value)}
                            className="px-2 py-1 border border-gray-300 rounded-md"
                          >
                            <option value="per sqm">per sqm</option>
                            <option value="Lump Sum">Lump Sum</option>
                            <option value="per floor">per floor</option>
                            <option value="percentage">percentage</option>
                          </select>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <button
                            onClick={() => removeRegulatoryFee(index)}
                            className="text-red-500 hover:text-red-700 transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleSaveFees}
                disabled={isSaving}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white rounded-lg font-medium transition-colors"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    Save Fee Structures
                  </>
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}