import React, { useState, useEffect } from 'react'
import { X, Palette, Zap, Droplets, Eye, AlertTriangle, Check, Edit, Package, Info, Camera, Loader } from 'lucide-react'
import { RoomConfiguration } from '../../types/calculator'
import { Component, componentAPI, overrideAPI } from '../../lib/supabase'
import { BundledComponentSelector } from './BundledComponentSelector'

interface VisualRoomConfiguratorProps {
  room: RoomConfiguration
  isOpen: boolean
  onClose: () => void
  onSave: (roomConfig: any) => void
  projectId?: string
}

interface RoomCustomization {
  flooringComponent: Component | null
  wallFinishComponent: Component | null
  electricalPoints: number
  plumbingFixtures: number
  bundleSelections: Record<string, any>
  customizations: Record<string, any>
}

export function VisualRoomConfigurator({ 
  room, 
  isOpen, 
  onClose, 
  onSave,
  projectId 
}: VisualRoomConfiguratorProps) {
  const [activeCategory, setActiveCategory] = useState<'flooring' | 'walls' | 'electrical' | 'plumbing' | 'bundles'>('flooring')
  const [flooringOptions, setFlooringOptions] = useState<Component[]>([])
  const [wallOptions, setWallOptions] = useState<Component[]>([])
  const [bundleOptions, setBundleOptions] = useState<Component[]>([])
  const [plumbingOptions, setPlumbingOptions] = useState<Component[]>([])
  const [electricalOptions, setElectricalOptions] = useState<Component[]>([])
  const [customization, setCustomization] = useState<RoomCustomization>({
    flooringComponent: null,
    wallFinishComponent: null,
    electricalPoints: Math.ceil(room.areaPerRoom / 100) * 2,
    plumbingFixtures: room.type === 'Bathroom' ? 1 : 0,
    bundleSelections: {},
    customizations: {}
  })
  const [isLoading, setIsLoading] = useState(false)
  const [showOverrideModal, setShowOverrideModal] = useState(false)
  const [overrideData, setOverrideData] = useState<{
    component: Component | null
    originalQuantity: number
    originalRate: number
    overrideQuantity?: number
    overrideRate?: number
    reason?: string
  }>({
    component: null,
    originalQuantity: 0,
    originalRate: 0
  })
  const [isSavingOverride, setIsSavingOverride] = useState(false)
  const [view3DMode, setView3DMode] = useState<'2d' | '3d'>('2d')
  const [showMaterialInfo, setShowMaterialInfo] = useState<string | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [selectedElectricalComponent, setSelectedElectricalComponent] = useState<Component | null>(null)
  const [selectedPlumbingComponent, setSelectedPlumbingComponent] = useState<Component | null>(null)

  useEffect(() => {
    if (isOpen) {
      loadComponentOptions()
      loadExistingCustomization()
    }
  }, [isOpen])

  const loadComponentOptions = async () => {
    setIsLoading(true)
    try {
      const [flooring, walls, bundles, plumbing, electrical] = await Promise.all([
        componentAPI.getAll('Flooring'),
        componentAPI.getAll('Wall Finishes'),
        componentAPI.getAll().then(components => 
          components.filter(c => c.name.includes('Bundle') || c.name.includes('Set'))
        ),
        componentAPI.getAll('Plumbing'),
        componentAPI.getAll('Electrical')
      ])
      setFlooringOptions(flooring)
      setWallOptions(walls)
      setBundleOptions(bundles)
      setPlumbingOptions(plumbing)
      setElectricalOptions(electrical)
      
      // Set default electrical component
      if (electrical.length > 0) {
        const defaultElectrical = electrical.find(c => c.name.includes('Standard')) || electrical[0]
        setSelectedElectricalComponent(defaultElectrical)
      }
      
      // Set default plumbing component for bathrooms
      if (room.type === 'Bathroom' && plumbing.length > 0) {
        const defaultPlumbing = plumbing.find(c => c.name.includes('Standard')) || plumbing[0]
        setSelectedPlumbingComponent(defaultPlumbing)
      }
    } catch (error) {
      console.error('Error loading components:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadExistingCustomization = async () => {
    if (!projectId) return
    
    try {
      // This would be replaced with actual API call to get saved customizations
      // For now, we'll just set some defaults based on room type
      if (room.type === 'Bathroom') {
        const antiSkidTiles = flooringOptions.find(c => c.name.includes('Anti-Skid'))
        if (antiSkidTiles) {
          setCustomization(prev => ({
            ...prev,
            flooringComponent: antiSkidTiles
          }))
        }
      } else {
        const vitrifiedTiles = flooringOptions.find(c => c.name.includes('Vitrified'))
        if (vitrifiedTiles) {
          setCustomization(prev => ({
            ...prev,
            flooringComponent: vitrifiedTiles
          }))
        }
      }
    } catch (error) {
      console.error('Error loading existing customization:', error)
    }
  }

  const calculateComponentCost = (component: Component, area: number) => {
    const quantity = component.cost_model === 'per_sqm' ? area : 1
    return {
      quantity,
      rate: component.unit_price,
      total: quantity * component.unit_price
    }
  }

  const handleComponentSelect = (component: Component, category: string) => {
    if (category === 'flooring') {
      setCustomization(prev => ({ ...prev, flooringComponent: component }))
    } else if (category === 'walls') {
      setCustomization(prev => ({ ...prev, wallFinishComponent: component }))
    } else if (category === 'electrical') {
      setSelectedElectricalComponent(component)
    } else if (category === 'plumbing') {
      setSelectedPlumbingComponent(component)
    }
  }

  const handleBundleSelectionChange = (bundleId: string, selectedItems: any[], totalCost: number) => {
    setCustomization(prev => ({
      ...prev,
      bundleSelections: {
        ...prev.bundleSelections,
        [bundleId]: {
          selectedItems,
          totalCost
        }
      }
    }))
  }

  const handleOverrideClick = (component: Component) => {
    const cost = calculateComponentCost(component, room.totalArea)
    setOverrideData({
      component,
      originalQuantity: cost.quantity,
      originalRate: cost.rate
    })
    setShowOverrideModal(true)
  }

  const handleSaveOverride = async () => {
    if (!overrideData.component || !projectId) return

    setIsSavingOverride(true)
    try {
      await overrideAPI.create({
        project_id: projectId,
        component_id: overrideData.component.id,
        original_quantity: overrideData.originalQuantity,
        override_quantity: overrideData.overrideQuantity,
        original_rate: overrideData.originalRate,
        override_rate: overrideData.overrideRate,
        override_reason: overrideData.reason
      })
      
      // Update local state to reflect override
      if (overrideData.component.category === 'Flooring' && customization.flooringComponent?.id === overrideData.component.id) {
        const updatedComponent = { ...customization.flooringComponent };
        if (overrideData.overrideRate !== undefined) {
          updatedComponent.unit_price = overrideData.overrideRate;
        }
        setCustomization(prev => ({ ...prev, flooringComponent: updatedComponent }));
      } else if (overrideData.component.category === 'Wall Finishes' && customization.wallFinishComponent?.id === overrideData.component.id) {
        const updatedComponent = { ...customization.wallFinishComponent };
        if (overrideData.overrideRate !== undefined) {
          updatedComponent.unit_price = overrideData.overrideRate;
        }
        setCustomization(prev => ({ ...prev, wallFinishComponent: updatedComponent }));
      }
      
      setShowOverrideModal(false)
    } catch (error) {
      console.error('Error saving override:', error)
      alert('Failed to save override. Please try again.')
    } finally {
      setIsSavingOverride(false)
    }
  }

  const handleElectricalPointsChange = (points: number) => {
    setCustomization(prev => ({
      ...prev,
      electricalPoints: points
    }))
  }

  const handlePlumbingFixturesChange = (fixtures: number) => {
    setCustomization(prev => ({
      ...prev,
      plumbingFixtures: fixtures
    }))
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      const roomConfig = {
        roomId: room.id,
        roomType: room.type,
        flooringComponent: customization.flooringComponent,
        wallFinishComponent: customization.wallFinishComponent,
        electricalPoints: customization.electricalPoints,
        plumbingFixtures: customization.plumbingFixtures,
        electricalComponent: selectedElectricalComponent,
        plumbingComponent: selectedPlumbingComponent,
        bundleSelections: customization.bundleSelections,
        customizations: customization.customizations
      }
      
      await onSave(roomConfig)
      onClose()
    } catch (error) {
      console.error('Error saving room configuration:', error)
      alert('Failed to save room configuration. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const toggleMaterialInfo = (componentId: string | null) => {
    setShowMaterialInfo(componentId === showMaterialInfo ? null : componentId)
  }

  if (!isOpen) return null

  const categories = [
    { id: 'flooring', label: 'Flooring', icon: Palette, options: flooringOptions },
    { id: 'walls', label: 'Wall Finish', icon: Palette, options: wallOptions },
    { id: 'electrical', label: 'Electrical', icon: Zap, options: electricalOptions },
    ...(room.type === 'Bathroom' ? [{ id: 'plumbing', label: 'Plumbing', icon: Droplets, options: plumbingOptions }] : []),
    ...(room.type === 'Bathroom' ? [{ id: 'bundles', label: 'Bundles', icon: Package, options: bundleOptions }] : [])
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Customize {room.type}</h2>
            <p className="text-gray-600 mt-1">
              {room.totalArea} sq ft total • {room.count > 1 ? `${room.count} rooms • ${room.areaPerRoom} sq ft each` : ''}
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex border border-gray-200 rounded-lg overflow-hidden">
              <button
                onClick={() => setView3DMode('2d')}
                className={`px-3 py-1 text-sm font-medium ${
                  view3DMode === '2d' 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                2D View
              </button>
              <button
                onClick={() => setView3DMode('3d')}
                className={`px-3 py-1 text-sm font-medium ${
                  view3DMode === '3d' 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                3D View
              </button>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-6 h-6 text-gray-500" />
            </button>
          </div>
        </div>

        <div className="flex h-[calc(90vh-200px)]">
          {/* 3D Room Preview */}
          <div className="flex-1 bg-gray-100 relative">
            <div className="absolute inset-0 flex items-center justify-center">
              {view3DMode === '2d' ? (
                <div className="text-center">
                  <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">2D Floor Plan View</h3>
                    
                    {/* 2D Room Visualization */}
                    <div className="relative w-64 h-64 mx-auto border-2 border-gray-300 rounded-lg mb-4">
                      {/* Room outline */}
                      <div 
                        className="absolute inset-0 m-2 rounded"
                        style={{
                          backgroundColor: customization.flooringComponent 
                            ? '#dbeafe' // Light blue for selected flooring
                            : '#f3f4f6' // Light gray for default
                        }}
                      >
                        {/* Room label */}
                        <div className="absolute top-2 left-2 text-sm font-medium text-gray-700">
                          {room.type}
                        </div>
                        
                        {/* Room dimensions */}
                        <div className="absolute bottom-2 right-2 text-xs text-gray-500">
                          {Math.sqrt(room.areaPerRoom).toFixed(1)}' × {Math.sqrt(room.areaPerRoom).toFixed(1)}'
                        </div>
                        
                        {/* Electrical points */}
                        {Array.from({ length: Math.min(customization.electricalPoints, 8) }).map((_, i) => (
                          <div 
                            key={i}
                            className="absolute w-2 h-2 bg-yellow-400 rounded-full"
                            style={{
                              top: `${20 + (i % 4) * 20}%`,
                              left: `${20 + Math.floor(i / 4) * 60}%`
                            }}
                            title="Electrical point"
                          />
                        ))}
                        
                        {/* Plumbing fixtures for bathroom */}
                        {room.type === 'Bathroom' && customization.plumbingFixtures > 0 && (
                          <>
                            <div 
                              className="absolute w-12 h-6 bg-blue-200 rounded-sm"
                              style={{ bottom: '10%', right: '10%' }}
                              title="Washbasin"
                            />
                            <div 
                              className="absolute w-8 h-12 bg-blue-200 rounded-sm"
                              style={{ bottom: '10%', left: '10%' }}
                              title="WC"
                            />
                          </>
                        )}
                      </div>
                    </div>
                    
                    {/* Current selections preview */}
                    <div className="space-y-2 text-sm">
                      {customization.flooringComponent && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Flooring:</span>
                          <span className="font-medium">{customization.flooringComponent.name}</span>
                        </div>
                      )}
                      {customization.wallFinishComponent && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Walls:</span>
                          <span className="font-medium">{customization.wallFinishComponent.name}</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span className="text-gray-600">Electrical Points:</span>
                        <span className="font-medium">{customization.electricalPoints}</span>
                      </div>
                      {room.type === 'Bathroom' && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Plumbing Fixtures:</span>
                          <span className="font-medium">{customization.plumbingFixtures}</span>
                        </div>
                      )}
                      {Object.keys(customization.bundleSelections).length > 0 && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Bundles:</span>
                          <span className="font-medium">{Object.keys(customization.bundleSelections).length} selected</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center">
                  <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">3D Room Visualization</h3>
                    
                    {/* 3D Room Preview (placeholder) */}
                    <div className="relative aspect-video bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg mb-4 flex items-center justify-center">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Camera className="w-12 h-12 text-blue-400 opacity-50" />
                      </div>
                      
                      {/* Hotspots */}
                      <div className="absolute top-1/4 left-1/4 w-4 h-4 bg-blue-500 rounded-full animate-pulse cursor-pointer" 
                           title="Floor" onClick={() => setActiveCategory('flooring')}></div>
                      <div className="absolute top-1/4 right-1/4 w-4 h-4 bg-green-500 rounded-full animate-pulse cursor-pointer" 
                           title="Wall" onClick={() => setActiveCategory('walls')}></div>
                      <div className="absolute bottom-1/4 left-1/4 w-4 h-4 bg-yellow-500 rounded-full animate-pulse cursor-pointer" 
                           title="Electrical" onClick={() => setActiveCategory('electrical')}></div>
                      {room.type === 'Bathroom' && (
                        <div className="absolute bottom-1/4 right-1/4 w-4 h-4 bg-purple-500 rounded-full animate-pulse cursor-pointer" 
                             title="Plumbing" onClick={() => setActiveCategory('plumbing')}></div>
                      )}
                      
                      {/* Selected materials overlay */}
                      <div className="absolute bottom-4 left-4 right-4 bg-white bg-opacity-80 p-2 rounded text-sm">
                        <div className="font-medium text-gray-800">Selected Materials:</div>
                        <div className="text-gray-600">
                          {customization.flooringComponent?.name || 'No flooring selected'} | 
                          {customization.wallFinishComponent?.name || ' No wall finish selected'}
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-sm text-gray-600">
                      Click on the colored hotspots to quickly navigate to different customization categories
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Customization Panel */}
          <div className="w-96 border-l border-gray-200 flex flex-col">
            {/* Category Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex">
                {categories.map((category) => {
                  const IconComponent = category.icon
                  return (
                    <button
                      key={category.id}
                      onClick={() => setActiveCategory(category.id as any)}
                      className={`flex-1 flex items-center justify-center gap-2 py-3 px-2 text-sm font-medium border-b-2 transition-colors ${
                        activeCategory === category.id
                          ? 'border-blue-500 text-blue-600 bg-blue-50'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <IconComponent className="w-4 h-4" />
                      <span className="hidden sm:inline">{category.label}</span>
                    </button>
                  )
                })}
              </nav>
            </div>

            {/* Options Panel */}
            <div className="flex-1 overflow-y-auto p-4">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
                </div>
              ) : (
                <div className="space-y-4">
                  {activeCategory === 'flooring' && (
                    <FlooringOptions
                      options={flooringOptions}
                      selected={customization.flooringComponent}
                      onSelect={(component) => handleComponentSelect(component, 'flooring')}
                      onOverride={handleOverrideClick}
                      onShowInfo={toggleMaterialInfo}
                      showInfoFor={showMaterialInfo}
                      room={room}
                    />
                  )}

                  {activeCategory === 'walls' && (
                    <WallOptions
                      options={wallOptions}
                      selected={customization.wallFinishComponent}
                      onSelect={(component) => handleComponentSelect(component, 'walls')}
                      onOverride={handleOverrideClick}
                      onShowInfo={toggleMaterialInfo}
                      showInfoFor={showMaterialInfo}
                      room={room}
                    />
                  )}

                  {activeCategory === 'electrical' && (
                    <ElectricalOptions
                      points={customization.electricalPoints}
                      onChange={handleElectricalPointsChange}
                      room={room}
                      options={electricalOptions}
                      selected={selectedElectricalComponent}
                      onSelect={(component) => handleComponentSelect(component, 'electrical')}
                    />
                  )}

                  {activeCategory === 'plumbing' && room.type === 'Bathroom' && (
                    <PlumbingOptions
                      fixtures={customization.plumbingFixtures}
                      onChange={handlePlumbingFixturesChange}
                      room={room}
                      options={plumbingOptions}
                      selected={selectedPlumbingComponent}
                      onSelect={(component) => handleComponentSelect(component, 'plumbing')}
                    />
                  )}

                  {activeCategory === 'bundles' && room.type === 'Bathroom' && (
                    <div className="space-y-4">
                      <h4 className="font-semibold text-gray-800">Bathroom Bundles</h4>
                      {bundleOptions.map((bundle) => (
                        <div key={bundle.id} className="border border-gray-200 rounded-lg p-4">
                          <h5 className="font-medium text-gray-800 mb-2">{bundle.name}</h5>
                          <BundledComponentSelector
                            bundleComponent={bundle}
                            onSelectionChange={(items, cost) => handleBundleSelectionChange(bundle.id, items, cost)}
                            roomArea={room.totalArea}
                          />
                        </div>
                      ))}
                      {bundleOptions.length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                          <Package className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                          <p>No bundles available for this room type</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Save Button */}
            <div className="border-t border-gray-200 p-4">
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="w-full flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-3 px-4 rounded-lg font-medium transition-colors"
              >
                {isSaving ? (
                  <>
                    <Loader className="w-4 h-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Apply Customization'
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Override Modal */}
        {showOverrideModal && (
          <OverrideModal
            data={overrideData}
            onChange={setOverrideData}
            onSave={handleSaveOverride}
            onCancel={() => setShowOverrideModal(false)}
            isSaving={isSavingOverride}
          />
        )}
        
        {/* Material Info Modal */}
        {showMaterialInfo && (
          <MaterialInfoModal
            componentId={showMaterialInfo}
            onClose={() => setShowMaterialInfo(null)}
            components={[...flooringOptions, ...wallOptions, ...plumbingOptions, ...electricalOptions]}
          />
        )}
      </div>
    </div>
  )
}

function FlooringOptions({ 
  options, 
  selected, 
  onSelect, 
  onOverride,
  onShowInfo,
  showInfoFor,
  room 
}: {
  options: Component[]
  selected: Component | null
  onSelect: (component: Component) => void
  onOverride: (component: Component) => void
  onShowInfo: (componentId: string) => void
  showInfoFor: string | null
  room: RoomConfiguration
}) {
  return (
    <div className="space-y-3">
      <h4 className="font-semibold text-gray-800">Choose Flooring</h4>
      {options.map((option) => {
        const cost = option.cost_model === 'per_sqm' 
          ? room.totalArea * option.unit_price 
          : option.unit_price
        const isSelected = selected?.id === option.id

        return (
          <div
            key={option.id}
            className={`border rounded-lg p-3 cursor-pointer transition-all ${
              isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => onSelect(option)}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h5 className="font-medium text-gray-800">{option.name}</h5>
                <p className="text-sm text-gray-600">{option.brand}</p>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-sm font-semibold text-green-600">
                    ₹{cost.toLocaleString()}
                  </span>
                  <span className="text-xs text-gray-500">
                    (₹{option.unit_price}/{option.unit})
                  </span>
                </div>
              </div>
              <div className="flex gap-1">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onShowInfo(option.id)
                  }}
                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                  title="View material details"
                >
                  <Info className="w-4 h-4 text-gray-500" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onOverride(option)
                  }}
                  className="p-1 hover:bg-gray-100 rounded transition-colors"
                  title="Override pricing"
                >
                  <Edit className="w-4 h-4 text-gray-500" />
                </button>
                {isSelected && (
                  <Check className="w-5 h-5 text-blue-600" />
                )}
              </div>
            </div>
            
            {option.image_url && (
              <img 
                src={option.image_url} 
                alt={option.name}
                className="w-full h-20 object-cover rounded mt-2"
              />
            )}
            
            {/* Specifications */}
            {option.specifications && Object.keys(option.specifications).length > 0 && (
              <div className="mt-2 grid grid-cols-2 gap-x-4 gap-y-1 text-xs text-gray-500">
                {Object.entries(option.specifications).slice(0, 4).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="capitalize">{key}:</span>
                    <span className="text-gray-700">{value as string}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
}

function WallOptions({ 
  options, 
  selected, 
  onSelect, 
  onOverride,
  onShowInfo,
  showInfoFor,
  room 
}: {
  options: Component[]
  selected: Component | null
  onSelect: (component: Component) => void
  onOverride: (component: Component) => void
  onShowInfo: (componentId: string) => void
  showInfoFor: string | null
  room: RoomConfiguration
}) {
  // Estimate wall area (simplified calculation)
  const wallArea = room.totalArea * 2.5 // Approximate wall area

  return (
    <div className="space-y-3">
      <h4 className="font-semibold text-gray-800">Choose Wall Finish</h4>
      <p className="text-sm text-gray-600">Estimated wall area: {wallArea.toFixed(0)} sq ft</p>
      
      {options.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <Palette className="w-8 h-8 mx-auto mb-2 text-gray-400" />
          <p>Wall finish options coming soon</p>
        </div>
      ) : (
        options.map((option) => {
          const cost = option.cost_model === 'per_sqm' 
            ? wallArea * option.unit_price 
            : option.unit_price
          const isSelected = selected?.id === option.id

          return (
            <div
              key={option.id}
              className={`border rounded-lg p-3 cursor-pointer transition-all ${
                isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onSelect(option)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h5 className="font-medium text-gray-800">{option.name}</h5>
                  <p className="text-sm text-gray-600">{option.brand}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-sm font-semibold text-green-600">
                      ₹{cost.toLocaleString()}
                    </span>
                    <span className="text-xs text-gray-500">
                      (₹{option.unit_price}/{option.unit})
                    </span>
                  </div>
                </div>
                <div className="flex gap-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      onShowInfo(option.id)
                    }}
                    className="p-1 hover:bg-gray-100 rounded transition-colors"
                    title="View material details"
                  >
                    <Info className="w-4 h-4 text-gray-500" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      onOverride(option)
                    }}
                    className="p-1 hover:bg-gray-100 rounded transition-colors"
                    title="Override pricing"
                  >
                    <Edit className="w-4 h-4 text-gray-500" />
                  </button>
                  {isSelected && (
                    <Check className="w-5 h-5 text-blue-600" />
                  )}
                </div>
              </div>
              
              {option.image_url && (
                <img 
                  src={option.image_url} 
                  alt={option.name}
                  className="w-full h-20 object-cover rounded mt-2"
                />
              )}
              
              {/* Specifications */}
              {option.specifications && Object.keys(option.specifications).length > 0 && (
                <div className="mt-2 grid grid-cols-2 gap-x-4 gap-y-1 text-xs text-gray-500">
                  {Object.entries(option.specifications).slice(0, 4).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="capitalize">{key}:</span>
                      <span className="text-gray-700">{value as string}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )
        })
      )}
    </div>
  )
}

function ElectricalOptions({ 
  points, 
  onChange, 
  room,
  options,
  selected,
  onSelect
}: {
  points: number
  onChange: (points: number) => void
  room: RoomConfiguration
  options: Component[]
  selected: Component | null
  onSelect: (component: Component) => void
}) {
  const recommendedPoints = Math.ceil(room.areaPerRoom / 100) * 2
  const costPerPoint = selected ? selected.unit_price : 350 // Use selected component price or default

  return (
    <div className="space-y-4">
      <h4 className="font-semibold text-gray-800">Electrical Points</h4>
      
      <div className="bg-blue-50 rounded-lg p-3">
        <div className="flex items-center gap-2 mb-2">
          <Zap className="w-4 h-4 text-blue-600" />
          <span className="text-sm font-medium text-blue-800">Recommended: {recommendedPoints} points</span>
        </div>
        <p className="text-xs text-blue-600">
          Based on room size and typical usage patterns
        </p>
      </div>

      {/* Component Selection */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Select Electrical Component Quality
        </label>
        {options.map((option) => {
          const isSelected = selected?.id === option.id;
          return (
            <div
              key={option.id}
              className={`border rounded-lg p-3 cursor-pointer transition-all ${
                isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onSelect(option)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h5 className="font-medium text-gray-800">{option.name}</h5>
                  <p className="text-sm text-gray-600">{option.brand}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-sm font-semibold text-green-600">
                      ₹{option.unit_price.toLocaleString()}/{option.unit}
                    </span>
                  </div>
                </div>
                {isSelected && (
                  <Check className="w-5 h-5 text-blue-600" />
                )}
              </div>
              
              {/* Specifications */}
              {option.specifications && Object.keys(option.specifications).length > 0 && (
                <div className="mt-2 grid grid-cols-2 gap-x-4 gap-y-1 text-xs text-gray-500">
                  {Object.entries(option.specifications).slice(0, 4).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="capitalize">{key}:</span>
                      <span className="text-gray-700">{value as string}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Number of Electrical Points
        </label>
        <div className="flex items-center gap-4">
          <button
            onClick={() => onChange(Math.max(1, points - 1))}
            className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
          >
            -
          </button>
          <span className="text-lg font-semibold w-12 text-center">{points}</span>
          <button
            onClick={() => onChange(points + 1)}
            className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
          >
            +
          </button>
        </div>
      </div>

      <div className="bg-gray-50 rounded-lg p-3">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Estimated Cost:</span>
          <span className="font-semibold">₹{(points * costPerPoint).toLocaleString()}</span>
        </div>
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>Per point:</span>
          <span>₹{costPerPoint}</span>
        </div>
      </div>
      
      {/* Electrical Points Explanation */}
      <div className="bg-yellow-50 rounded-lg p-3 border border-yellow-200">
        <h5 className="text-sm font-medium text-yellow-800 mb-1">What's included in each point?</h5>
        <ul className="text-xs text-yellow-700 space-y-1 list-disc pl-4">
          <li>Switch/socket with modular plate</li>
          <li>Concealed PVC conduit</li>
          <li>Copper wiring (FR grade)</li>
          <li>Junction box and accessories</li>
          <li>Installation labor</li>
        </ul>
        <p className="text-xs text-yellow-600 mt-2">
          Note: Light fixtures, fans, and appliances are not included in this cost.
        </p>
      </div>
    </div>
  )
}

function PlumbingOptions({ 
  fixtures, 
  onChange, 
  room,
  options,
  selected,
  onSelect
}: {
  fixtures: number
  onChange: (fixtures: number) => void
  room: RoomConfiguration
  options: Component[]
  selected: Component | null
  onSelect: (component: Component) => void
}) {
  const costPerFixture = selected ? selected.unit_price : 15000 // Use selected component price or default

  return (
    <div className="space-y-4">
      <h4 className="font-semibold text-gray-800">Plumbing Fixtures</h4>
      
      <div className="bg-blue-50 rounded-lg p-3">
        <div className="flex items-center gap-2 mb-2">
          <Droplets className="w-4 h-4 text-blue-600" />
          <span className="text-sm font-medium text-blue-800">Standard Bathroom Set</span>
        </div>
        <p className="text-xs text-blue-600">
          Includes WC, washbasin, faucets, and basic accessories
        </p>
      </div>

      {/* Component Selection */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Select Bathroom Fixtures Quality
        </label>
        {options.map((option) => {
          const isSelected = selected?.id === option.id;
          return (
            <div
              key={option.id}
              className={`border rounded-lg p-3 cursor-pointer transition-all ${
                isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onSelect(option)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h5 className="font-medium text-gray-800">{option.name}</h5>
                  <p className="text-sm text-gray-600">{option.brand}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-sm font-semibold text-green-600">
                      ₹{option.unit_price.toLocaleString()}/{option.unit}
                    </span>
                  </div>
                </div>
                {isSelected && (
                  <Check className="w-5 h-5 text-blue-600" />
                )}
              </div>
              
              {/* Specifications */}
              {option.specifications && Object.keys(option.specifications).length > 0 && (
                <div className="mt-2 grid grid-cols-2 gap-x-4 gap-y-1 text-xs text-gray-500">
                  {Object.entries(option.specifications).slice(0, 4).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="capitalize">{key}:</span>
                      <span className="text-gray-700">{value as string}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Number of Fixture Sets
        </label>
        <div className="flex items-center gap-4">
          <button
            onClick={() => onChange(Math.max(0, fixtures - 1))}
            className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
          >
            -
          </button>
          <span className="text-lg font-semibold w-12 text-center">{fixtures}</span>
          <button
            onClick={() => onChange(fixtures + 1)}
            className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
          >
            +
          </button>
        </div>
      </div>

      <div className="bg-gray-50 rounded-lg p-3">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Estimated Cost:</span>
          <span className="font-semibold">₹{(fixtures * costPerFixture).toLocaleString()}</span>
        </div>
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>Per set:</span>
          <span>₹{costPerFixture.toLocaleString()}</span>
        </div>
      </div>
      
      {/* Plumbing Fixtures Explanation */}
      <div className="bg-yellow-50 rounded-lg p-3 border border-yellow-200">
        <h5 className="text-sm font-medium text-yellow-800 mb-1">What's included in each set?</h5>
        <ul className="text-xs text-yellow-700 space-y-1 list-disc pl-4">
          <li>Water closet (WC) with flush</li>
          <li>Washbasin with mixer tap</li>
          <li>Shower with diverter</li>
          <li>Necessary fittings and valves</li>
          <li>Installation labor</li>
        </ul>
        <p className="text-xs text-yellow-600 mt-2">
          Note: For premium fixtures or additional items like bathtubs, please use the Bundles tab.
        </p>
      </div>
    </div>
  )
}

function OverrideModal({ 
  data, 
  onChange, 
  onSave, 
  onCancel,
  isSaving = false
}: {
  data: any
  onChange: (data: any) => void
  onSave: () => void
  onCancel: () => void
  isSaving?: boolean
}) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-bold text-gray-800">Override Pricing</h3>
          <button onClick={onCancel} className="p-2 hover:bg-gray-100 rounded-lg" disabled={isSaving}>
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        <div className="p-6 space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-semibold text-yellow-800">Manual Override Warning</h4>
                <p className="text-yellow-700 text-sm mt-1">
                  You are manually overriding auto-calculated values. This may affect the accuracy of your total estimate.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Original Quantity: {data.originalQuantity} {data.component?.unit}
              </label>
              <input
                type="number"
                value={data.overrideQuantity || ''}
                onChange={(e) => onChange({ ...data, overrideQuantity: Number(e.target.value) })}
                placeholder={data.originalQuantity.toString()}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isSaving}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Original Rate: ₹{data.originalRate}/{data.component?.unit}
              </label>
              <input
                type="number"
                value={data.overrideRate || ''}
                onChange={(e) => onChange({ ...data, overrideRate: Number(e.target.value) })}
                placeholder={data.originalRate.toString()}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isSaving}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reason for Override (Optional)
              </label>
              <textarea
                value={data.reason || ''}
                onChange={(e) => onChange({ ...data, reason: e.target.value })}
                placeholder="e.g., Got a better quote from local supplier"
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                disabled={isSaving}
              />
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              onClick={onSave}
              disabled={isSaving}
              className="flex-1 flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-2 px-4 rounded-lg font-medium transition-colors"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  Saving...
                </>
              ) : (
                'Save Override'
              )}
            </button>
            <button
              onClick={onCancel}
              disabled={isSaving}
              className="flex-1 bg-gray-200 hover:bg-gray-300 disabled:bg-gray-100 text-gray-800 py-2 px-4 rounded-lg font-medium transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

function MaterialInfoModal({
  componentId,
  onClose,
  components
}: {
  componentId: string
  onClose: () => void
  components: Component[]
}) {
  const component = components.find(c => c.id === componentId)
  
  if (!component) return null
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-lg w-full">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <h3 className="text-lg font-bold text-gray-800">{component.name}</h3>
          <button onClick={onClose} className="p-2 hover:bg-gray-100 rounded-lg">
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>
        
        <div className="p-6 space-y-4">
          {/* Material Image */}
          {component.image_url && (
            <img 
              src={component.image_url} 
              alt={component.name}
              className="w-full h-48 object-cover rounded-lg"
            />
          )}
          
          {/* Basic Info */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Brand:</span>
              <div className="font-semibold text-gray-800">{component.brand || 'Generic'}</div>
            </div>
            <div>
              <span className="text-gray-600">Category:</span>
              <div className="font-semibold text-gray-800">{component.category}</div>
            </div>
            <div>
              <span className="text-gray-600">Sub-Category:</span>
              <div className="font-semibold text-gray-800">{component.sub_category || 'N/A'}</div>
            </div>
            <div>
              <span className="text-gray-600">Price:</span>
              <div className="font-semibold text-green-600">₹{component.unit_price}/{component.unit}</div>
            </div>
          </div>
          
          {/* Specifications */}
          {component.specifications && Object.keys(component.specifications).length > 0 && (
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Specifications</h4>
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                  {Object.entries(component.specifications).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-gray-600 capitalize">{key}:</span>
                      <span className="text-gray-800">{value as string}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
          
          {/* Installation Information */}
          {component.associated_task_id && (
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Installation Information</h4>
              <div className="bg-blue-50 rounded-lg p-3 text-sm text-blue-700">
                <p>This material has an associated installation task that includes labor and supporting materials.</p>
                <p className="mt-1">The price shown includes only the material cost. The full installed cost will be calculated based on the area and installation requirements.</p>
              </div>
            </div>
          )}
          
          {/* Engineering Standards */}
          <div>
            <h4 className="font-medium text-gray-800 mb-2">Engineering Standards</h4>
            <div className="bg-gray-50 rounded-lg p-3 text-sm">
              {component.category === 'Flooring' && (
                <ul className="space-y-1 text-gray-700">
                  <li>• Follows IS 15622:2017 for ceramic/vitrified tiles</li>
                  <li>• Installation as per industry best practices</li>
                  <li>• Includes standard wastage factor of 5-8%</li>
                  <li>• Requires proper subfloor preparation</li>
                </ul>
              )}
              {component.category === 'Wall Finishes' && (
                <ul className="space-y-1 text-gray-700">
                  <li>• Follows IS 5411 for paint application</li>
                  <li>• Requires proper surface preparation</li>
                  <li>• Includes primer and two coats of paint</li>
                  <li>• Coverage as per manufacturer specifications</li>
                </ul>
              )}
              {component.category === 'Bathroom Fittings' && (
                <ul className="space-y-1 text-gray-700">
                  <li>• Follows IS 2556 for sanitary appliances</li>
                  <li>• Water-efficient fixtures as per standards</li>
                  <li>• Includes all necessary fittings and fixtures</li>
                  <li>• Installation as per manufacturer guidelines</li>
                </ul>
              )}
              {component.category === 'Plumbing' && (
                <ul className="space-y-1 text-gray-700">
                  <li>• Follows IS 15778:2007 for CPVC pipes</li>
                  <li>• Pressure tested to 10 kg/cm²</li>
                  <li>• Temperature rating up to 93°C</li>
                  <li>• Includes all necessary fittings and valves</li>
                </ul>
              )}
              {component.category === 'Electrical' && (
                <ul className="space-y-1 text-gray-700">
                  <li>• Follows IS 732:2019 for electrical installations</li>
                  <li>• FRLS wiring as per safety standards</li>
                  <li>• Modular switches with ISI certification</li>
                  <li>• Includes proper earthing connections</li>
                </ul>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}