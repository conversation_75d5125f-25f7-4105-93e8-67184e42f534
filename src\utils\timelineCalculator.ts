import { GeometricQuantities, QualityTier, UserInputs } from '../types/calculator';
import { laborRates, calculateManDays } from '../data/laborRates';
import { locationData } from '../data/locationMultipliers';

export interface ProjectPhase {
  id: string;
  name: string;
  description: string;
  manDays: number;
  duration: number; // in days
  dependencies: string[];
  criticalPath: boolean;
  activities: PhaseActivity[];
  startDay?: number;
  endDay?: number;
}

export interface PhaseActivity {
  id: string;
  name: string;
  manDays: number;
  duration: number;
  laborType: string;
  quantity: number;
  unit: string;
}

export interface ProjectTimeline {
  totalDuration: number; // in days
  totalManDays: number;
  phases: ProjectPhase[];
  criticalPath: string[];
  recommendations: string[];
  parallelActivities: string[][];
}

export function calculateProjectTimeline(
  inputs: UserInputs,
  geometricQuantities: GeometricQuantities,
  qualityTier: QualityTier
): ProjectTimeline {
  const locationInfo = locationData[inputs.location];
  const phases: ProjectPhase[] = [];

  // Phase 1: Site Preparation & Excavation
  const excavationVolume = geometricQuantities.concreteVolume_Foundations_m3 * 1.2;
  const excavationManDays = calculateManDays('excavation_manual', excavationVolume);
  
  phases.push({
    id: 'site_preparation',
    name: 'Site Preparation & Excavation',
    description: 'Site clearing, marking, and foundation excavation',
    manDays: excavationManDays + 5, // +5 for site preparation
    duration: Math.ceil((excavationManDays + 5) / 4), // 4 workers
    dependencies: [],
    criticalPath: true,
    activities: [
      {
        id: 'site_clearing',
        name: 'Site Clearing & Marking',
        manDays: 5,
        duration: 2,
        laborType: 'unskilled',
        quantity: geometricQuantities.groundCoverageArea,
        unit: 'sqft'
      },
      {
        id: 'excavation',
        name: 'Foundation Excavation',
        manDays: excavationManDays,
        duration: Math.ceil(excavationManDays / 4),
        laborType: 'unskilled',
        quantity: excavationVolume,
        unit: 'm³'
      }
    ]
  });

  // Phase 2: Foundation Work
  const foundationConcreteManDays = calculateManDays('concrete_mixing_pouring', geometricQuantities.concreteVolume_Foundations_m3);
  const foundationSteelManDays = calculateManDays('steel_fixing', geometricQuantities.steel_foundations_kg);
  const foundationFormworkManDays = calculateManDays('formwork_erection', geometricQuantities.concreteVolume_Foundations_m3 * 6); // Approx formwork area
  
  const foundationTotalManDays = foundationConcreteManDays + foundationSteelManDays + foundationFormworkManDays;
  
  phases.push({
    id: 'foundation',
    name: 'Foundation & Basement Work',
    description: 'Foundation concrete, steel work, and basement construction',
    manDays: foundationTotalManDays,
    duration: Math.ceil(foundationTotalManDays / 6), // 6 workers
    dependencies: ['site_preparation'],
    criticalPath: true,
    activities: [
      {
        id: 'foundation_steel',
        name: 'Foundation Steel Work',
        manDays: foundationSteelManDays,
        duration: Math.ceil(foundationSteelManDays / 3),
        laborType: 'skilled',
        quantity: geometricQuantities.steel_foundations_kg,
        unit: 'kg'
      },
      {
        id: 'foundation_concrete',
        name: 'Foundation Concrete',
        manDays: foundationConcreteManDays,
        duration: Math.ceil(foundationConcreteManDays / 4),
        laborType: 'skilled',
        quantity: geometricQuantities.concreteVolume_Foundations_m3,
        unit: 'm³'
      }
    ]
  });

  // Phase 3: Superstructure (Columns, Beams, Slabs)
  const structureConcreteManDays = calculateManDays('concrete_mixing_pouring', 
    geometricQuantities.concreteVolume_Columns_m3 + 
    geometricQuantities.concreteVolume_Beams_m3 + 
    geometricQuantities.concreteVolume_Slabs_m3
  );
  const structureSteelManDays = calculateManDays('steel_fixing', 
    geometricQuantities.steel_columns_kg + 
    geometricQuantities.steel_beams_kg + 
    geometricQuantities.steel_slabs_kg
  );
  
  const structureTotalManDays = structureConcreteManDays + structureSteelManDays;
  
  phases.push({
    id: 'superstructure',
    name: 'Superstructure Work',
    description: 'Columns, beams, and slab construction',
    manDays: structureTotalManDays,
    duration: Math.ceil(structureTotalManDays / 8), // 8 workers
    dependencies: ['foundation'],
    criticalPath: true,
    activities: [
      {
        id: 'structure_steel',
        name: 'Superstructure Steel Work',
        manDays: structureSteelManDays,
        duration: Math.ceil(structureSteelManDays / 4),
        laborType: 'skilled',
        quantity: geometricQuantities.steel_columns_kg + geometricQuantities.steel_beams_kg + geometricQuantities.steel_slabs_kg,
        unit: 'kg'
      },
      {
        id: 'structure_concrete',
        name: 'Superstructure Concrete',
        manDays: structureConcreteManDays,
        duration: Math.ceil(structureConcreteManDays / 4),
        laborType: 'skilled',
        quantity: geometricQuantities.concreteVolume_Columns_m3 + geometricQuantities.concreteVolume_Beams_m3 + geometricQuantities.concreteVolume_Slabs_m3,
        unit: 'm³'
      }
    ]
  });

  // Phase 4: Masonry Work
  const masonryManDays = calculateManDays('aac_block_laying', geometricQuantities.totalWallArea_sqm);
  
  phases.push({
    id: 'masonry',
    name: 'Masonry Work',
    description: 'Wall construction using AAC blocks or bricks',
    manDays: masonryManDays,
    duration: Math.ceil(masonryManDays / 6), // 6 workers
    dependencies: ['superstructure'],
    criticalPath: true,
    activities: [
      {
        id: 'wall_construction',
        name: 'Wall Construction',
        manDays: masonryManDays,
        duration: Math.ceil(masonryManDays / 6),
        laborType: 'skilled',
        quantity: geometricQuantities.totalWallArea_sqm,
        unit: 'sqm'
      }
    ]
  });

  // Phase 5: Roofing & Waterproofing
  const roofingManDays = 15; // Estimated based on project size
  
  phases.push({
    id: 'roofing',
    name: 'Roofing & Waterproofing',
    description: 'Roof construction and waterproofing work',
    manDays: roofingManDays,
    duration: Math.ceil(roofingManDays / 4), // 4 workers
    dependencies: ['masonry'],
    criticalPath: true,
    activities: [
      {
        id: 'roof_work',
        name: 'Roofing Work',
        manDays: roofingManDays,
        duration: Math.ceil(roofingManDays / 4),
        laborType: 'skilled',
        quantity: geometricQuantities.totalFloorArea_sqm,
        unit: 'sqm'
      }
    ]
  });

  // Phase 6: Plastering
  const plasteringManDays = calculateManDays('plastering_internal', geometricQuantities.totalWallArea_sqm);
  
  phases.push({
    id: 'plastering',
    name: 'Plastering Work',
    description: 'Internal and external plastering',
    manDays: plasteringManDays,
    duration: Math.ceil(plasteringManDays / 6), // 6 workers
    dependencies: ['roofing'],
    criticalPath: false, // Can run parallel with MEP rough-in
    activities: [
      {
        id: 'wall_plastering',
        name: 'Wall Plastering',
        manDays: plasteringManDays,
        duration: Math.ceil(plasteringManDays / 6),
        laborType: 'skilled',
        quantity: geometricQuantities.totalWallArea_sqm,
        unit: 'sqm'
      }
    ]
  });

  // Phase 7: MEP Rough-in (Can run parallel with plastering)
  const electricalPoints = Math.ceil(geometricQuantities.totalFloorArea_sqm * 0.86);
  const plumbingPoints = Math.ceil(geometricQuantities.totalFloorArea_sqm * 0.43);
  
  const electricalRoughManDays = calculateManDays('electrical_wiring', electricalPoints) * 0.6; // 60% for rough-in
  const plumbingRoughManDays = calculateManDays('plumbing_installation', plumbingPoints) * 0.7; // 70% for rough-in
  const mepRoughTotalManDays = Math.max(electricalRoughManDays, plumbingRoughManDays); // Can run parallel
  
  phases.push({
    id: 'mep_rough',
    name: 'MEP Rough-in',
    description: 'Electrical conduits and plumbing pipes installation',
    manDays: mepRoughTotalManDays,
    duration: Math.ceil(mepRoughTotalManDays / 4), // 4 workers
    dependencies: ['roofing'],
    criticalPath: false, // Can run parallel with plastering
    activities: [
      {
        id: 'electrical_rough',
        name: 'Electrical Rough-in',
        manDays: electricalRoughManDays,
        duration: Math.ceil(electricalRoughManDays / 2),
        laborType: 'skilled',
        quantity: electricalPoints,
        unit: 'points'
      },
      {
        id: 'plumbing_rough',
        name: 'Plumbing Rough-in',
        manDays: plumbingRoughManDays,
        duration: Math.ceil(plumbingRoughManDays / 2),
        laborType: 'skilled',
        quantity: plumbingPoints,
        unit: 'points'
      }
    ]
  });

  // Phase 8: Flooring & Tiling
  const flooringManDays = calculateManDays('tile_laying_floor', geometricQuantities.totalFloorArea_sqm);
  
  phases.push({
    id: 'flooring',
    name: 'Flooring & Tiling',
    description: 'Floor and wall tile installation',
    manDays: flooringManDays,
    duration: Math.ceil(flooringManDays / 4), // 4 workers
    dependencies: ['plastering', 'mep_rough'],
    criticalPath: true,
    activities: [
      {
        id: 'floor_tiling',
        name: 'Floor Tiling',
        manDays: flooringManDays,
        duration: Math.ceil(flooringManDays / 4),
        laborType: 'skilled',
        quantity: geometricQuantities.totalFloorArea_sqm,
        unit: 'sqm'
      }
    ]
  });

  // Phase 9: MEP Finishing
  const electricalFinishManDays = calculateManDays('electrical_wiring', electricalPoints) * 0.4; // 40% for finishing
  const plumbingFinishManDays = calculateManDays('plumbing_installation', plumbingPoints) * 0.3; // 30% for finishing
  const mepFinishTotalManDays = electricalFinishManDays + plumbingFinishManDays;
  
  phases.push({
    id: 'mep_finish',
    name: 'MEP Finishing',
    description: 'Electrical fittings and plumbing fixtures installation',
    manDays: mepFinishTotalManDays,
    duration: Math.ceil(mepFinishTotalManDays / 3), // 3 workers
    dependencies: ['flooring'],
    criticalPath: false,
    activities: [
      {
        id: 'electrical_finish',
        name: 'Electrical Finishing',
        manDays: electricalFinishManDays,
        duration: Math.ceil(electricalFinishManDays / 2),
        laborType: 'skilled',
        quantity: electricalPoints,
        unit: 'points'
      },
      {
        id: 'plumbing_finish',
        name: 'Plumbing Finishing',
        manDays: plumbingFinishManDays,
        duration: Math.ceil(plumbingFinishManDays / 1),
        laborType: 'skilled',
        quantity: plumbingPoints,
        unit: 'points'
      }
    ]
  });

  // Phase 10: Painting & Finishing
  const paintingManDays = calculateManDays('painting_interior', geometricQuantities.totalWallArea_sqm + geometricQuantities.totalCeilingArea_sqm);
  
  phases.push({
    id: 'painting_finishing',
    name: 'Painting & Finishing',
    description: 'Interior and exterior painting, final finishing',
    manDays: paintingManDays,
    duration: Math.ceil(paintingManDays / 4), // 4 workers
    dependencies: ['mep_finish'],
    criticalPath: true,
    activities: [
      {
        id: 'painting_work',
        name: 'Painting Work',
        manDays: paintingManDays,
        duration: Math.ceil(paintingManDays / 4),
        laborType: 'semiskilled',
        quantity: geometricQuantities.totalWallArea_sqm + geometricQuantities.totalCeilingArea_sqm,
        unit: 'sqm'
      }
    ]
  });

  // Phase 11: Final Fixtures & Handover
  const fixturesManDays = 10; // Estimated
  
  phases.push({
    id: 'final_fixtures',
    name: 'Final Fixtures & Handover',
    description: 'Door/window installation, final fixtures, cleanup',
    manDays: fixturesManDays,
    duration: Math.ceil(fixturesManDays / 3), // 3 workers
    dependencies: ['painting_finishing'],
    criticalPath: true,
    activities: [
      {
        id: 'fixtures_installation',
        name: 'Fixtures Installation',
        manDays: fixturesManDays,
        duration: Math.ceil(fixturesManDays / 3),
        laborType: 'skilled',
        quantity: 1,
        unit: 'lump sum'
      }
    ]
  });

  // Calculate critical path using dependency analysis
  const { criticalPath, totalDuration, parallelActivities } = calculateEnhancedCriticalPath(phases);
  const totalManDays = phases.reduce((sum, phase) => sum + phase.manDays, 0);

  // Generate recommendations based on location and quality tier
  const recommendations = generateTimelineRecommendations(inputs, qualityTier, locationInfo, totalDuration);

  return {
    totalDuration,
    totalManDays,
    phases,
    criticalPath,
    recommendations,
    parallelActivities
  };
}

function calculateEnhancedCriticalPath(phases: ProjectPhase[]): {
  criticalPath: string[];
  totalDuration: number;
  parallelActivities: string[][];
} {
  // Create a map of phases for easy lookup
  const phaseMap = new Map(phases.map(phase => [phase.id, phase]));
  
  // Calculate earliest start and finish times
  const earliestStart = new Map<string, number>();
  const earliestFinish = new Map<string, number>();
  
  // Initialize phases with no dependencies
  phases.forEach(phase => {
    if (phase.dependencies.length === 0) {
      earliestStart.set(phase.id, 0);
      earliestFinish.set(phase.id, phase.duration);
    }
  });
  
  // Forward pass - calculate earliest start/finish times
  let changed = true;
  while (changed) {
    changed = false;
    phases.forEach(phase => {
      if (!earliestStart.has(phase.id)) {
        // Check if all dependencies have been calculated
        const dependencyTimes = phase.dependencies.map(depId => earliestFinish.get(depId));
        if (dependencyTimes.every(time => time !== undefined)) {
          const maxDependencyFinish = Math.max(...dependencyTimes as number[]);
          earliestStart.set(phase.id, maxDependencyFinish);
          earliestFinish.set(phase.id, maxDependencyFinish + phase.duration);
          changed = true;
        }
      }
    });
  }
  
  // Calculate latest start and finish times (backward pass)
  const projectDuration = Math.max(...Array.from(earliestFinish.values()));
  const latestStart = new Map<string, number>();
  const latestFinish = new Map<string, number>();
  
  // Initialize phases with no successors
  phases.forEach(phase => {
    const hasSuccessors = phases.some(p => p.dependencies.includes(phase.id));
    if (!hasSuccessors) {
      latestFinish.set(phase.id, projectDuration);
      latestStart.set(phase.id, projectDuration - phase.duration);
    }
  });
  
  // Backward pass
  changed = true;
  while (changed) {
    changed = false;
    phases.forEach(phase => {
      if (!latestStart.has(phase.id)) {
        // Find all successors
        const successors = phases.filter(p => p.dependencies.includes(phase.id));
        const successorTimes = successors.map(successor => latestStart.get(successor.id));
        
        if (successorTimes.length > 0 && successorTimes.every(time => time !== undefined)) {
          const minSuccessorStart = Math.min(...successorTimes as number[]);
          latestFinish.set(phase.id, minSuccessorStart);
          latestStart.set(phase.id, minSuccessorStart - phase.duration);
          changed = true;
        }
      }
    });
  }
  
  // Identify critical path phases (where earliest start = latest start)
  const criticalPhases = phases.filter(phase => {
    const es = earliestStart.get(phase.id) || 0;
    const ls = latestStart.get(phase.id) || 0;
    return Math.abs(es - ls) < 0.1; // Allow for small floating point differences
  });
  
  // Update phase start/end times and critical path flags
  phases.forEach(phase => {
    phase.startDay = earliestStart.get(phase.id) || 0;
    phase.endDay = earliestFinish.get(phase.id) || 0;
    phase.criticalPath = criticalPhases.some(cp => cp.id === phase.id);
  });
  
  // Identify parallel activities (phases that can run simultaneously)
  const parallelActivities: string[][] = [];
  const timeSlots = new Map<number, string[]>();
  
  phases.forEach(phase => {
    const startDay = phase.startDay || 0;
    if (!timeSlots.has(startDay)) {
      timeSlots.set(startDay, []);
    }
    timeSlots.get(startDay)!.push(phase.id);
  });
  
  timeSlots.forEach(phaseIds => {
    if (phaseIds.length > 1) {
      parallelActivities.push(phaseIds);
    }
  });
  
  return {
    criticalPath: criticalPhases.map(phase => phase.id),
    totalDuration: projectDuration,
    parallelActivities
  };
}

function generateTimelineRecommendations(
  inputs: UserInputs,
  qualityTier: QualityTier,
  locationInfo: any,
  totalDuration: number
): string[] {
  const recommendations: string[] = [];

  // Weather-based recommendations
  recommendations.push(
    'Plan foundation work during dry season (October-March) for optimal concrete curing conditions.'
  );

  // Location-specific recommendations
  if (locationInfo.marketConditions.laborAvailability === 'scarce') {
    recommendations.push(
      `Labor availability is limited in ${locationInfo.name}. Consider booking skilled workers 2-3 weeks in advance.`
    );
  }

  // Quality tier recommendations
  if (qualityTier === 'best') {
    recommendations.push(
      'Premium quality tier requires specialized skilled labor. Add 10-15% buffer time for quality control.'
    );
  }

  // Project size recommendations
  if (inputs.numberOfFloors > 3) {
    recommendations.push(
      'Multi-story construction requires crane rental. Factor in 2-3 days for crane setup and dismantling.'
    );
  }

  // Monsoon considerations
  if (totalDuration > 180) { // More than 6 months
    recommendations.push(
      'Project spans monsoon season. Plan for 15-20% time extension during July-September due to weather delays.'
    );
  }

  // Parallel activity optimization
  recommendations.push(
    'Plastering and MEP rough-in can run in parallel to save 2-3 weeks. Coordinate teams carefully to avoid conflicts.'
  );

  return recommendations;
}