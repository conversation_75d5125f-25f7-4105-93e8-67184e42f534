export interface LocationData {
  id: string;
  name: string;
  costIndex: number;
  laborMultiplier: number;
  authority: string;
  approvalFeeRate: number;
  description: string;
  marketConditions: {
    materialAvailability: 'abundant' | 'moderate' | 'scarce';
    laborAvailability: 'abundant' | 'moderate' | 'scarce';
    transportationCost: 'low' | 'medium' | 'high';
  };
}

export const locationData: Record<string, LocationData> = {
  delhi: {
    id: 'delhi',
    name: 'Delhi',
    costIndex: 1.0,
    laborMultiplier: 1.0,
    authority: 'MCD Delhi',
    approvalFeeRate: 387,
    description: 'Baseline pricing for Delhi NCR region',
    marketConditions: {
      materialAvailability: 'abundant',
      laborAvailability: 'moderate',
      transportationCost: 'medium'
    }
  },
  gurgaon: {
    id: 'gurgaon',
    name: 'Gurgaon',
    costIndex: 1.05,
    laborMultiplier: 1.15,
    authority: 'DTCP Haryana',
    approvalFeeRate: 450,
    description: '5% higher material costs, 15% higher labor costs due to premium location',
    marketConditions: {
      materialAvailability: 'moderate',
      laborAvailability: 'scarce',
      transportationCost: 'high'
    }
  },
  noida: {
    id: 'noida',
    name: 'Noida',
    costIndex: 0.98,
    laborMultiplier: 0.95,
    authority: 'Noida Authority',
    approvalFeeRate: 350,
    description: '2% lower material costs, 5% lower labor costs due to better infrastructure',
    marketConditions: {
      materialAvailability: 'abundant',
      laborAvailability: 'abundant',
      transportationCost: 'low'
    }
  },
  ghaziabad: {
    id: 'ghaziabad',
    name: 'Ghaziabad',
    costIndex: 0.95,
    laborMultiplier: 0.90,
    authority: 'GDA',
    approvalFeeRate: 320,
    description: '5% lower material costs, 10% lower labor costs due to competitive market',
    marketConditions: {
      materialAvailability: 'abundant',
      laborAvailability: 'abundant',
      transportationCost: 'low'
    }
  }
};