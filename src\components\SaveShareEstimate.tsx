import React, { useState } from 'react';
import { Share2, <PERSON><PERSON>, <PERSON>, Check, AlertCircle, Save } from 'lucide-react';
import { CalculationResult, UserInputs } from '../types/calculator';

interface SaveShareEstimateProps {
  result: CalculationResult;
  inputs: UserInputs;
  isOpen: boolean;
  onClose: () => void;
}

interface SavedEstimateData {
  id: string;
  name: string;
  inputs: UserInputs;
  result: CalculationResult;
  timestamp: string;
  shareUrl?: string;
}

export function SaveShareEstimate({ result, inputs, isOpen, onClose }: SaveShareEstimateProps) {
  const [estimateName, setEstimateName] = useState('');
  const [shareUrl, setShareUrl] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const [copied, setCopied] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');

  if (!isOpen) return null;

  const generateUniqueId = () => {
    return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  };

  const saveEstimate = async () => {
    if (!estimateName.trim()) {
      alert('Please enter a name for your estimate');
      return;
    }

    setIsSaving(true);
    setSaveStatus('idle');

    try {
      const estimateData: SavedEstimateData = {
        id: generateUniqueId(),
        name: estimateName.trim(),
        inputs,
        result,
        timestamp: new Date().toISOString()
      };

      // Save to localStorage (in a real app, this would be a database)
      const existingEstimates = JSON.parse(localStorage.getItem('nirmaanai_estimates') || '[]');
      existingEstimates.push(estimateData);
      localStorage.setItem('nirmaanai_estimates', JSON.stringify(existingEstimates));

      setSaveStatus('success');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (error) {
      console.error('Error saving estimate:', error);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const generateShareUrl = async () => {
    setIsSharing(true);

    try {
      const estimateData: SavedEstimateData = {
        id: generateUniqueId(),
        name: estimateName.trim() || 'Shared Estimate',
        inputs,
        result,
        timestamp: new Date().toISOString()
      };

      // In a real application, this would save to a database and return a unique URL
      // For now, we'll simulate this by encoding the data in the URL
      const encodedData = btoa(JSON.stringify(estimateData));
      const baseUrl = window.location.origin + window.location.pathname;
      const shareableUrl = `${baseUrl}?shared=${encodedData}`;

      // Save to localStorage with share URL
      const existingSharedEstimates = JSON.parse(localStorage.getItem('nirmaanai_shared_estimates') || '[]');
      estimateData.shareUrl = shareableUrl;
      existingSharedEstimates.push(estimateData);
      localStorage.setItem('nirmaanai_shared_estimates', JSON.stringify(existingSharedEstimates));

      setShareUrl(shareableUrl);
    } catch (error) {
      console.error('Error generating share URL:', error);
      alert('Error generating share URL. Please try again.');
    } finally {
      setIsSharing(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = shareUrl;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-green-50 to-blue-50">
          <div className="flex items-center gap-3">
            <Share2 className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-bold text-gray-800">Save & Share Estimate</h2>
              <p className="text-gray-600 mt-1">Save your configuration and share with others</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            aria-label="Close save and share dialog"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Estimate Summary */}
          <div className="bg-blue-50 rounded-lg p-4 mb-6 border-2 border-blue-200">
            <h3 className="font-semibold text-blue-800 mb-3">Estimate Summary</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-blue-700">Total Cost:</span>
                <div className="font-bold text-blue-800 text-lg">{formatCurrency(result.totalCost)}</div>
              </div>
              <div>
                <span className="text-blue-700">Built-up Area:</span>
                <div className="font-bold text-blue-800">{result.quantities.totalBuiltUpArea.toLocaleString()} sq ft</div>
              </div>
              <div>
                <span className="text-blue-700">Quality Tier:</span>
                <div className="font-bold text-blue-800 capitalize">{result.qualityTier}</div>
              </div>
              <div>
                <span className="text-blue-700">Location:</span>
                <div className="font-bold text-blue-800 capitalize">{result.location}</div>
              </div>
            </div>
          </div>

          {/* Save Section */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <Save className="w-5 h-5 text-green-600" />
              Save Estimate
            </h3>
            <div className="space-y-4">
              <div>
                <label htmlFor="estimate-name" className="block text-sm font-medium text-gray-700 mb-2">
                  Estimate Name
                </label>
                <input
                  id="estimate-name"
                  type="text"
                  value={estimateName}
                  onChange={(e) => setEstimateName(e.target.value)}
                  placeholder="e.g., 'My Dream Home - With Basement'"
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <button
                onClick={saveEstimate}
                disabled={isSaving || !estimateName.trim()}
                className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    Save Estimate
                  </>
                )}
              </button>
              
              {/* Save Status */}
              {saveStatus === 'success' && (
                <div className="flex items-center gap-2 text-green-600 text-sm">
                  <Check className="w-4 h-4" />
                  Estimate saved successfully!
                </div>
              )}
              {saveStatus === 'error' && (
                <div className="flex items-center gap-2 text-red-600 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  Error saving estimate. Please try again.
                </div>
              )}
            </div>
          </div>

          {/* Share Section */}
          <div className="border-t border-gray-200 pt-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <Link className="w-5 h-5 text-blue-600" />
              Share Estimate
            </h3>
            
            {!shareUrl ? (
              <div className="space-y-4">
                <p className="text-gray-600 text-sm">
                  Generate a shareable link that others can use to view this estimate configuration. 
                  The shared version will be read-only.
                </p>
                <button
                  onClick={generateShareUrl}
                  disabled={isSharing}
                  className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors"
                >
                  {isSharing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      Generating Link...
                    </>
                  ) : (
                    <>
                      <Share2 className="w-4 h-4" />
                      Generate Share Link
                    </>
                  )}
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-green-600 text-sm flex items-center gap-2">
                  <Check className="w-4 h-4" />
                  Share link generated successfully!
                </p>
                <div className="bg-gray-50 rounded-lg p-3 border">
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      value={shareUrl}
                      readOnly
                      className="flex-1 bg-transparent text-sm text-gray-700 outline-none"
                    />
                    <button
                      onClick={copyToClipboard}
                      className="flex items-center gap-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm font-medium transition-colors"
                    >
                      {copied ? (
                        <>
                          <Check className="w-3 h-3" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="w-3 h-3" />
                          Copy
                        </>
                      )}
                    </button>
                  </div>
                </div>
                <div className="bg-yellow-50 rounded-lg p-3 border border-yellow-200">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="w-4 h-4 text-yellow-600 mt-0.5" />
                    <div className="text-sm text-yellow-700">
                      <strong>Note:</strong> Anyone with this link can view your estimate configuration. 
                      The shared version is read-only and cannot be modified.
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            Saved estimates are stored locally on your device
          </div>
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}