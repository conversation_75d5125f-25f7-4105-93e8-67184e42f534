import React, { useState, useCallback, useEffect } from 'react';
import { Calculator, Building2, Settings } from 'lucide-react';
import { UserInputSection } from './components/UserInputSection';
import { AreaStatement } from './components/AreaStatement';
import { ResultsSection } from './components/ResultsSection';
import { ProjectSummary } from './components/ProjectSummary';
import { SkipNavigation } from './components/SkipNavigation';
import { MaterialPriceTracker } from './components/MaterialPriceTracker';
import { ComplianceChecker } from './components/ComplianceChecker';
import { InteractiveFloorPlan } from './components/room/InteractiveFloorPlan';
import { VisualRoomConfigurator } from './components/room/VisualRoomConfigurator';
import { FacadeDesignModule } from './components/facade/FacadeDesignModule';
import { AdminDashboard } from './components/admin/AdminDashboard';
import { UserInputs, CalculatedQuantities, CalculationResult, QualityTier, CostItem, RoomConfiguration, RoomMaterialSelection, ProjectSummary as ProjectSummaryType } from './types/calculator';
import { calculateV2Cost, generateV2ProjectSummary } from './utils/v2CalculationEngine';
import { supabase, projectAPI, overrideAPI } from './lib/supabase';
import { AIArchitectNotes } from './components/AIArchitectNotes';

function App() {
  const [inputs, setInputs] = useState<UserInputs>({
    plotSize: 1000,
    plotUnit: 'sqft',
    plotInputMode: 'area',
    plotShapeAdjustment: 100,
    numberOfFloors: 3,
    constructionPercentage: 80,
    hasBasement: false,
    hasStiltParking: false,
    location: 'gurgaon'
  });

  const [result, setResult] = useState<CalculationResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [itemOverrides, setItemOverrides] = useState<Record<string, Partial<CostItem>>>({});
  const [roomMaterialSelections, setRoomMaterialSelections] = useState<RoomMaterialSelection[]>([]);
  const [showProjectSummary, setShowProjectSummary] = useState(false);
  const [projectSummary, setProjectSummary] = useState<ProjectSummaryType | null>(null);
  
  // V2.0 State
  const [showFloorPlan, setShowFloorPlan] = useState(false);
  const [showRoomConfigurator, setShowRoomConfigurator] = useState(false);
  const [showFacadeDesign, setShowFacadeDesign] = useState(false);
  const [showAdminPanel, setShowAdminPanel] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState<RoomConfiguration | null>(null);
  const [useV2Engine, setUseV2Engine] = useState(true); // V2 engine is always used now
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [currentProject, setCurrentProject] = useState<any>(null);

  // Check for user session and shared estimate on app load
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const sharedData = urlParams.get('shared');
    
    if (sharedData) {
      try {
        const decodedData = JSON.parse(atob(sharedData));
        setInputs(decodedData.inputs);
        setResult(decodedData.result);
        setRoomMaterialSelections(decodedData.result.roomMaterialSelections || []);
        
        setTimeout(() => {
          alert('You are viewing a shared estimate. This is read-only and cannot be modified.');
        }, 1000);
      } catch (error) {
        console.error('Error loading shared estimate:', error);
      }
    }

    // Check for user session
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setCurrentUser(session?.user || null);
    });

    return () => subscription.unsubscribe();
  }, []);

  const createOrUpdateProject = async (result: CalculationResult) => {
    if (!currentUser) return null;

    try {
      let projectId = currentProject?.id;

      const projectData = {
        user_id: currentUser.id,
        project_name: `${inputs.plotSize} sq ft ${inputs.numberOfFloors} floor house in ${inputs.location}`,
        project_data: { inputs, result },
        room_configurations: result.quantities.roomConfiguration,
        selected_components: {},
        total_cost: result.totalCost
      };

      if (projectId) {
        // Update existing project
        await projectAPI.update(projectId, projectData);
      } else {
        // Create new project
        const newProject = await projectAPI.create(projectData);
        setCurrentProject(newProject);
        projectId = newProject.id;
      }

      return projectId;
    } catch (error) {
      console.error('Error saving project:', error);
      return null;
    }
  };

  const calculateCost = useCallback(async (qualityTier: QualityTier = 'better') => {
    if (!inputs.plotSize) return;

    setIsLoading(true);
    
    try {
      // Use V2 calculation engine with Supabase components
      const calculationResult = await calculateV2Cost(inputs, qualityTier, itemOverrides, currentProject?.id);
      setResult(calculationResult);
      
      // Save project to database if user is logged in
      const projectId = await createOrUpdateProject(calculationResult);
      
      setShowFloorPlan(true); // Auto-show floor plan after calculation
    } catch (error) {
      console.error('Error calculating cost:', error);
      alert('Error calculating cost. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [inputs, itemOverrides, roomMaterialSelections, currentUser, currentProject]);

  const handleQualityTierChange = (tier: QualityTier) => {
    calculateCost(tier);
  };

  const handleItemChange = (sectionId: string, itemId: string, updates: Partial<CostItem>) => {
    const newOverrides = {
      ...itemOverrides,
      [itemId]: { ...itemOverrides[itemId], ...updates }
    };
    setItemOverrides(newOverrides);
    
    if (result) {
      calculateCost(result.qualityTier);
    }
  };

  const handleRoomConfigurationSave = (rooms: RoomConfiguration[]) => {
    if (result) {
      const updatedResult = {
        ...result,
        quantities: {
          ...result.quantities,
          roomConfiguration: rooms
        }
      };
      setResult(updatedResult);
      
      // Update project in database if user is logged in
      if (currentProject?.id) {
        projectAPI.update(currentProject.id, {
          room_configurations: rooms
        });
      }
      
      // Recalculate with updated room configuration
      calculateCost(result.qualityTier);
    }
  };

  const handleMaterialCustomizationSave = (selections: RoomMaterialSelection[]) => {
    setRoomMaterialSelections(selections);
    
    if (result) {
      // Update result with new selections
      const updatedResult = {
        ...result,
        roomMaterialSelections: selections
      };
      setResult(updatedResult);
      
      // Update project in database if user is logged in
      if (currentProject?.id) {
        projectAPI.update(currentProject.id, {
          selected_components: { roomMaterialSelections: selections }
        });
      }
      
      // Recalculate with updated material selections
      calculateCost(result.qualityTier);
    }
  };

  const handleRoomSelect = (room: RoomConfiguration) => {
    setSelectedRoom(room);
    setShowRoomConfigurator(true);
  };

  const handleRoomCustomizationSave = async (roomConfig: any) => {
    // Update room material selections
    const updatedSelections = [...roomMaterialSelections];
    const existingIndex = updatedSelections.findIndex(s => s.roomId === roomConfig.roomId);
    
    if (existingIndex >= 0) {
      updatedSelections[existingIndex] = roomConfig;
    } else {
      updatedSelections.push(roomConfig);
    }
    
    setRoomMaterialSelections(updatedSelections);
    
    // Recalculate costs
    if (result) {
      // Update result with new selections
      const updatedResult = {
        ...result,
        roomMaterialSelections: updatedSelections
      };
      setResult(updatedResult);
      
      // Update project in database if user is logged in
      if (currentProject?.id) {
        projectAPI.update(currentProject.id, {
          selected_components: { 
            ...currentProject.selected_components,
            roomCustomizations: updatedSelections
          }
        });
      }
      
      // Recalculate with updated room customization
      calculateCost(result.qualityTier);
    }
  };

  const handleFacadeDesignSave = async (facadeConfig: any) => {
    // Add facade costs to the calculation
    if (result) {
      const facadeItem: CostItem = {
        id: 'facade_design',
        name: 'Facade Design & Finishes',
        quantity: 1,
        unit: 'lump sum',
        rate: facadeConfig.totalCost,
        total: facadeConfig.totalCost,
        category: 'facade',
        editable: true,
        isCalculated: false
      };

      // Add or update facade section
      const updatedSections = [...result.sections];
      const facadeSection = updatedSections.find(s => s.id === 'facade');
      
      if (facadeSection) {
        facadeSection.items = [facadeItem];
        facadeSection.subtotal = facadeConfig.totalCost;
      } else {
        updatedSections.push({
          id: 'facade',
          title: 'Facade & Exterior',
          items: [facadeItem],
          subtotal: facadeConfig.totalCost
        });
      }

      const newTotalCost = updatedSections.reduce((sum, section) => sum + section.subtotal, 0);
      
      const updatedResult = {
        ...result,
        sections: updatedSections,
        totalCost: newTotalCost,
        ratePerSqft: newTotalCost / result.quantities.totalBuiltUpArea
      };
      
      setResult(updatedResult);
      
      // Update project in database if user is logged in
      if (currentProject?.id) {
        projectAPI.update(currentProject.id, {
          selected_components: { 
            ...currentProject.selected_components,
            facadeDesign: facadeConfig
          },
          total_cost: newTotalCost
        });
      }
    }
  };

  const handleShowSummary = () => {
    if (result) {
      const summary = generateV2ProjectSummary(inputs, result);
      setProjectSummary(summary);
      setShowProjectSummary(true);
    }
  };

  const handleMaterialPriceUpdate = (materialId: string, newPrice: number) => {
    if (result) {
      calculateCost(result.qualityTier);
    }
  };

  const handleLogin = async () => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: window.location.href
      }
    });
    
    if (error) {
      console.error('Error logging in:', error);
      alert('Error logging in. Please try again.');
    }
  };

  const handleLogout = async () => {
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      console.error('Error logging out:', error);
      alert('Error logging out. Please try again.');
    } else {
      setCurrentProject(null);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <SkipNavigation />

      {/* Header */}
      <header className="bg-white shadow-sm border-b" role="banner">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div 
                className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-600 to-blue-800 rounded-xl"
                aria-hidden="true"
              >
                <Building2 className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-gray-800">NirmaanAI v2.0</h1>
                <p className="text-sm sm:text-base text-gray-600">The Definitive User-Centric Certainty Engine</p>
              </div>
            </div>
            
            {/* Admin Panel Access & User Account */}
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <label className="text-sm text-gray-600">Engine:</label>
                <button
                  onClick={() => setUseV2Engine(!useV2Engine)}
                  className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                    useV2Engine 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-600'
                  }`}
                >
                  {useV2Engine ? 'V2.0 (Pluggable)' : 'Legacy'}
                </button>
              </div>
              
              {/* User Account */}
              {currentUser ? (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">{currentUser.email}</span>
                  <button
                    onClick={handleLogout}
                    className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm transition-colors"
                  >
                    Logout
                  </button>
                </div>
              ) : (
                <button
                  onClick={handleLogin}
                  className="px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg text-sm transition-colors"
                >
                  Login
                </button>
              )}
              
              <button
                onClick={() => setShowAdminPanel(true)}
                className="flex items-center gap-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm font-medium transition-colors"
              >
                <Settings className="w-4 h-4" />
                Admin Panel
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8" id="main-content" role="main">
        {/* Introduction */}
        <section className="text-center mb-6 sm:mb-8" aria-labelledby="intro-heading">
          <h2 id="intro-heading" className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-3 sm:mb-4 leading-tight">
            Professional Construction Cost Calculator for Gurgaon
          </h2>
          <p className="text-base sm:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Generate accurate, client-ready construction estimates with real-time material pricing, 
            regulatory compliance, and professional quote generation for Gurgaon projects.
          </p>
          
          {useV2Engine && (
            <div className="mt-4 inline-flex items-center gap-2 px-4 py-2 bg-green-50 border border-green-200 rounded-lg">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-green-800 text-sm font-medium">
                Powered by V2.0 Pluggable Component Architecture
              </span>
            </div>
          )}
        </section>

        {/* Material Price Tracker */}
        <MaterialPriceTracker onPriceUpdate={handleMaterialPriceUpdate} />

        {/* User Input Section */}
        <section aria-labelledby="input-section-heading">
          <h2 id="input-section-heading" className="sr-only">Project Configuration</h2>
          <UserInputSection
            inputs={inputs}
            onInputChange={setInputs}
            onCalculate={() => calculateCost()}
            isLoading={isLoading}
          />
        </section>

        {/* Compliance Checker */}
        {result && (
          <ComplianceChecker 
            inputs={inputs} 
            builtUpArea={result.quantities.totalBuiltUpArea} 
          />
        )}

        {/* Area Statement */}
        {result && (
          <section aria-labelledby="area-statement-heading">
            <h2 id="area-statement-heading" className="sr-only">Project Area Statement</h2>
            <AreaStatement
              quantities={result.quantities}
              plotSize={inputs.plotSize}
            />
          </section>
        )}

        {/* AI Architect Notes */}
        {result && (
          <AIArchitectNotes
            result={result}
            roomSelections={result.roomMaterialSelections}
          />
        )}

        {/* Interactive Floor Plan - V2.0 Feature */}
        {result && showFloorPlan && (
          <section aria-labelledby="floor-plan-heading">
            <h2 id="floor-plan-heading" className="sr-only">Interactive Floor Plan</h2>
            <InteractiveFloorPlan
              rooms={result.quantities.roomConfiguration}
              onRoomSelect={handleRoomSelect}
              selectedRoomId={selectedRoom?.id}
            />
            
            {/* V2.0 Action Buttons */}
            <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Customize Your Design</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => setShowFacadeDesign(true)}
                  className="flex items-center justify-center gap-3 p-4 border-2 border-orange-200 rounded-lg hover:border-orange-400 hover:bg-orange-50 transition-all"
                >
                  <Building2 className="w-6 h-6 text-orange-600" />
                  <div className="text-left">
                    <div className="font-semibold text-gray-800">Facade Design</div>
                    <div className="text-sm text-gray-600">Customize exterior finishes</div>
                  </div>
                </button>
                
                <button
                  onClick={() => setShowFloorPlan(!showFloorPlan)}
                  className="flex items-center justify-center gap-3 p-4 border-2 border-blue-200 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-all"
                >
                  <Calculator className="w-6 h-6 text-blue-600" />
                  <div className="text-left">
                    <div className="font-semibold text-gray-800">Room Planner</div>
                    <div className="text-sm text-gray-600">Adjust room configurations</div>
                  </div>
                </button>
                
                <button
                  onClick={handleShowSummary}
                  className="flex items-center justify-center gap-3 p-4 border-2 border-green-200 rounded-lg hover:border-green-400 hover:bg-green-50 transition-all"
                >
                  <Building2 className="w-6 h-6 text-green-600" />
                  <div className="text-left">
                    <div className="font-semibold text-gray-800">Project Summary</div>
                    <div className="text-sm text-gray-600">View complete overview</div>
                  </div>
                </button>
              </div>
            </div>
          </section>
        )}

        {/* Results Section */}
        {result && (
          <section aria-labelledby="results-section-heading">
            <h2 id="results-section-heading" className="sr-only">Cost Calculation Results</h2>
            <ResultsSection
              result={result}
              inputs={inputs}
              onQualityTierChange={handleQualityTierChange}
              onItemChange={handleItemChange}
              onRoomConfigurationSave={handleRoomConfigurationSave}
              onMaterialCustomizationSave={handleMaterialCustomizationSave}
              onShowSummary={handleShowSummary}
              projectId={currentProject?.id}
            />
          </section>
        )}

        {/* Call to Action */}
        {!result && !isLoading && (
          <section className="text-center py-8 sm:py-12" aria-labelledby="cta-heading">
            <div className="flex justify-center mb-4" aria-hidden="true">
              <Calculator className="w-12 h-12 sm:w-16 sm:h-16 text-blue-600" />
            </div>
            <h3 id="cta-heading" className="text-lg sm:text-xl font-semibold text-gray-800 mb-2">
              Ready to create a professional estimate?
            </h3>
            <p className="text-sm sm:text-base text-gray-600">
              Configure your project details above and generate a comprehensive construction cost analysis.
            </p>
          </section>
        )}
      </main>

      {/* V2.0 Modals */}
      {selectedRoom && (
        <VisualRoomConfigurator
          room={selectedRoom}
          isOpen={showRoomConfigurator}
          onClose={() => {
            setShowRoomConfigurator(false);
            setSelectedRoom(null);
          }}
          onSave={handleRoomCustomizationSave}
          projectId={currentProject?.id}
        />
      )}

      {result && (
        <FacadeDesignModule
          isOpen={showFacadeDesign}
          onClose={() => setShowFacadeDesign(false)}
          buildingData={{
            floors: inputs.numberOfFloors,
            totalArea: result.quantities.totalBuiltUpArea,
            externalWallArea: result.quantities.totalBuiltUpArea * 0.4 // Approximate
          }}
          onSave={handleFacadeDesignSave}
        />
      )}

      <AdminDashboard
        isOpen={showAdminPanel}
        onClose={() => setShowAdminPanel(false)}
      />

      {/* Legacy Modals */}
      {projectSummary && result && (
        <ProjectSummary
          isOpen={showProjectSummary}
          onClose={() => setShowProjectSummary(false)}
          summary={projectSummary}
          inputs={inputs}
          result={result}
        />
      )}

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-6 sm:py-8 mt-12 sm:mt-16" role="contentinfo">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <Building2 className="w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
            <span className="font-semibold text-sm sm:text-base">NirmaanAI v2.0</span>
          </div>
          <p className="text-xs sm:text-sm text-gray-400">
            The Definitive User-Centric Certainty Engine for construction cost planning
          </p>
          <p className="text-xs text-gray-500 mt-2">
            Powered by Pluggable Component Architecture & Supabase
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;