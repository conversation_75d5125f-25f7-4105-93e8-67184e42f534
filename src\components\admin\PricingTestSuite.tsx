import React, { useState, useEffect } from 'react';
import { Component } from '../../lib/supabase';
import { validateComponentPrices, ValidationResult } from '../../utils/priceValidation';
import { BrandQualitySelector } from '../BrandQualitySelector';
import { MaterialComparisonTool } from '../MaterialComparisonTool';

interface PricingTestSuiteProps {
  components: Component[];
}

interface TestScenario {
  id: string;
  name: string;
  description: string;
  location: string;
  qualityTier: 'good' | 'better' | 'best';
  projectType: string;
  expectedBudgetRange: {
    min: number;
    max: number;
  };
}

export function PricingTestSuite({ components }: PricingTestSuiteProps) {
  const [activeTest, setActiveTest] = useState<string>('validation');
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const [selectedScenario, setSelectedScenario] = useState<TestScenario | null>(null);
  const [testResults, setTestResults] = useState<any[]>([]);

  const testScenarios: TestScenario[] = [
    {
      id: 'budget_home',
      name: 'Budget Home (1000 sqft)',
      description: 'Basic quality materials for cost-effective construction',
      location: 'ghaziabad',
      qualityTier: 'good',
      projectType: 'Residential',
      expectedBudgetRange: { min: 800, max: 1200 }
    },
    {
      id: 'premium_home',
      name: 'Premium Home (1500 sqft)',
      description: 'High-quality materials for upscale residential project',
      location: 'gurgaon',
      qualityTier: 'better',
      projectType: 'Residential',
      expectedBudgetRange: { min: 1500, max: 2200 }
    },
    {
      id: 'luxury_villa',
      name: 'Luxury Villa (2500 sqft)',
      description: 'Premium materials for luxury residential construction',
      location: 'delhi',
      qualityTier: 'best',
      projectType: 'Luxury Residential',
      expectedBudgetRange: { min: 2500, max: 3500 }
    },
    {
      id: 'commercial_office',
      name: 'Commercial Office (5000 sqft)',
      description: 'Durable materials for commercial construction',
      location: 'noida',
      qualityTier: 'better',
      projectType: 'Commercial',
      expectedBudgetRange: { min: 1200, max: 1800 }
    }
  ];

  useEffect(() => {
    runPriceValidation();
  }, [components]);

  const runPriceValidation = () => {
    const componentData = components.map(c => ({
      name: c.name,
      category: c.category,
      subCategory: c.sub_category || '',
      unitPrice: c.unit_price
    }));

    const results = validateComponentPrices(componentData);
    setValidationResults(results);
  };

  const runScenarioTest = (scenario: TestScenario) => {
    setSelectedScenario(scenario);
    
    // Simulate a typical project calculation
    const projectComponents = [
      { category: 'Materials', subCategory: 'Cement', quantity: 100, unit: 'bag' },
      { category: 'Materials', subCategory: 'Steel', quantity: 2000, unit: 'kg' },
      { category: 'Flooring', subCategory: 'Vitrified Tiles', quantity: 1000, unit: 'sqft' },
      { category: 'Bathroom Fittings', subCategory: 'Complete Set', quantity: 3, unit: 'set' },
      { category: 'Electrical', subCategory: 'Switches', quantity: 25, unit: 'point' },
      { category: 'Wall Finishes', subCategory: 'Premium Emulsion', quantity: 2000, unit: 'sqft' },
      { category: 'Windows', subCategory: 'uPVC', quantity: 150, unit: 'sqft' },
      { category: 'Facade', subCategory: 'ACP Cladding', quantity: 500, unit: 'sqft' }
    ];

    const results = projectComponents.map(item => {
      const relevantComponents = components.filter(
        c => c.category === item.category && 
        (c.sub_category === item.subCategory || item.subCategory === '')
      );

      if (relevantComponents.length === 0) {
        return {
          ...item,
          selectedComponent: null,
          cost: 0,
          status: 'No components available'
        };
      }

      // Filter by quality tier
      const qualityFilteredComponents = relevantComponents.filter(c => {
        const qualityTier = c.specifications?.quality_tier;
        if (scenario.qualityTier === 'good') return qualityTier === 'good';
        if (scenario.qualityTier === 'better') return ['good', 'better'].includes(qualityTier);
        return true; // 'best' includes all
      });

      const selectedComponent = qualityFilteredComponents.length > 0 
        ? qualityFilteredComponents[0] 
        : relevantComponents[0];

      const regionalMultiplier = getRegionalMultiplier(scenario.location);
      const qualityMultiplier = getQualityTierMultiplier(scenario.qualityTier);
      const adjustedPrice = selectedComponent.unit_price * regionalMultiplier * qualityMultiplier;
      const totalCost = adjustedPrice * item.quantity;

      return {
        ...item,
        selectedComponent,
        unitPrice: adjustedPrice,
        cost: totalCost,
        status: 'Calculated'
      };
    });

    setTestResults(results);
  };

  const getTotalProjectCost = () => {
    return testResults.reduce((sum, item) => sum + item.cost, 0);
  };

  const getValidationSummary = () => {
    const critical = validationResults.filter(r => r.severity === 'critical').length;
    const errors = validationResults.filter(r => r.severity === 'error').length;
    const warnings = validationResults.filter(r => r.severity === 'warning').length;
    const valid = validationResults.filter(r => r.isValid && r.severity === 'info').length;

    return { critical, errors, warnings, valid };
  };

  const summary = getValidationSummary();

  return (
    <div className="space-y-6">
      {/* Test Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {[
            { id: 'validation', label: 'Price Validation', desc: 'Market rate compliance' },
            { id: 'scenarios', label: 'Scenario Testing', desc: 'Project simulations' },
            { id: 'comparison', label: 'Material Comparison', desc: 'Brand analysis' }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTest(tab.id)}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTest === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <div>
                <div>{tab.label}</div>
                <div className="text-xs text-gray-400">{tab.desc}</div>
              </div>
            </button>
          ))}
        </nav>
      </div>

      {/* Price Validation Tab */}
      {activeTest === 'validation' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Price Validation Summary</h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-red-800">{summary.critical}</div>
                <div className="text-sm text-red-600">Critical Issues</div>
              </div>
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-orange-800">{summary.errors}</div>
                <div className="text-sm text-orange-600">Errors</div>
              </div>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-yellow-800">{summary.warnings}</div>
                <div className="text-sm text-yellow-600">Warnings</div>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-green-800">{summary.valid}</div>
                <div className="text-sm text-green-600">Valid Prices</div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Recent Validation Results:</h4>
              <div className="max-h-64 overflow-y-auto space-y-2">
                {validationResults.slice(0, 10).map((result, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded border text-sm ${
                      result.severity === 'critical' ? 'bg-red-50 border-red-200' :
                      result.severity === 'error' ? 'bg-orange-50 border-orange-200' :
                      result.severity === 'warning' ? 'bg-yellow-50 border-yellow-200' :
                      'bg-green-50 border-green-200'
                    }`}
                  >
                    {result.message}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Scenario Testing Tab */}
      {activeTest === 'scenarios' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {testScenarios.map(scenario => (
              <div
                key={scenario.id}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  selectedScenario?.id === scenario.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => runScenarioTest(scenario)}
              >
                <h4 className="font-medium text-gray-900">{scenario.name}</h4>
                <p className="text-sm text-gray-600 mt-1">{scenario.description}</p>
                <div className="mt-2 flex items-center justify-between text-xs">
                  <span className="text-gray-500">{scenario.location} • {scenario.qualityTier}</span>
                  <span className="text-gray-500">
                    ₹{scenario.expectedBudgetRange.min}-{scenario.expectedBudgetRange.max}/sqft
                  </span>
                </div>
              </div>
            ))}
          </div>

          {selectedScenario && testResults.length > 0 && (
            <div className="bg-white rounded-lg border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Test Results: {selectedScenario.name}
              </h3>
              
              <div className="mb-4 p-4 bg-blue-50 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Total Project Cost:</span>
                  <span className="text-xl font-bold">₹{getTotalProjectCost().toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center mt-1">
                  <span className="text-sm text-gray-600">Expected Range:</span>
                  <span className="text-sm">
                    ₹{(selectedScenario.expectedBudgetRange.min * 1000).toLocaleString()} - 
                    ₹{(selectedScenario.expectedBudgetRange.max * 1000).toLocaleString()}
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                {testResults.map((result, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <div>
                      <span className="font-medium">{result.category}</span>
                      <span className="text-sm text-gray-600 ml-2">
                        ({result.quantity} {result.unit})
                      </span>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">₹{result.cost.toLocaleString()}</div>
                      <div className="text-sm text-gray-600">
                        ₹{result.unitPrice?.toFixed(2)}/{result.unit}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Material Comparison Tab */}
      {activeTest === 'comparison' && (
        <div className="space-y-6">
          <MaterialComparisonTool
            category="Flooring"
            subCategory="Vitrified Tiles"
            components={components}
            quantity={100}
            unit="sqft"
            location="delhi"
            qualityTier="better"
            onMaterialSelect={(componentId, adjustedPrice) => {
              console.log('Selected material:', componentId, adjustedPrice);
            }}
          />
        </div>
      )}
    </div>
  );
}

// Helper functions
function getRegionalMultiplier(location: string): number {
  const multipliers: Record<string, number> = {
    'delhi': 1.0,
    'gurgaon': 1.15,
    'noida': 1.05,
    'ghaziabad': 0.95
  };
  return multipliers[location.toLowerCase()] || 1.0;
}

function getQualityTierMultiplier(tier: 'good' | 'better' | 'best'): number {
  switch (tier) {
    case 'good': return 0.85;
    case 'better': return 1.0;
    case 'best': return 1.35;
    default: return 1.0;
  }
}
