# 🎛️ **ADMIN PANEL CONFIGURATION GUIDE**

## 📊 **WHERE TO CONFIGURE THE 15+ CRITICAL PARAMETERS**

### 🚨 **CURRENT STATUS:**
The parameters are **designed to be configurable** but need to be **added to the database and admin interface**. Here's exactly where and how to configure them:

## 🗄️ **DATABASE TABLES TO UPDATE**

### **1. STRUCTURAL ASSUMPTIONS TABLE**
**Table**: `engineering_standards` (category: 'structural')

```sql
-- Add new structural parameters
INSERT INTO engineering_standards (category, name, value, unit, description) VALUES
('structural', 'floor_height', 3.0, 'meters', 'Standard floor height'),
('structural', 'beam_width', 300, 'mm', 'Standard beam width'),
('structural', 'beam_depth', 450, 'mm', 'Standard beam depth'),
('structural', 'column_size_low_rise', 350, 'mm', 'Column size for ≤2 floors'),
('structural', 'column_size_mid_rise', 400, 'mm', 'Column size for 3-4 floors'),
('structural', 'column_size_high_rise', 450, 'mm', 'Column size for 5+ floors');
```

### **2. MATERIAL RATES TABLE**
**Create New Table**: `material_rates`

```sql
CREATE TABLE material_rates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  category VARCHAR(50) NOT NULL,
  name VARCHAR(100) NOT NULL,
  rate DECIMAL(10,2) NOT NULL,
  unit VARCHAR(20) NOT NULL,
  quality_tier VARCHAR(20) DEFAULT 'better',
  location VARCHAR(50) DEFAULT 'delhi',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Insert default material rates
INSERT INTO material_rates (category, name, rate, unit, quality_tier) VALUES
('excavation', 'Manual Excavation', 450.00, 'per_m3', 'better'),
('plaster', 'Cement Plaster 12mm', 85.00, 'per_sqm', 'better'),
('electrical', 'Electrical Point Installation', 350.00, 'per_point', 'better'),
('plumbing', 'Plumbing Point Installation', 500.00, 'per_point', 'better'),
('waterproofing', 'Membrane Waterproofing', 60.00, 'per_sqm', 'good'),
('waterproofing', 'Membrane Waterproofing', 85.00, 'per_sqm', 'better'),
('waterproofing', 'Membrane Waterproofing', 120.00, 'per_sqm', 'best');
```

### **3. CALCULATION FACTORS TABLE**
**Create New Table**: `calculation_factors`

```sql
CREATE TABLE calculation_factors (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  factor_name VARCHAR(100) NOT NULL,
  factor_value DECIMAL(10,4) NOT NULL,
  description TEXT,
  category VARCHAR(50) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Insert default calculation factors
INSERT INTO calculation_factors (factor_name, factor_value, description, category) VALUES
('plaster_coverage_factor', 3.0000, 'Internal plaster coverage multiplier', 'plaster'),
('door_density', 200.0000, 'Square meters per door', 'openings'),
('window_density', 150.0000, 'Square meters per window', 'openings'),
('excavation_extra_factor', 1.2000, 'Extra excavation percentage (20%)', 'excavation'),
('internal_wall_factor', 0.8000, 'Internal wall area factor', 'walls'),
('opening_external_distribution', 0.7000, 'Percentage of openings on external walls', 'openings'),
('opening_internal_distribution', 0.3000, 'Percentage of openings on internal walls', 'openings');
```

### **4. OPENING SIZES TABLE**
**Create New Table**: `opening_sizes`

```sql
CREATE TABLE opening_sizes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  opening_type VARCHAR(50) NOT NULL,
  dimension_type VARCHAR(20) NOT NULL, -- 'height' or 'width'
  value DECIMAL(5,2) NOT NULL,
  unit VARCHAR(10) DEFAULT 'meters',
  standard_reference VARCHAR(50) DEFAULT 'IS 1200:2012',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Insert IS 1200:2012 compliant opening sizes
INSERT INTO opening_sizes (opening_type, dimension_type, value, standard_reference) VALUES
('door', 'height', 2.10, 'IS 1200:2012'),
('door', 'width', 1.00, 'IS 1200:2012'),
('window', 'height', 1.50, 'IS 1200:2012'),
('window', 'width', 1.20, 'IS 1200:2012');
```

## 🎛️ **ADMIN PANEL INTERFACE LOCATIONS**

### **📍 WHERE TO ADD THESE IN ADMIN PANEL:**

#### **1. STRUCTURAL PARAMETERS**
**Location**: Admin Panel → Engineering Standards → Structural Assumptions

**Fields to Add:**
```typescript
// Floor Height Configuration
{
  label: "Floor Height",
  field: "floor_height", 
  type: "number",
  unit: "meters",
  min: 2.8,
  max: 3.5,
  step: 0.1,
  default: 3.0,
  description: "Standard floor height for calculations"
}

// Beam Dimensions
{
  label: "Beam Width",
  field: "beam_width",
  type: "number", 
  unit: "mm",
  min: 250,
  max: 400,
  step: 25,
  default: 300
}

{
  label: "Beam Depth", 
  field: "beam_depth",
  type: "number",
  unit: "mm", 
  min: 350,
  max: 600,
  step: 25,
  default: 450
}

// Column Sizing Matrix
{
  label: "Column Size - Low Rise (≤2 floors)",
  field: "column_size_low_rise",
  type: "number",
  unit: "mm",
  default: 350
}

{
  label: "Column Size - Mid Rise (3-4 floors)",
  field: "column_size_mid_rise", 
  type: "number",
  unit: "mm",
  default: 400
}

{
  label: "Column Size - High Rise (5+ floors)",
  field: "column_size_high_rise",
  type: "number", 
  unit: "mm",
  default: 450
}
```

#### **2. MATERIAL RATES**
**Location**: Admin Panel → Material Rates (New Section)

**Interface Structure:**
```typescript
// Material Rates Management
{
  sections: [
    {
      title: "Excavation Rates",
      fields: [
        { name: "manual_excavation", label: "Manual Excavation", unit: "₹/m³", default: 450 }
      ]
    },
    {
      title: "Plaster Rates", 
      fields: [
        { name: "cement_plaster", label: "Cement Plaster 12mm", unit: "₹/sqm", default: 85 }
      ]
    },
    {
      title: "MEP Rates",
      fields: [
        { name: "electrical_point", label: "Electrical Point", unit: "₹/point", default: 350 },
        { name: "plumbing_point", label: "Plumbing Point", unit: "₹/point", default: 500 }
      ]
    },
    {
      title: "Waterproofing Rates by Quality",
      fields: [
        { name: "waterproofing_good", label: "Good Quality", unit: "₹/sqm", default: 60 },
        { name: "waterproofing_better", label: "Better Quality", unit: "₹/sqm", default: 85 },
        { name: "waterproofing_best", label: "Best Quality", unit: "₹/sqm", default: 120 }
      ]
    }
  ]
}
```

#### **3. CALCULATION FACTORS**
**Location**: Admin Panel → Calculation Factors (New Section)

**Fields to Add:**
```typescript
{
  title: "Plaster Calculation",
  fields: [
    {
      name: "plaster_coverage_factor",
      label: "Plaster Coverage Factor", 
      type: "number",
      min: 2.5,
      max: 4.0,
      step: 0.1,
      default: 3.0,
      description: "Multiplier for internal plaster area calculation"
    }
  ]
},
{
  title: "Opening Density",
  fields: [
    {
      name: "door_density",
      label: "Door Density",
      type: "number", 
      unit: "sqm per door",
      default: 200,
      description: "Square meters of built-up area per door"
    },
    {
      name: "window_density", 
      label: "Window Density",
      type: "number",
      unit: "sqm per window", 
      default: 150,
      description: "Square meters of built-up area per window"
    }
  ]
}
```

#### **4. OPENING SIZES**
**Location**: Admin Panel → Opening Standards (New Section)

**IS 1200:2012 Compliant Interface:**
```typescript
{
  title: "Door Dimensions (IS 1200:2012)",
  fields: [
    { name: "door_height", label: "Door Height", unit: "meters", default: 2.1 },
    { name: "door_width", label: "Door Width", unit: "meters", default: 1.0 }
  ]
},
{
  title: "Window Dimensions (IS 1200:2012)", 
  fields: [
    { name: "window_height", label: "Window Height", unit: "meters", default: 1.5 },
    { name: "window_width", label: "Window Width", unit: "meters", default: 1.2 }
  ]
}
```

## 🚀 **IMPLEMENTATION STEPS**

### **STEP 1: Database Setup**
1. Run the SQL scripts above to create new tables
2. Insert default values for all parameters
3. Update existing `engineering_standards` table

### **STEP 2: Admin Panel Interface**
1. Add new sections to admin panel navigation
2. Create forms for each parameter category
3. Add validation and IS code compliance checks

### **STEP 3: API Integration**
1. Update `fetchCalculationStandards()` function
2. Add new API endpoints for material rates and factors
3. Ensure backward compatibility with existing data

### **STEP 4: Testing**
1. Test all parameters are loaded correctly
2. Verify calculations use admin panel values
3. Test fallback values work when admin data is missing

## 🎯 **IMMEDIATE BENEFITS**

Once implemented, administrators will have **complete control** over:
- ✅ **Structural dimensions** (floor height, beam sizes, column sizes)
- ✅ **Material rates** (excavation, plaster, electrical, plumbing, waterproofing)  
- ✅ **Calculation factors** (coverage factors, opening densities)
- ✅ **Opening sizes** (IS 1200 compliant door/window dimensions)

**Result**: No code changes needed for rate updates or calculation adjustments!
