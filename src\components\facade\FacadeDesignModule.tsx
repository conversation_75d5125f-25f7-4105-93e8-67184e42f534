import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON>, <PERSON>, Layers, Save, AlertTriangle } from 'lucide-react'
import { Component, componentAPI, taskAPI } from '../../lib/supabase'

interface FacadeDesignModuleProps {
  isOpen: boolean
  onClose: () => void
  buildingData: {
    floors: number
    totalArea: number
    externalWallArea: number
  }
  onSave: (facadeConfig: any) => void
}

interface FacadeSection {
  id: string
  name: string
  area: number
  selectedComponent: Component | null
  position: { x: number; y: number; width: number; height: number }
}

export function FacadeDesignModule({ 
  isOpen, 
  onClose, 
  buildingData, 
  onSave 
}: FacadeDesignModuleProps) {
  const [facadeComponents, setFacadeComponents] = useState<Component[]>([])
  const [selectedComponent, setSelectedComponent] = useState<Component | null>(null)
  const [facadeSections, setFacadeSections] = useState<FacadeSection[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [activeView, setActiveView] = useState<'front' | 'side' | 'back'>('front')
  const [tasks, setTasks] = useState<any[]>([])
  const [showOverrideWarning, setShowOverrideWarning] = useState(false)

  useEffect(() => {
    if (isOpen) {
      loadFacadeComponents()
      loadTasks()
      initializeFacadeSections()
    }
  }, [isOpen])

  const loadFacadeComponents = async () => {
    setIsLoading(true)
    try {
      const components = await componentAPI.getAll('Facade')
      setFacadeComponents(components)
    } catch (error) {
      console.error('Error loading facade components:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadTasks = async () => {
    try {
      const allTasks = await taskAPI.getAll()
      setTasks(allTasks)
    } catch (error) {
      console.error('Error loading tasks:', error)
    }
  }

  const initializeFacadeSections = () => {
    // Create default facade sections based on building data
    const sections: FacadeSection[] = [
      {
        id: 'ground_floor',
        name: 'Ground Floor',
        area: buildingData.externalWallArea / buildingData.floors,
        selectedComponent: null,
        position: { x: 50, y: 200, width: 300, height: 80 }
      },
      {
        id: 'upper_floors',
        name: 'Upper Floors',
        area: buildingData.externalWallArea * (buildingData.floors - 1) / buildingData.floors,
        selectedComponent: null,
        position: { x: 50, y: 50, width: 300, height: 150 }
      },
      {
        id: 'accent_band',
        name: 'Accent Band',
        area: buildingData.externalWallArea * 0.1,
        selectedComponent: null,
        position: { x: 50, y: 180, width: 300, height: 20 }
      }
    ]
    setFacadeSections(sections)
  }

  const handleSectionClick = (sectionId: string) => {
    if (selectedComponent) {
      setFacadeSections(prev => prev.map(section => 
        section.id === sectionId 
          ? { ...section, selectedComponent }
          : section
      ))
    }
  }

  const calculateTotalCost = () => {
    let totalCost = 0
    
    facadeSections.forEach(section => {
      if (section.selectedComponent) {
        // Find associated task for full cost calculation
        const associatedTask = section.selectedComponent.associated_task_id
          ? tasks.find(t => t.id === section.selectedComponent?.associated_task_id)
          : null
          
        let sectionCost = section.area * section.selectedComponent.unit_price
        
        // Add labor and additional materials if task is found
        if (associatedTask && associatedTask.task_requirements) {
          // Calculate additional costs from task requirements
          associatedTask.task_requirements.forEach((req: any) => {
            if (req.requirement_type === 'labor') {
              // Add labor cost
              const laborRate = req.labor_rates?.rates?.better || 0
              sectionCost += section.area * laborRate * req.quantity_per_sqm
            } else if (req.requirement_type === 'material' && req.component_id !== section.selectedComponent?.id) {
              // Add supporting material cost (e.g., adhesive)
              const materialRate = req.components?.unit_price || 0
              sectionCost += section.area * materialRate * req.quantity_per_sqm
            }
          })
        }
        
        totalCost += sectionCost
      }
    })
    
    return totalCost
  }

  const handleSave = () => {
    const facadeConfig = {
      sections: facadeSections,
      totalCost: calculateTotalCost(),
      totalArea: buildingData.externalWallArea
    }
    onSave(facadeConfig)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-7xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-orange-50 to-red-50">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Facade Design Studio</h2>
            <p className="text-gray-600 mt-1">Design your building's exterior with premium finishes</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-200px)]">
          {/* 3D Building Preview */}
          <div className="flex-1 bg-gray-100 relative">
            {/* View Selector */}
            <div className="absolute top-4 left-4 z-10">
              <div className="bg-white rounded-lg shadow-lg p-2 flex gap-2">
                {['front', 'side', 'back'].map((view) => (
                  <button
                    key={view}
                    onClick={() => setActiveView(view as any)}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      activeView === view
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {view.charAt(0).toUpperCase() + view.slice(1)} View
                  </button>
                ))}
              </div>
            </div>

            {/* Building Elevation */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">
                  {activeView.charAt(0).toUpperCase() + activeView.slice(1)} Elevation
                </h3>
                
                <svg
                  viewBox="0 0 400 300"
                  className="w-96 h-72 border border-gray-200 rounded-lg"
                >
                  {/* Building outline */}
                  <rect
                    x="50"
                    y="50"
                    width="300"
                    height="200"
                    fill="#f9fafb"
                    stroke="#d1d5db"
                    strokeWidth="2"
                  />

                  {/* Facade sections */}
                  {facadeSections.map((section) => (
                    <g key={section.id}>
                      <rect
                        x={section.position.x}
                        y={section.position.y}
                        width={section.position.width}
                        height={section.position.height}
                        fill={section.selectedComponent ? '#dbeafe' : '#f3f4f6'}
                        stroke={section.selectedComponent ? '#3b82f6' : '#9ca3af'}
                        strokeWidth="1"
                        className="cursor-pointer hover:opacity-80 transition-opacity"
                        onClick={() => handleSectionClick(section.id)}
                      />
                      
                      {/* Section label */}
                      <text
                        x={section.position.x + section.position.width / 2}
                        y={section.position.y + section.position.height / 2}
                        textAnchor="middle"
                        dominantBaseline="middle"
                        className="text-xs font-medium fill-gray-600 pointer-events-none"
                      >
                        {section.name}
                      </text>

                      {/* Material indicator */}
                      {section.selectedComponent && (
                        <text
                          x={section.position.x + section.position.width / 2}
                          y={section.position.y + section.position.height / 2 + 12}
                          textAnchor="middle"
                          dominantBaseline="middle"
                          className="text-xs fill-blue-600 pointer-events-none"
                        >
                          {section.selectedComponent.name}
                        </text>
                      )}
                    </g>
                  ))}

                  {/* Windows (decorative) */}
                  {Array.from({ length: 6 }, (_, i) => (
                    <rect
                      key={i}
                      x={80 + (i % 3) * 80}
                      y={80 + Math.floor(i / 3) * 60}
                      width="40"
                      height="30"
                      fill="#e5e7eb"
                      stroke="#9ca3af"
                      strokeWidth="1"
                    />
                  ))}
                </svg>

                {/* Instructions */}
                <div className="mt-4 text-center">
                  <p className="text-sm text-gray-600">
                    {selectedComponent 
                      ? `Click on sections to apply ${selectedComponent.name}`
                      : 'Select a material from the panel to start designing'
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Cost Summary */}
            <div className="absolute bottom-4 right-4 bg-white rounded-lg shadow-lg p-4">
              <h4 className="font-semibold text-gray-800 mb-2">Facade Cost Summary</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Area:</span>
                  <span className="font-medium">{buildingData.externalWallArea.toFixed(0)} sq ft</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Estimated Cost:</span>
                  <span className="font-bold text-green-600">₹{calculateTotalCost().toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Materials Panel */}
          <div className="w-80 border-l border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Facade Materials</h3>
              <p className="text-sm text-gray-600">Select materials to apply to facade sections</p>
            </div>

            <div className="flex-1 overflow-y-auto p-4">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-2 border-orange-600 border-t-transparent"></div>
                </div>
              ) : (
                <div className="space-y-3">
                  {facadeComponents.map((component) => (
                    <div
                      key={component.id}
                      onClick={() => {
                        setSelectedComponent(component)
                        // Show warning if component has no associated task
                        if (!component.associated_task_id) {
                          setShowOverrideWarning(true)
                          setTimeout(() => setShowOverrideWarning(false), 5000)
                        }
                      }}
                      className={`border rounded-lg p-3 cursor-pointer transition-all ${
                        selectedComponent?.id === component.id
                          ? 'border-orange-500 bg-orange-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-800">{component.name}</h4>
                          <p className="text-sm text-gray-600">{component.brand}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-sm font-semibold text-green-600">
                              ₹{component.unit_price.toLocaleString()}/{component.unit}
                            </span>
                          </div>
                        </div>
                        {selectedComponent?.id === component.id && (
                          <Palette className="w-5 h-5 text-orange-600" />
                        )}
                      </div>

                      {component.image_url && (
                        <img 
                          src={component.image_url} 
                          alt={component.name}
                          className="w-full h-16 object-cover rounded mt-2"
                        />
                      )}

                      {/* Task information */}
                      {component.associated_task_id && (
                        <div className="mt-2 text-xs text-gray-500">
                          <div className="flex items-center gap-1">
                            <Layers className="w-3 h-3" />
                            <span>
                              {tasks.find(t => t.id === component.associated_task_id)?.name || 'Installation Task'}
                            </span>
                          </div>
                        </div>
                      )}

                      {/* Specifications */}
                      {component.specifications && Object.keys(component.specifications).length > 0 && (
                        <div className="mt-2 text-xs text-gray-500">
                          {Object.entries(component.specifications).slice(0, 2).map(([key, value]) => (
                            <div key={key} className="flex justify-between">
                              <span>{key}:</span>
                              <span>{value as string}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Warning for missing task */}
            {showOverrideWarning && (
              <div className="p-3 bg-yellow-50 border-t border-yellow-200">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5" />
                  <div className="text-xs text-yellow-700">
                    <p className="font-medium">Missing installation task</p>
                    <p>This material doesn't have an associated installation task. Only material cost will be calculated.</p>
                  </div>
                </div>
              </div>
            )}

            {/* Section Details */}
            <div className="border-t border-gray-200 p-4">
              <h4 className="font-semibold text-gray-800 mb-3">Facade Sections</h4>
              <div className="space-y-2">
                {facadeSections.map((section) => (
                  <div key={section.id} className="flex items-center justify-between text-sm">
                    <div>
                      <div className="font-medium text-gray-700">{section.name}</div>
                      <div className="text-gray-500">{section.area.toFixed(0)} sq ft</div>
                    </div>
                    <div className="text-right">
                      {section.selectedComponent ? (
                        <div>
                          <div className="font-medium text-green-600">
                            ₹{(section.area * section.selectedComponent.unit_price).toLocaleString()}
                          </div>
                          <div className="text-xs text-gray-500">
                            {section.selectedComponent.name}
                          </div>
                        </div>
                      ) : (
                        <div className="text-gray-400">Not selected</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Save Button */}
            <div className="border-t border-gray-200 p-4">
              <button
                onClick={handleSave}
                className="w-full bg-orange-600 hover:bg-orange-700 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                <Save className="w-4 h-4" />
                Apply Facade Design
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}