import { laborRateAPI } from './supabase';
import { EnhancedLaborRate } from './multiModalLaborCalculations';

export const enhancedLaborRatesData: Omit<EnhancedLaborRate, 'id' | 'created_at' | 'updated_at'>[] = [
  // ELECTRICAL WORK
  {
    name: 'Electrical Wiring (Conduit)',
    category: 'electrical',
    skill_level: 'skilled',
    unit: 'point',
    rates: { good: 150, better: 180, best: 220 },
    productivity: { output_per_day: 20, unit: 'points' },
    location: 'gurgaon',
    is_active: true,
    charging_models: {
      primary_model: 'per_unit',
      lump_sum_rates: {
        small_project: { good: 75000, better: 90000, best: 110000 },
        medium_project: { good: 140000, better: 170000, best: 210000 },
        large_project: { good: 280000, better: 340000, best: 420000 }
      },
      daily_rates: { good: 800, better: 1000, best: 1200 },
      per_sqft_rates: { good: 18, better: 22, best: 28 },
      recommended_for: {
        project_sizes: ['small', 'medium', 'large'],
        project_types: ['residential', 'commercial'],
        notes: 'Per point for residential, per sqft for commercial, lump sum for well-defined scope'
      }
    }
  },

  {
    name: 'Electrical Panel Installation',
    category: 'electrical',
    skill_level: 'specialist',
    unit: 'set',
    rates: { good: 2500, better: 3200, best: 4000 },
    productivity: { output_per_day: 2, unit: 'sets' },
    location: 'gurgaon',
    is_active: true,
    charging_models: {
      primary_model: 'per_unit',
      daily_rates: { good: 1200, better: 1500, best: 1800 },
      recommended_for: {
        project_sizes: ['medium', 'large'],
        project_types: ['residential', 'commercial'],
        notes: 'Specialist work requiring certified electricians'
      }
    }
  },

  // PLUMBING WORK
  {
    name: 'Plumbing Installation (Water Supply)',
    category: 'plumbing',
    skill_level: 'skilled',
    unit: 'point',
    rates: { good: 200, better: 250, best: 320 },
    productivity: { output_per_day: 12, unit: 'points' },
    location: 'gurgaon',
    is_active: true,
    charging_models: {
      primary_model: 'per_unit',
      lump_sum_rates: {
        small_project: { good: 45000, better: 60000, best: 80000 },
        medium_project: { good: 85000, better: 110000, best: 145000 },
        large_project: { good: 160000, better: 210000, best: 280000 }
      },
      daily_rates: { good: 700, better: 900, best: 1100 },
      per_sqft_rates: { good: 15, better: 20, best: 26 },
      recommended_for: {
        project_sizes: ['small', 'medium', 'large'],
        project_types: ['residential', 'commercial'],
        notes: 'Lump sum preferred for complete bathroom/kitchen packages'
      }
    }
  },

  {
    name: 'Drainage & Sewerage Installation',
    category: 'plumbing',
    skill_level: 'skilled',
    unit: 'point',
    rates: { good: 180, better: 220, best: 280 },
    productivity: { output_per_day: 10, unit: 'points' },
    location: 'gurgaon',
    is_active: true,
    charging_models: {
      primary_model: 'per_unit',
      lump_sum_rates: {
        small_project: { good: 35000, better: 45000, best: 60000 },
        medium_project: { good: 65000, better: 85000, best: 115000 },
        large_project: { good: 125000, better: 165000, best: 220000 }
      },
      daily_rates: { good: 650, better: 800, best: 1000 },
      recommended_for: {
        project_sizes: ['medium', 'large'],
        project_types: ['residential', 'commercial'],
        notes: 'Complex drainage work often benefits from daily rate pricing'
      }
    }
  },

  // MASONRY WORK
  {
    name: 'Brick Masonry (230mm)',
    category: 'masonry',
    skill_level: 'skilled',
    unit: 'sqm',
    rates: { good: 150, better: 180, best: 220 },
    productivity: { output_per_day: 8, unit: 'sqm' },
    location: 'gurgaon',
    is_active: true,
    charging_models: {
      primary_model: 'per_sqft',
      daily_rates: { good: 800, better: 1000, best: 1200 },
      lump_sum_rates: {
        small_project: { good: 85000, better: 105000, best: 130000 },
        medium_project: { good: 160000, better: 200000, best: 250000 },
        large_project: { good: 320000, better: 400000, best: 500000 }
      },
      recommended_for: {
        project_sizes: ['medium', 'large'],
        project_types: ['residential', 'commercial'],
        notes: 'Per sqft most common, daily rates for complex layouts'
      }
    }
  },

  {
    name: 'AAC Block Masonry',
    category: 'masonry',
    skill_level: 'skilled',
    unit: 'sqm',
    rates: { good: 80, better: 100, best: 125 },
    productivity: { output_per_day: 12, unit: 'sqm' },
    location: 'gurgaon',
    is_active: true,
    charging_models: {
      primary_model: 'per_sqft',
      daily_rates: { good: 700, better: 850, best: 1000 },
      lump_sum_rates: {
        small_project: { good: 55000, better: 70000, best: 90000 },
        medium_project: { good: 105000, better: 135000, best: 170000 },
        large_project: { good: 210000, better: 270000, best: 340000 }
      },
      recommended_for: {
        project_sizes: ['small', 'medium', 'large'],
        project_types: ['residential', 'commercial'],
        notes: 'AAC blocks faster installation, good for large areas'
      }
    }
  },

  // FINISHES WORK
  {
    name: 'Internal Plastering',
    category: 'finishes',
    skill_level: 'skilled',
    unit: 'sqm',
    rates: { good: 45, better: 55, best: 70 },
    productivity: { output_per_day: 25, unit: 'sqm' },
    location: 'gurgaon',
    is_active: true,
    charging_models: {
      primary_model: 'per_sqft',
      daily_rates: { good: 600, better: 750, best: 900 },
      lump_sum_rates: {
        small_project: { good: 35000, better: 45000, best: 60000 },
        medium_project: { good: 70000, better: 90000, best: 120000 },
        large_project: { good: 140000, better: 180000, best: 240000 }
      },
      recommended_for: {
        project_sizes: ['medium', 'large'],
        project_types: ['residential', 'commercial'],
        notes: 'Large areas benefit from lump sum pricing'
      }
    }
  },

  {
    name: 'External Plastering',
    category: 'finishes',
    skill_level: 'skilled',
    unit: 'sqm',
    rates: { good: 55, better: 68, best: 85 },
    productivity: { output_per_day: 20, unit: 'sqm' },
    location: 'gurgaon',
    is_active: true,
    charging_models: {
      primary_model: 'per_sqft',
      daily_rates: { good: 650, better: 800, best: 950 },
      recommended_for: {
        project_sizes: ['medium', 'large'],
        project_types: ['residential', 'commercial'],
        notes: 'Weather-dependent work, daily rates provide flexibility'
      }
    }
  },

  {
    name: 'Interior Painting',
    category: 'finishes',
    skill_level: 'semiskilled',
    unit: 'sqm',
    rates: { good: 35, better: 45, best: 60 },
    productivity: { output_per_day: 40, unit: 'sqm' },
    location: 'gurgaon',
    is_active: true,
    charging_models: {
      primary_model: 'per_sqft',
      daily_rates: { good: 500, better: 650, best: 800 },
      lump_sum_rates: {
        small_project: { good: 25000, better: 35000, best: 50000 },
        medium_project: { good: 50000, better: 70000, best: 100000 },
        large_project: { good: 100000, better: 140000, best: 200000 }
      },
      recommended_for: {
        project_sizes: ['small', 'medium', 'large'],
        project_types: ['residential', 'commercial'],
        notes: 'Lump sum common for complete house painting'
      }
    }
  },

  // FLOORING WORK
  {
    name: 'Vitrified Tile Installation',
    category: 'flooring',
    skill_level: 'skilled',
    unit: 'sqm',
    rates: { good: 100, better: 125, best: 160 },
    productivity: { output_per_day: 15, unit: 'sqm' },
    location: 'gurgaon',
    is_active: true,
    charging_models: {
      primary_model: 'per_sqft',
      daily_rates: { good: 700, better: 850, best: 1000 },
      lump_sum_rates: {
        small_project: { good: 45000, better: 60000, best: 80000 },
        medium_project: { good: 90000, better: 120000, best: 160000 },
        large_project: { good: 180000, better: 240000, best: 320000 }
      },
      recommended_for: {
        project_sizes: ['medium', 'large'],
        project_types: ['residential', 'commercial'],
        notes: 'Per sqft standard, lump sum for large uniform areas'
      }
    }
  },

  {
    name: 'Italian Marble Installation',
    category: 'flooring',
    skill_level: 'specialist',
    unit: 'sqm',
    rates: { good: 180, better: 220, best: 280 },
    productivity: { output_per_day: 8, unit: 'sqm' },
    location: 'gurgaon',
    is_active: true,
    charging_models: {
      primary_model: 'per_sqft',
      daily_rates: { good: 900, better: 1100, best: 1400 },
      recommended_for: {
        project_sizes: ['small', 'medium'],
        project_types: ['residential'],
        notes: 'Specialist work requiring experienced craftsmen'
      }
    }
  },

  // FOUNDATION WORK
  {
    name: 'Manual Excavation',
    category: 'foundation',
    skill_level: 'unskilled',
    unit: 'm3',
    rates: { good: 450, better: 550, best: 650 },
    productivity: { output_per_day: 3, unit: 'm3' },
    location: 'gurgaon',
    is_active: true,
    charging_models: {
      primary_model: 'per_unit',
      daily_rates: { good: 400, better: 500, best: 600 },
      lump_sum_rates: {
        small_project: { good: 35000, better: 45000, best: 60000 },
        medium_project: { good: 70000, better: 90000, best: 120000 },
        large_project: { good: 140000, better: 180000, best: 240000 }
      },
      recommended_for: {
        project_sizes: ['small', 'medium'],
        project_types: ['residential'],
        notes: 'Daily rates common for uncertain soil conditions'
      }
    }
  },

  {
    name: 'RCC Foundation Work',
    category: 'foundation',
    skill_level: 'skilled',
    unit: 'm3',
    rates: { good: 800, better: 1000, best: 1250 },
    productivity: { output_per_day: 2, unit: 'm3' },
    location: 'gurgaon',
    is_active: true,
    charging_models: {
      primary_model: 'per_unit',
      daily_rates: { good: 900, better: 1100, best: 1350 },
      recommended_for: {
        project_sizes: ['medium', 'large'],
        project_types: ['residential', 'commercial'],
        notes: 'Critical structural work requiring skilled supervision'
      }
    }
  }
];

export async function seedEnhancedLaborRates(): Promise<void> {
  console.log('Starting enhanced labor rates seeding...');
  
  try {
    let successCount = 0;
    let errorCount = 0;

    for (const rateData of enhancedLaborRatesData) {
      try {
        await laborRateAPI.create(rateData as any);
        successCount++;
        console.log(`✅ Added: ${rateData.name}`);
      } catch (error) {
        errorCount++;
        console.error(`❌ Failed to add ${rateData.name}:`, error);
      }
    }

    console.log(`\n🎉 Seeding completed!`);
    console.log(`✅ Successfully added: ${successCount} rates`);
    console.log(`❌ Failed to add: ${errorCount} rates`);
    
    if (errorCount > 0) {
      console.log('\n⚠️  Some rates failed to add. This might be due to:');
      console.log('   - Duplicate entries (rates with same name already exist)');
      console.log('   - Database constraints');
      console.log('   - Network issues');
    }

  } catch (error) {
    console.error('❌ Seeding failed:', error);
    throw error;
  }
}

// Helper function to update existing rates with enhanced models
export async function upgradeExistingRatesToEnhanced(): Promise<void> {
  console.log('Upgrading existing labor rates to enhanced models...');
  
  try {
    // This would fetch existing rates and add charging_models to them
    // Implementation would depend on your specific upgrade strategy
    console.log('⚠️  Upgrade function not yet implemented');
    console.log('   This would add charging_models to existing labor rates');
  } catch (error) {
    console.error('❌ Upgrade failed:', error);
    throw error;
  }
}
