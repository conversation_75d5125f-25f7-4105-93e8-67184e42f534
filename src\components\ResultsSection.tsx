import React, { useState, useEffect } from 'react';
import { CalculationResult, QualityTier, CostItem, RoomConfiguration, RoomMaterialSelection } from '../types/calculator';
import { ChevronDown, ChevronUp, Download, Share2, RotateCcw, Settings, Home, Star, FileText, GitCompare, Calculator, Eye, Lightbulb, AlertTriangle, Edit } from 'lucide-react';
import { ArchitectNote } from './ArchitectNote';
import { ItemBreakdown } from './ItemBreakdown';
import { CostHighlight } from './CostHighlight';
import { RoomPlanner } from './RoomPlanner';
import { RoomMaterialCustomizer } from './RoomMaterialCustomizer';
import { EstimateComparison } from './EstimateComparison';
import { InfoTooltip } from './InfoTooltip';
import { QualityTierAnimator } from './QualityTierAnimator';
import { UnifiedExpertGuidance } from './UnifiedExpertGuidance';
import { CalculationBreakdown } from './CalculationBreakdown';
import { SaveShareEstimate } from './SaveShareEstimate';
import { ProfessionalQuoteGenerator } from './ProfessionalQuoteGenerator';
import { ItemOverrideModal } from './ItemOverrideModal';
import { architectNotes } from '../data/architectNotes';
import { itemBreakdowns } from '../data/itemBreakdowns';
import { technicalTerms } from '../data/technicalTerms';
import { overrideAPI } from '../lib/supabase';

interface ResultsSectionProps {
  result: CalculationResult;
  inputs: any;
  onQualityTierChange: (tier: QualityTier) => void;
  onItemChange: (sectionId: string, itemId: string, updates: Partial<CostItem>) => void;
  onRoomConfigurationSave: (rooms: RoomConfiguration[]) => void;
  onMaterialCustomizationSave: (selections: RoomMaterialSelection[]) => void;
  onShowSummary: () => void;
  projectId?: string;
}

export function ResultsSection({ 
  result, 
  inputs,
  onQualityTierChange, 
  onItemChange, 
  onRoomConfigurationSave, 
  onMaterialCustomizationSave,
  onShowSummary,
  projectId
}: ResultsSectionProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [originalValues, setOriginalValues] = useState<Record<string, Record<string, Partial<CostItem>>>>({});
  const [highlightedItems, setHighlightedItems] = useState<Set<string>>(new Set());
  const [costChanges, setCostChanges] = useState<Record<string, { isIncrease: boolean; timestamp: number }>>({});
  const [previousTotalCost, setPreviousTotalCost] = useState(result.totalCost);
  const [showRoomPlanner, setShowRoomPlanner] = useState(false);
  const [showMaterialCustomizer, setShowMaterialCustomizer] = useState(false);
  const [showEstimateComparison, setShowEstimateComparison] = useState(false);
  const [animatingItems, setAnimatingItems] = useState<Set<string>>(new Set());
  const [showCalculationBreakdown, setShowCalculationBreakdown] = useState(false);
  const [selectedItemBreakdown, setSelectedItemBreakdown] = useState<{
    breakdown: any;
    itemName: string;
  } | null>(null);
  const [showSaveShare, setShowSaveShare] = useState(false);
  const [showProfessionalQuote, setShowProfessionalQuote] = useState(false);
  const [expertHighlightedItems, setExpertHighlightedItems] = useState<string[]>([]);
  const [inlineExpertNotes, setInlineExpertNotes] = useState<Record<string, any>>({});
  
  // V2.0 Override functionality
  const [showOverrideModal, setShowOverrideModal] = useState(false);
  const [selectedItemForOverride, setSelectedItemForOverride] = useState<{
    item: CostItem;
    sectionId: string;
  } | null>(null);
  const [itemOverrides, setItemOverrides] = useState<Record<string, {
    originalQuantity: number;
    originalRate: number;
    overrideQuantity?: number;
    overrideRate?: number;
    reason?: string;
  }>>({});
  const [isSavingOverride, setIsSavingOverride] = useState(false);

  // Store original values when result changes
  useEffect(() => {
    const original: Record<string, Record<string, Partial<CostItem>>> = {};
    result.sections.forEach(section => {
      original[section.id] = {};
      section.items.forEach(item => {
        original[section.id][item.id] = {
          quantity: item.quantity,
          rate: item.rate,
          selectedMaterial: item.selectedMaterial
        };
        
        // Store original values for override tracking
        if (!itemOverrides[item.id]) {
          setItemOverrides(prev => ({
            ...prev,
            [item.id]: {
              originalQuantity: item.quantity,
              originalRate: item.rate
            }
          }));
        }
      });
    });
    setOriginalValues(original);
  }, [result.qualityTier]);

  // Track cost changes for highlighting
  useEffect(() => {
    if (previousTotalCost !== result.totalCost) {
      const isIncrease = result.totalCost > previousTotalCost;
      setCostChanges(prev => ({
        ...prev,
        totalCost: { isIncrease, timestamp: Date.now() }
      }));
      setPreviousTotalCost(result.totalCost);
    }
  }, [result.totalCost, previousTotalCost]);

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const qualityTierLabels = {
    good: 'Good (Budget)',
    better: 'Better (Premium)',
    best: 'Best (Luxury)'
  };

  const handleQualityTierChange = (tier: QualityTier) => {
    // Trigger animation for affected items
    const affectedItems = new Set([
      'concrete_foundation',
      'concrete_structure', 
      'steel_work',
      'brickwork',
      'flooring',
      'interior_painting',
      'main_door',
      'windows',
      'electrical',
      'bathroom_fittings'
    ]);
    
    setAnimatingItems(affectedItems);
    
    // Remove animation after 2 seconds
    setTimeout(() => {
      setAnimatingItems(new Set());
    }, 2000);
    
    onQualityTierChange(tier);
  };

  const handleItemChange = (sectionId: string, itemId: string, updates: Partial<CostItem>) => {
    const currentItem = result.sections
      .find(s => s.id === sectionId)?.items
      .find(i => i.id === itemId);
    
    if (currentItem && !currentItem.isCalculated) {
      const oldTotal = currentItem.total;
      onItemChange(sectionId, itemId, updates);
      
      // Highlight the changed item
      const itemKey = `${sectionId}-${itemId}`;
      setHighlightedItems(prev => new Set([...prev, itemKey]));
      
      // Calculate if it's an increase or decrease
      const newTotal = (updates.quantity ?? currentItem.quantity) * (updates.rate ?? currentItem.rate);
      const isIncrease = newTotal > oldTotal;
      
      setCostChanges(prev => ({
        ...prev,
        [itemKey]: { isIncrease, timestamp: Date.now() }
      }));
      
      // Remove highlight after animation
      setTimeout(() => {
        setHighlightedItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(itemKey);
          return newSet;
        });
      }, 2500);
    }

    // Trigger inline expert guidance evaluation
    evaluateInlineExpertGuidance(sectionId, itemId, updates);
  };

  const handleItemOverrideClick = (item: CostItem, sectionId: string) => {
    setSelectedItemForOverride({ item, sectionId });
    setShowOverrideModal(true);
  };

  const handleOverrideSave = async (overrideData: any) => {
    if (!selectedItemForOverride) return;

    const { item, sectionId } = selectedItemForOverride;
    
    // Update local override tracking
    setItemOverrides(prev => ({
      ...prev,
      [item.id]: {
        ...prev[item.id],
        overrideQuantity: overrideData.overrideQuantity,
        overrideRate: overrideData.overrideRate,
        reason: overrideData.reason
      }
    }));

    // Apply the override to the item
    const updates: Partial<CostItem> = {};
    if (overrideData.overrideQuantity !== undefined) {
      updates.quantity = overrideData.overrideQuantity;
    }
    if (overrideData.overrideRate !== undefined) {
      updates.rate = overrideData.overrideRate;
    }

    if (Object.keys(updates).length > 0) {
      handleItemChange(sectionId, item.id, updates);
    }

    // Persist override to Supabase if projectId is available
    if (projectId) {
      setIsSavingOverride(true);
      try {
        await overrideAPI.create({
          project_id: projectId,
          component_id: item.id, // Using item.id as component_id for simplicity
          original_quantity: itemOverrides[item.id].originalQuantity,
          override_quantity: overrideData.overrideQuantity,
          original_rate: itemOverrides[item.id].originalRate,
          override_rate: overrideData.overrideRate,
          override_reason: overrideData.reason
        });
      } catch (error) {
        console.error('Error saving override to Supabase:', error);
        alert('Failed to save override to database. The override will apply to your current session only.');
      } finally {
        setIsSavingOverride(false);
      }
    }

    setShowOverrideModal(false);
    setSelectedItemForOverride(null);
  };

  const evaluateInlineExpertGuidance = (sectionId: string, itemId: string, updates: Partial<CostItem>) => {
    // Generate contextual expert notes based on material selection
    if (updates.selectedMaterial) {
      const material = updates.selectedMaterial;
      let note = null;

      // AAC Blocks guidance
      if (material.includes('AAC')) {
        note = {
          type: 'suggestion',
          icon: 'lightbulb',
          title: 'AAC Blocks Selected',
          message: 'Excellent choice! AAC blocks provide superior thermal insulation and can reduce AC costs by 20-30%. Ensure skilled labor for proper installation with adhesive mortar.'
        };
      }

      // UPVC Windows guidance
      if (material.includes('UPVC')) {
        note = {
          type: 'recommendation',
          icon: 'lightbulb',
          title: 'UPVC Windows - Great for Gurgaon',
          message: 'Perfect for Gurgaon\'s extreme climate. UPVC windows provide excellent insulation and can reduce energy costs by 15-25%.'
        };
      }

      // Premium material with budget tier warning
      if ((material.includes('Premium') || material.includes('Luxury')) && result.qualityTier === 'good') {
        note = {
          type: 'warning',
          icon: 'alert-triangle',
          title: 'Quality Tier Mismatch',
          message: 'You\'ve selected a premium material but are on the "Good" quality tier. Consider upgrading to "Better" or "Best" for consistent quality.'
        };
      }

      if (note) {
        setInlineExpertNotes(prev => ({
          ...prev,
          [itemId]: note
        }));
      } else {
        setInlineExpertNotes(prev => {
          const newNotes = { ...prev };
          delete newNotes[itemId];
          return newNotes;
        });
      }
    }
  };

  const resetSectionToDefaults = (sectionId: string) => {
    const section = result.sections.find(s => s.id === sectionId);
    if (section && originalValues[sectionId]) {
      section.items.forEach(item => {
        if (!item.isCalculated) {
          const originalItem = originalValues[sectionId][item.id];
          if (originalItem) {
            onItemChange(sectionId, item.id, originalItem);
          }
        }
      });
    }
  };

  const handleShowCalculationBreakdown = (item: CostItem) => {
    if (item.calculationBreakdown) {
      setSelectedItemBreakdown({
        breakdown: item.calculationBreakdown,
        itemName: item.name
      });
      setShowCalculationBreakdown(true);
    }
  };

  const handleExpertHighlight = (itemIds: string[]) => {
    setExpertHighlightedItems(itemIds);
    
    // Auto-expand sections containing highlighted items
    const sectionsToExpand = new Set<string>();
    result.sections.forEach(section => {
      if (section.items.some(item => itemIds.includes(item.id))) {
        sectionsToExpand.add(section.id);
      }
    });
    setExpandedSections(prev => new Set([...prev, ...sectionsToExpand]));

    // Apply visual highlighting with CSS classes
    itemIds.forEach(itemId => {
      const elements = document.querySelectorAll(`[data-item-id="${itemId}"]`);
      elements.forEach(element => {
        element.classList.add('border-2', 'border-red-500', 'animate-pulse', 'bg-red-50');
      });
    });

    // Remove highlighting after 5 seconds
    setTimeout(() => {
      setExpertHighlightedItems([]);
      itemIds.forEach(itemId => {
        const elements = document.querySelectorAll(`[data-item-id="${itemId}"]`);
        elements.forEach(element => {
          element.classList.remove('border-2', 'border-red-500', 'animate-pulse', 'bg-red-50');
        });
      });
    }, 5000);
  };

  const getArchitectNote = (itemId: string) => {
    return architectNotes[itemId];
  };

  const getItemBreakdown = (itemId: string) => {
    return itemBreakdowns[itemId];
  };

  const getTechnicalTerm = (itemId: string) => {
    return technicalTerms[itemId];
  };

  const RecommendationBadge = ({ item }: { item: CostItem }) => {
    if (!item.isRecommended) return null;
    
    return (
      <div className="group relative">
        <div className="flex items-center gap-1">
          <Star className="w-3 h-3 text-yellow-500 fill-current" aria-hidden="true" />
          <span className="text-xs text-yellow-600 font-medium">Recommended</span>
        </div>
        
        {item.recommendationNote && (
          <div className="absolute bottom-full left-0 mb-2 w-80 bg-gray-800 text-white rounded-lg shadow-lg p-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50" role="tooltip">
            <div className="text-xs font-semibold mb-1">Architect's Recommendation</div>
            <div className="text-xs leading-relaxed">{item.recommendationNote}</div>
            <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-800"></div>
          </div>
        )}
      </div>
    );
  };

  const InlineExpertNote = ({ itemId }: { itemId: string }) => {
    const note = inlineExpertNotes[itemId];
    if (!note) return null;

    const IconComponent = note.icon === 'lightbulb' ? Lightbulb : AlertTriangle;
    const colorClass = note.type === 'warning' ? 'text-orange-500' : 'text-blue-500';

    return (
      <div className="group relative">
        <div className="flex items-center gap-1 cursor-help">
          <IconComponent className={`w-4 h-4 ${colorClass}`} />
          <span className={`text-xs font-medium ${colorClass}`}>AI Insight</span>
        </div>
        
        <div className="absolute bottom-full left-0 mb-2 w-80 bg-gray-800 text-white rounded-lg shadow-lg p-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
          <div className="text-xs font-semibold mb-1">{note.title}</div>
          <div className="text-xs leading-relaxed">{note.message}</div>
          <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-800"></div>
        </div>
      </div>
    );
  };

  const OverrideIndicator = ({ item }: { item: CostItem }) => {
    const override = itemOverrides[item.id];
    if (!override || (!override.overrideQuantity && !override.overrideRate)) return null;

    const hasQuantityOverride = override.overrideQuantity !== undefined && override.overrideQuantity !== override.originalQuantity;
    const hasRateOverride = override.overrideRate !== undefined && override.overrideRate !== override.originalRate;

    return (
      <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-center gap-2 mb-1">
          <AlertTriangle className="w-4 h-4 text-yellow-600" />
          <span className="text-xs font-medium text-yellow-800">Manual Override Applied</span>
        </div>
        
        {hasQuantityOverride && (
          <div className="text-xs text-yellow-700">
            Quantity: <span className="line-through">{override.originalQuantity}</span> → <span className="font-semibold">{override.overrideQuantity}</span>
          </div>
        )}
        
        {hasRateOverride && (
          <div className="text-xs text-yellow-700">
            Rate: <span className="line-through">₹{override.originalRate}</span> → <span className="font-semibold">₹{override.overrideRate}</span>
          </div>
        )}
        
        {override.reason && (
          <div className="text-xs text-yellow-600 mt-1">
            Reason: {override.reason}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Unified Expert Guidance - Summary */}
      <UnifiedExpertGuidance 
        result={result} 
        roomSelections={result.roomMaterialSelections}
        context="summary"
        onHighlightItems={handleExpertHighlight}
      />

      {/* Hero Summary Section */}
      <section className="bg-gradient-to-br from-blue-600 to-blue-800 text-white rounded-xl p-6 sm:p-8" aria-labelledby="cost-summary-heading">
        <div className="text-center">
          <CostHighlight
            shouldHighlight={!!costChanges.totalCost}
            isIncrease={costChanges.totalCost?.isIncrease ?? false}
            className="inline-block rounded-lg px-4 py-2"
          >
            <h3 id="cost-summary-heading" className="text-3xl sm:text-4xl md:text-5xl font-bold mb-2">
              {formatCurrency(result.totalCost)}
            </h3>
          </CostHighlight>
          <p className="text-blue-100 text-base sm:text-lg mb-4 sm:mb-6">
            (Approximately {formatCurrency(result.ratePerSqft)} / Sq. Ft.)
          </p>
          
          {/* Quality Tier Selector with Animation */}
          <fieldset className="inline-flex bg-white/10 backdrop-blur-sm rounded-xl p-1 mb-4 sm:mb-6">
            <legend className="sr-only">Select Quality Tier</legend>
            {(Object.keys(qualityTierLabels) as QualityTier[]).map((tier) => (
              <button
                key={tier}
                onClick={() => handleQualityTierChange(tier)}
                className={`px-4 sm:px-6 py-2 sm:py-3 rounded-lg font-medium transition-all text-sm sm:text-base min-h-[44px] transform hover:scale-105 ${
                  result.qualityTier === tier
                    ? 'bg-white text-blue-600 shadow-lg scale-105'
                    : 'text-white hover:bg-white/10 focus:bg-white/10'
                } focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600`}
                aria-pressed={result.qualityTier === tier}
              >
                {qualityTierLabels[tier]}
              </button>
            ))}
          </fieldset>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
            <button
              onClick={() => setShowRoomPlanner(true)}
              className="flex items-center justify-center gap-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white px-4 sm:px-6 py-3 rounded-lg font-medium transition-all text-sm sm:text-base min-h-[44px] focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 border border-white/20 hover:border-white/30"
            >
              <Home className="w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
              Customize Rooms
            </button>
            <button
              onClick={() => setShowMaterialCustomizer(true)}
              className="flex items-center justify-center gap-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white px-4 sm:px-6 py-3 rounded-lg font-medium transition-all text-sm sm:text-base min-h-[44px] focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 border border-white/20 hover:border-white/30"
            >
              <Settings className="w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
              Customize by Room
            </button>
            <button
              onClick={() => setShowEstimateComparison(true)}
              className="flex items-center justify-center gap-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white px-4 sm:px-6 py-3 rounded-lg font-medium transition-all text-sm sm:text-base min-h-[44px] focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 border border-white/20 hover:border-white/30"
            >
              <GitCompare className="w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
              Compare Estimates
            </button>
            <button
              onClick={onShowSummary}
              className="flex items-center justify-center gap-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white px-4 sm:px-6 py-3 rounded-lg font-medium transition-all text-sm sm:text-base min-h-[44px] focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 border border-white/20 hover:border-white/30"
            >
              <FileText className="w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
              View Summary
            </button>
            <button
              onClick={() => setShowSaveShare(true)}
              className="flex items-center justify-center gap-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white px-4 sm:px-6 py-3 rounded-lg font-medium transition-all text-sm sm:text-base min-h-[44px] focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 border border-white/20 hover:border-white/30"
            >
              <Share2 className="w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
              Save & Share
            </button>
            <button
              onClick={() => setShowProfessionalQuote(true)}
              className="flex items-center justify-center gap-2 bg-orange-500 hover:bg-orange-600 text-white px-4 sm:px-6 py-3 rounded-lg font-medium transition-all text-sm sm:text-base min-h-[44px] focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 border border-orange-600 hover:border-orange-700"
            >
              <Download className="w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
              Professional Quote
            </button>
          </div>
        </div>
      </section>

      {/* Detailed Breakdown Accordion */}
      <div className="space-y-3 sm:space-y-4">
        {result.sections.map((section) => (
          <section key={section.id} className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200">
            <button
              onClick={() => toggleSection(section.id)}
              className="w-full px-4 sm:px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset border-b border-gray-100"
              aria-expanded={expandedSections.has(section.id)}
              aria-controls={`section-${section.id}-content`}
            >
              <div>
                <h4 className="text-base sm:text-lg font-semibold text-gray-800">
                  {section.title}
                </h4>
                <p className="text-sm sm:text-base text-gray-600">
                  Sub-total: {formatCurrency(section.subtotal)}
                </p>
              </div>
              {expandedSections.has(section.id) ? (
                <ChevronUp className="w-5 h-5 text-gray-400" aria-hidden="true" />
              ) : (
                <ChevronDown className="w-5 h-5 text-gray-400" aria-hidden="true" />
              )}
            </button>

            {expandedSections.has(section.id) && (
              <div id={`section-${section.id}-content`} className="px-4 sm:px-6 pb-4 sm:pb-6">
                {/* Section Controls */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
                  <button
                    onClick={() => resetSectionToDefaults(section.id)}
                    className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 transition-colors ml-auto focus:outline-none focus:ring-2 focus:ring-gray-500 rounded px-2 py-1 min-h-[44px] border-2 border-gray-200 hover:border-gray-300"
                  >
                    <RotateCcw className="w-4 h-4" aria-hidden="true" />
                    Reset to Defaults
                  </button>
                </div>

                <div className="overflow-x-auto">
                  {/* Table with responsive design */}
                  <div className="min-w-full">
                    {/* Header */}
                    <div className="hidden sm:grid sm:grid-cols-12 gap-4 text-left text-sm text-gray-600 border-b pb-3 mb-4 font-medium">
                      <div className="col-span-4">Item</div>
                      <div className="col-span-2 text-center">Quantity</div>
                      <div className="col-span-2 text-center">Rate</div>
                      <div className="col-span-2 text-right">Total Cost</div>
                      <div className="col-span-2 text-center">Actions</div>
                    </div>

                    {/* Data Rows */}
                    {section.items.map((item) => {
                      const itemKey = `${section.id}-${item.id}`;
                      const shouldHighlight = highlightedItems.has(itemKey) || 
                                            animatingItems.has(item.id) || 
                                            expertHighlightedItems.includes(item.id);
                      const costChange = costChanges[itemKey];
                      const architectNote = getArchitectNote(item.id);
                      const itemBreakdown = getItemBreakdown(item.id);
                      const technicalTerm = getTechnicalTerm(item.id);

                      return (
                        <CostHighlight
                          key={item.id}
                          shouldHighlight={shouldHighlight}
                          isIncrease={costChange?.isIncrease ?? false}
                          className={`border-b border-gray-100 last:border-b-0 transition-all duration-500 ${
                            expertHighlightedItems.includes(item.id) ? 'ring-2 ring-yellow-400 bg-yellow-50' : ''
                          }`}
                        >
                          <div data-item-id={item.id}>
                            {/* Mobile Layout */}
                            <div className="sm:hidden py-4 space-y-3">
                              <div>
                                <div className="flex items-center gap-2 mb-1">
                                  <div className="font-medium text-gray-800 text-sm">
                                    {item.name}
                                  </div>
                                  {technicalTerm && (
                                    <InfoTooltip
                                      title={technicalTerm.title}
                                      content={technicalTerm.explanation}
                                    />
                                  )}
                                  {itemBreakdown && (
                                    <ItemBreakdown
                                      title={itemBreakdown.title}
                                      breakdown={itemBreakdown.breakdown}
                                    />
                                  )}
                                  {item.roomCustomizable && (
                                    <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                                      Room Customizable
                                    </span>
                                  )}
                                  {item.isCalculated && (
                                    <span className="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">
                                      Auto-Calculated
                                    </span>
                                  )}
                                  {/* V2.0 CRITICAL: Edit Icon for Override */}
                                  <button
                                    onClick={() => handleItemOverrideClick(item, section.id)}
                                    className="p-1 hover:bg-gray-100 rounded transition-colors"
                                    title="Override pricing"
                                  >
                                    <Edit className="w-3 h-3 text-gray-500" />
                                  </button>
                                </div>
                                
                                <RecommendationBadge item={item} />
                                <InlineExpertNote itemId={item.id} />
                                
                                {item.materialOptions && !item.isCalculated && (
                                  <div className="mt-2 space-y-2">
                                    <label htmlFor={`material-${item.id}`} className="sr-only">
                                      Select material for {item.name}
                                    </label>
                                    <select
                                      id={`material-${item.id}`}
                                      value={item.selectedMaterial || item.materialOptions[0]}
                                      onChange={(e) => handleItemChange(section.id, item.id, { selectedMaterial: e.target.value })}
                                      className="enhanced-dropdown w-full"
                                    >
                                      {item.materialOptions.map((option) => (
                                        <option key={option} value={option} className="dropdown-option-visible">
                                          {option}
                                        </option>
                                      ))}
                                    </select>
                                    
                                    {architectNote && (
                                      <ArchitectNote
                                        title={architectNote.title}
                                        content={architectNote.content}
                                      />
                                    )}
                                  </div>
                                )}

                                {/* V2.0 CRITICAL: Override Indicator */}
                                <OverrideIndicator item={item} />

                                {/* Inline Expert Guidance */}
                                <UnifiedExpertGuidance 
                                  result={result} 
                                  roomSelections={result.roomMaterialSelections}
                                  currentItem={item}
                                  context="inline"
                                  itemId={item.id}
                                  sectionId={section.id}
                                />
                              </div>

                              <div className="grid grid-cols-3 gap-4 text-sm">
                                <div>
                                  <label htmlFor={`quantity-${item.id}`} className="block text-xs text-gray-500 mb-1">
                                    Quantity
                                  </label>
                                  <div className="flex items-center space-x-1">
                                    {item.isCalculated ? (
                                      <span className="w-full px-2 py-1 bg-gray-100 border-2 border-gray-200 rounded-lg text-sm text-gray-600">
                                        {item.quantity.toFixed(2)}
                                      </span>
                                    ) : (
                                      <input
                                        id={`quantity-${item.id}`}
                                        type="number"
                                        value={item.quantity}
                                        onChange={(e) => handleItemChange(section.id, item.id, { quantity: Number(e.target.value) })}
                                        className="w-full px-2 py-1 border-2 border-gray-200 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                      />
                                    )}
                                    <span className="text-xs text-gray-500 whitespace-nowrap">{item.unit}</span>
                                  </div>
                                </div>

                                <div>
                                  <label htmlFor={`rate-${item.id}`} className="block text-xs text-gray-500 mb-1">
                                    Rate
                                  </label>
                                  {item.isCalculated ? (
                                    <span className="w-full px-2 py-1 bg-gray-100 border-2 border-gray-200 rounded-lg text-sm text-gray-600">
                                      ₹{item.rate.toFixed(2)}
                                    </span>
                                  ) : (
                                    <input
                                      id={`rate-${item.id}`}
                                      type="number"
                                      value={item.rate}
                                      onChange={(e) => handleItemChange(section.id, item.id, { rate: Number(e.target.value) })}
                                      className="w-full px-2 py-1 border-2 border-gray-200 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                    />
                                  )}
                                </div>

                                <div>
                                  <div className="block text-xs text-gray-500 mb-1">Total Cost</div>
                                  <div className="font-medium text-gray-800 text-sm">
                                    {formatCurrency(item.total)}
                                  </div>
                                </div>
                              </div>

                              {/* Calculation Breakdown Button for Mobile */}
                              {item.calculationBreakdown && (
                                <div className="mt-2">
                                  <button
                                    onClick={() => handleShowCalculationBreakdown(item)}
                                    className="flex items-center gap-2 text-xs text-blue-600 hover:text-blue-800 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 rounded px-2 py-1 border-2 border-blue-200 hover:border-blue-300"
                                  >
                                    <Calculator className="w-3 h-3" />
                                    See Calculation
                                  </button>
                                </div>
                              )}
                            </div>

                            {/* Desktop Layout */}
                            <div className="hidden sm:grid sm:grid-cols-12 gap-4 py-4 items-center">
                              {/* Item Column */}
                              <div className="col-span-4">
                                <div>
                                  <div className="flex items-center gap-2 mb-1">
                                    <div className="font-medium text-gray-800">
                                      {item.name}
                                    </div>
                                    {technicalTerm && (
                                      <InfoTooltip
                                        title={technicalTerm.title}
                                        content={technicalTerm.explanation}
                                      />
                                    )}
                                    {itemBreakdown && (
                                      <ItemBreakdown
                                        title={itemBreakdown.title}
                                        breakdown={itemBreakdown.breakdown}
                                      />
                                    )}
                                    {item.roomCustomizable && (
                                      <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                                        Room Customizable
                                      </span>
                                    )}
                                    {item.isCalculated && (
                                      <span className="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">
                                        Auto-Calculated
                                      </span>
                                    )}
                                    {/* V2.0 CRITICAL: Edit Icon for Override */}
                                    <button
                                      onClick={() => handleItemOverrideClick(item, section.id)}
                                      className="p-1 hover:bg-gray-100 rounded transition-colors"
                                      title="Override pricing"
                                    >
                                      <Edit className="w-3 h-3 text-gray-500" />
                                    </button>
                                  </div>
                                  
                                  <RecommendationBadge item={item} />
                                  <InlineExpertNote itemId={item.id} />
                                  
                                  {item.materialOptions && !item.isCalculated && (
                                    <div className="mt-2 space-y-2">
                                      <label htmlFor={`material-desktop-${item.id}`} className="sr-only">
                                        Select material for {item.name}
                                      </label>
                                      <select
                                        id={`material-desktop-${item.id}`}
                                        value={item.selectedMaterial || item.materialOptions[0]}
                                        onChange={(e) => handleItemChange(section.id, item.id, { selectedMaterial: e.target.value })}
                                        className="enhanced-dropdown max-w-full"
                                      >
                                        {item.materialOptions.map((option) => (
                                          <option key={option} value={option} className="dropdown-option-visible">
                                            {option}
                                          </option>
                                        ))}
                                      </select>
                                      
                                      {architectNote && (
                                        <ArchitectNote
                                          title={architectNote.title}
                                          content={architectNote.content}
                                        />
                                      )}
                                    </div>
                                  )}

                                  {/* V2.0 CRITICAL: Override Indicator */}
                                  <OverrideIndicator item={item} />

                                  {/* Inline Expert Guidance */}
                                  <UnifiedExpertGuidance 
                                    result={result} 
                                    roomSelections={result.roomMaterialSelections}
                                    currentItem={item}
                                    context="inline"
                                    itemId={item.id}
                                    sectionId={section.id}
                                  />
                                </div>
                              </div>

                              {/* Quantity Column */}
                              <div className="col-span-2">
                                <div className="flex items-center justify-center space-x-2">
                                  <label htmlFor={`quantity-desktop-${item.id}`} className="sr-only">
                                    Quantity for {item.name}
                                  </label>
                                  {item.isCalculated ? (
                                    <span className="w-16 px-2 py-1 bg-gray-100 border-2 border-gray-200 rounded-lg text-sm text-center text-gray-600">
                                      {item.quantity.toFixed(2)}
                                    </span>
                                  ) : (
                                    <input
                                      id={`quantity-desktop-${item.id}`}
                                      type="number"
                                      value={item.quantity}
                                      onChange={(e) => handleItemChange(section.id, item.id, { quantity: Number(e.target.value) })}
                                      className="w-16 px-2 py-1 border-2 border-gray-200 rounded-lg text-sm text-center focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                    />
                                  )}
                                  <span className="text-sm text-gray-500 whitespace-nowrap">{item.unit}</span>
                                </div>
                              </div>

                              {/* Rate Column */}
                              <div className="col-span-2 text-center">
                                <label htmlFor={`rate-desktop-${item.id}`} className="sr-only">
                                  Rate for {item.name}
                                </label>
                                {item.isCalculated ? (
                                  <span className="w-20 px-2 py-1 bg-gray-100 border-2 border-gray-200 rounded-lg text-sm text-center text-gray-600">
                                    ₹{item.rate.toFixed(2)}
                                  </span>
                                ) : (
                                  <input
                                    id={`rate-desktop-${item.id}`}
                                    type="number"
                                    value={item.rate}
                                    onChange={(e) => handleItemChange(section.id, item.id, { rate: Number(e.target.value) })}
                                    className="w-20 px-2 py-1 border-2 border-gray-200 rounded-lg text-sm text-center focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                  />
                                )}
                              </div>

                              {/* Total Cost Column */}
                              <div className="col-span-2 font-medium text-gray-800 text-right">
                                {formatCurrency(item.total)}
                              </div>

                              {/* Actions Column */}
                              <div className="col-span-2 text-center">
                                {item.calculationBreakdown && (
                                  <button
                                    onClick={() => handleShowCalculationBreakdown(item)}
                                    className="flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 rounded px-2 py-1 border-2 border-blue-200 hover:border-blue-300 mx-auto"
                                    title="View detailed engineering calculation breakdown"
                                  >
                                    <Calculator className="w-3 h-3" />
                                    <span className="hidden lg:inline">See Calculation</span>
                                    <span className="lg:hidden">Details</span>
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>
                        </CostHighlight>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}
          </section>
        ))}
      </div>

      {/* Room Planner Modal */}
      <RoomPlanner
        isOpen={showRoomPlanner}
        onClose={() => setShowRoomPlanner(false)}
        totalBuiltUpArea={result.quantities.totalBuiltUpArea}
        initialRooms={result.quantities.roomConfiguration}
        onSave={onRoomConfigurationSave}
      />

      {/* Room Material Customizer Modal */}
      <RoomMaterialCustomizer
        isOpen={showMaterialCustomizer}
        onClose={() => setShowMaterialCustomizer(false)}
        rooms={result.quantities.roomConfiguration}
        materialSelections={result.roomMaterialSelections}
        onSave={onMaterialCustomizationSave}
      />

      {/* Estimate Comparison Modal */}
      <EstimateComparison
        currentResult={result}
        currentInputs={{
          plotSize: result.quantities.groundCoverageArea * (100 / 80), // Approximate plot size
          numberOfFloors: Math.ceil(result.quantities.totalBuiltUpArea / result.quantities.groundCoverageArea),
          constructionPercentage: 80,
          hasBasement: result.quantities.basementArea > 0,
          hasStiltParking: result.quantities.stiltArea > 0,
          location: result.location
        }}
        isOpen={showEstimateComparison}
        onClose={() => setShowEstimateComparison(false)}
      />

      {/* Calculation Breakdown Modal */}
      {selectedItemBreakdown && (
        <CalculationBreakdown
          isOpen={showCalculationBreakdown}
          onClose={() => {
            setShowCalculationBreakdown(false);
            setSelectedItemBreakdown(null);
          }}
          breakdown={selectedItemBreakdown.breakdown}
          itemName={selectedItemBreakdown.itemName}
        />
      )}

      {/* V2.0 CRITICAL: Item Override Modal */}
      {selectedItemForOverride && (
        <ItemOverrideModal
          isOpen={showOverrideModal}
          onClose={() => {
            setShowOverrideModal(false);
            setSelectedItemForOverride(null);
          }}
          item={selectedItemForOverride.item}
          originalData={itemOverrides[selectedItemForOverride.item.id]}
          onSave={handleOverrideSave}
          isSaving={isSavingOverride}
        />
      )}

      {/* Save & Share Modal */}
      <SaveShareEstimate
        result={result}
        inputs={inputs}
        isOpen={showSaveShare}
        onClose={() => setShowSaveShare(false)}
      />

      {/* Professional Quote Generator Modal */}
      <ProfessionalQuoteGenerator
        result={result}
        inputs={inputs}
        isOpen={showProfessionalQuote}
        onClose={() => setShowProfessionalQuote(false)}
      />
    </div>
  );
}