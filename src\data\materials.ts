export interface Material {
  id: string;
  category: string;
  name: string;
  unit: string;
  rates: {
    good: number;
    better: number;
    best: number;
  };
  wastagePercentage: number;
  brands: {
    good: string;
    better: string;
    best: string;
  };
  specifications: {
    good: string;
    better: string;
    best: string;
  };
}

export const materials: Material[] = [
  // Cement - Based on comprehensive research data
  {
    id: 'cement',
    category: 'foundation',
    name: 'Cement',
    unit: 'bag',
    rates: { good: 305, better: 345, best: 380 }, // Prism, Ambuja, UltraTech
    wastagePercentage: 2.5,
    brands: { good: 'Prism Cement', better: 'Ambuja Compocem', best: 'UltraTech Weather Plus' },
    specifications: { good: 'PPC', better: 'PPC', best: 'PPC Premium' }
  },
  
  // TMT Steel - Based on research data
  {
    id: 'steel',
    category: 'foundation',
    name: 'TMT Steel Bars',
    unit: 'kg',
    rates: { good: 46, better: 48, best: 52 }, // Kamdhenu, Jindal, Tata Tiscon
    wastagePercentage: 4,
    brands: { good: 'Kamdhenu TMT', better: 'Jindal Panther', best: 'Tata Tiscon' },
    specifications: { good: 'Fe 500D', better: 'Fe 500D', best: 'Fe 500D' }
  },
  
  // Sand - Based on Delhi NCR rates
  {
    id: 'sand',
    category: 'foundation',
    name: 'River Sand',
    unit: 'cft',
    rates: { good: 45, better: 50, best: 55 },
    wastagePercentage: 8,
    brands: { good: 'Local Supplier', better: 'Badarpur Sand', best: 'Yamuna Fine Sand' },
    specifications: { good: 'Coarse Sand', better: 'Fine Sand', best: 'Washed Sand' }
  },
  
  // Aggregate
  {
    id: 'aggregate',
    category: 'foundation',
    name: 'Coarse Aggregate',
    unit: 'cft',
    rates: { good: 55, better: 60, best: 65 },
    wastagePercentage: 5,
    brands: { good: 'Local Crusher', better: 'Graded Aggregate', best: 'Premium Aggregate' },
    specifications: { good: '20mm Stone', better: '20mm Graded', best: '20mm Premium' }
  },
  
  // Bricks - Based on research data
  {
    id: 'bricks',
    category: 'masonry',
    name: 'Bricks',
    unit: 'piece',
    rates: { good: 8, better: 10, best: 12 },
    wastagePercentage: 5,
    brands: { good: 'Local Kiln', better: 'Class A Red Brick', best: 'Premium Red Brick' },
    specifications: { good: '3rd Class', better: '2nd Class', best: '1st Class' }
  },
  
  // AAC Blocks
  {
    id: 'aac_blocks',
    category: 'masonry',
    name: 'AAC Blocks',
    unit: 'cft',
    rates: { good: 75, better: 85, best: 95 },
    wastagePercentage: 3,
    brands: { good: 'Local AAC', better: 'Siporex', best: 'Magicrete' },
    specifications: { good: '4 inch', better: '6 inch', best: '8 inch' }
  },
  
  // Tiles - Based on comprehensive research
  {
    id: 'flooring_tiles',
    category: 'finishes',
    name: 'Vitrified Tiles',
    unit: 'sqft',
    rates: { good: 35, better: 75, best: 160 }, // Somany, Kajaria, Premium
    wastagePercentage: 8,
    brands: { good: 'Somany Ceramic', better: 'Kajaria Vitrified', best: 'Nitco Luxe' },
    specifications: { good: '600x600mm', better: '600x1200mm', best: 'Large Format' }
  },
  
  // Bathroom Tiles
  {
    id: 'bathroom_tiles',
    category: 'finishes',
    name: 'Ceramic Wall Tiles',
    unit: 'sqft',
    rates: { good: 28, better: 45, best: 90 },
    wastagePercentage: 8,
    brands: { good: 'Basic Ceramic', better: 'Standard Ceramic', best: 'Designer Ceramic' },
    specifications: { good: '300x300mm', better: '300x450mm', best: '300x600mm' }
  },
  
  // Interior Paint - Based on research data
  {
    id: 'interior_paint',
    category: 'finishes',
    name: 'Interior Paint',
    unit: 'sqft',
    rates: { good: 25, better: 35, best: 50 },
    wastagePercentage: 3,
    brands: { good: 'Asian Paints Tractor', better: 'Asian Paints Apcolite', best: 'Asian Paints Royale' },
    specifications: { good: 'Economy Emulsion', better: 'Premium Emulsion', best: 'Luxury Emulsion' }
  },
  
  // Exterior Paint
  {
    id: 'exterior_paint',
    category: 'finishes',
    name: 'Exterior Paint',
    unit: 'sqft',
    rates: { good: 30, better: 45, best: 65 },
    wastagePercentage: 3,
    brands: { good: 'Asian Paints Apex', better: 'Nerolac Excel', best: 'Asian Paints Apex Ultima' },
    specifications: { good: 'Standard Weather', better: 'Premium Weather', best: 'Ultra Weather' }
  },
  
  // Wall Putty
  {
    id: 'wall_putty',
    category: 'finishes',
    name: 'Wall Putty',
    unit: 'kg',
    rates: { good: 20, better: 25, best: 30 },
    wastagePercentage: 5,
    brands: { good: 'Birla Wall Putty', better: 'JK Wall Max', best: 'Asian Paints TruCare' },
    specifications: { good: 'Standard', better: 'Premium', best: 'Ultra Premium' }
  },
  
  // Doors - Based on research data
  {
    id: 'main_door',
    category: 'doors_windows',
    name: 'Main Door',
    unit: 'sqft',
    rates: { good: 450, better: 650, best: 950 },
    wastagePercentage: 5,
    brands: { good: 'Hardwood Frame', better: 'Teak Wood Frame', best: 'Premium Teak' },
    specifications: { good: 'Standard Design', better: 'Designer Panel', best: 'Luxury Design' }
  },
  
  // Internal Doors
  {
    id: 'internal_doors',
    category: 'doors_windows',
    name: 'Internal Doors',
    unit: 'sqft',
    rates: { good: 250, better: 350, best: 500 },
    wastagePercentage: 5,
    brands: { good: 'Flush Door', better: 'Moulded Panel', best: 'Solid Wood Panel' },
    specifications: { good: '30mm Thick', better: '35mm Thick', best: '40mm Thick' }
  },
  
  // Windows - Based on research data
  {
    id: 'windows',
    category: 'doors_windows',
    name: 'Windows',
    unit: 'sqft',
    rates: { good: 280, better: 350, best: 700 }, // Aluminium, UPVC, Fenesta
    wastagePercentage: 4,
    brands: { good: 'Aluminium', better: 'UPVC Standard', best: 'Fenesta Premium' },
    specifications: { good: 'Powder Coated', better: 'Standard UPVC', best: 'Premium UPVC' }
  },
  
  // Electrical Wires - Based on research data
  {
    id: 'electrical_wire',
    category: 'electrical',
    name: 'Electrical Wire',
    unit: 'meter',
    rates: { good: 19, better: 25, best: 32 }, // Per meter for 1.5 sqmm
    wastagePercentage: 3,
    brands: { good: 'Finolex', better: 'Havells', best: 'Polycab' },
    specifications: { good: 'FR PVC', better: 'HRFR', best: 'FRLSH' }
  },
  
  // Electrical Switches
  {
    id: 'switches',
    category: 'electrical',
    name: 'Electrical Switches',
    unit: 'point',
    rates: { good: 350, better: 500, best: 750 },
    wastagePercentage: 1,
    brands: { good: 'Anchor Roma', better: 'Legrand Myrius', best: 'Schneider Livia' },
    specifications: { good: 'Basic Modular', better: 'Premium Modular', best: 'Designer Modular' }
  },
  
  // Plumbing Pipes
  {
    id: 'plumbing_pipes',
    category: 'plumbing',
    name: 'CPVC Pipes',
    unit: 'meter',
    rates: { good: 85, better: 110, best: 140 },
    wastagePercentage: 5,
    brands: { good: 'Supreme', better: 'Astral', best: 'Prince' },
    specifications: { good: 'Standard CPVC', better: 'Premium CPVC', best: 'Ultra CPVC' }
  },
  
  // Bathroom Fittings - Based on research data
  {
    id: 'bathroom_fittings',
    category: 'plumbing',
    name: 'Bathroom Fittings Set',
    unit: 'set',
    rates: { good: 8500, better: 15000, best: 25000 },
    wastagePercentage: 1,
    brands: { good: 'Cera Basic', better: 'Hindware', best: 'Jaquar Premium' },
    specifications: { good: 'Standard Set', better: 'Premium Set', best: 'Designer Set' }
  }
];