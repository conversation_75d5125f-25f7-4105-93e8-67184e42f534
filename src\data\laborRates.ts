export interface LaborRate {
  id: string;
  category: string;
  name: string;
  unit: string;
  rates: {
    good: number;
    better: number;
    best: number;
  };
  productivity: {
    task: string;
    outputPerDay: number;
    unit: string;
  };
  skillLevel: 'unskilled' | 'semiskilled' | 'skilled' | 'supervisor';
  location: 'delhi' | 'gurgaon' | 'noida' | 'ghaziabad' | 'all';
  description: string;
}

export const laborRates: LaborRate[] = [
  // Foundation & Structure - Based on Delhi NCR research
  {
    id: 'excavation_manual',
    category: 'foundation',
    name: 'Manual Excavation',
    unit: 'm3',
    rates: { good: 450, better: 525, best: 600 },
    productivity: { task: 'Manual Excavation', outputPerDay: 1.0, unit: 'm3' },
    skillLevel: 'unskilled',
    location: 'all',
    description: 'Manual excavation for foundation work'
  },
  {
    id: 'excavation_machine',
    category: 'foundation',
    name: 'Machine Excavation',
    unit: 'm3',
    rates: { good: 240, better: 280, best: 320 },
    productivity: { task: 'JCB Excavation', outputPerDay: 5.7, unit: 'm3' },
    skillLevel: 'skilled',
    location: 'all',
    description: 'Machine excavation using JCB/excavator'
  },
  {
    id: 'concrete_mixing_pouring',
    category: 'foundation',
    name: 'Concrete Mixing & Pouring',
    unit: 'm3',
    rates: { good: 380, better: 450, best: 520 },
    productivity: { task: 'Concrete Work', outputPerDay: 8, unit: 'm3' },
    skillLevel: 'skilled',
    location: 'all',
    description: 'Concrete mixing, pouring, and finishing'
  },
  {
    id: 'steel_cutting_bending',
    category: 'foundation',
    name: 'Steel Cutting & Bending',
    unit: 'kg',
    rates: { good: 2.5, better: 3, best: 3.5 },
    productivity: { task: 'Steel Preparation', outputPerDay: 200, unit: 'kg' },
    skillLevel: 'skilled',
    location: 'all',
    description: 'Steel bar cutting and bending as per drawings'
  },
  {
    id: 'steel_fixing',
    category: 'foundation',
    name: 'Steel Bar Fixing',
    unit: 'kg',
    rates: { good: 6.5, better: 8, best: 9.5 },
    productivity: { task: 'Rebar Installation', outputPerDay: 125, unit: 'kg' },
    skillLevel: 'skilled',
    location: 'all',
    description: 'Steel reinforcement fixing and tying'
  },
  {
    id: 'formwork_erection',
    category: 'foundation',
    name: 'Formwork Erection & Removal',
    unit: 'sqm',
    rates: { good: 70, better: 85, best: 100 },
    productivity: { task: 'Shuttering Work', outputPerDay: 15, unit: 'sqm' },
    skillLevel: 'skilled',
    location: 'all',
    description: 'Formwork installation and removal'
  },

  // Masonry & Finishes - Based on productivity research
  {
    id: 'brickwork_230mm',
    category: 'masonry',
    name: 'Brick Work (230mm)',
    unit: 'sqm',
    rates: { good: 150, better: 180, best: 210 },
    productivity: { task: '230mm Brickwork', outputPerDay: 8, unit: 'sqm' },
    skillLevel: 'skilled',
    location: 'all',
    description: '9-inch brick wall construction'
  },
  {
    id: 'brickwork_115mm',
    category: 'masonry',
    name: 'Brick Work (115mm)',
    unit: 'sqm',
    rates: { good: 100, better: 120, best: 140 },
    productivity: { task: '115mm Brickwork', outputPerDay: 12, unit: 'sqm' },
    skillLevel: 'skilled',
    location: 'all',
    description: '4.5-inch brick wall construction'
  },
  {
    id: 'aac_block_laying',
    category: 'masonry',
    name: 'AAC Block Laying',
    unit: 'sqm',
    rates: { good: 80, better: 95, best: 110 },
    productivity: { task: 'AAC Block Work', outputPerDay: 15, unit: 'sqm' },
    skillLevel: 'semiskilled',
    location: 'all',
    description: 'AAC block wall construction with adhesive'
  },
  {
    id: 'plastering_internal',
    category: 'masonry',
    name: 'Internal Plastering',
    unit: 'sqm',
    rates: { good: 70, better: 85, best: 100 },
    productivity: { task: '12mm Internal Plaster', outputPerDay: 25, unit: 'sqm' },
    skillLevel: 'skilled',
    location: 'all',
    description: '12mm cement sand plaster for internal walls'
  },
  {
    id: 'plastering_external',
    category: 'masonry',
    name: 'External Plastering',
    unit: 'sqm',
    rates: { good: 80, better: 95, best: 110 },
    productivity: { task: '15mm External Plaster', outputPerDay: 20, unit: 'sqm' },
    skillLevel: 'skilled',
    location: 'all',
    description: '15mm cement sand plaster for external walls'
  },

  // Finishing Work
  {
    id: 'tile_laying_floor',
    category: 'finishes',
    name: 'Floor Tile Laying',
    unit: 'sqm',
    rates: { good: 100, better: 120, best: 140 },
    productivity: { task: 'Floor Tiling', outputPerDay: 15, unit: 'sqm' },
    skillLevel: 'skilled',
    location: 'all',
    description: 'Floor tile installation with adhesive'
  },
  {
    id: 'tile_laying_wall',
    category: 'finishes',
    name: 'Wall Tile Laying',
    unit: 'sqm',
    rates: { good: 125, better: 150, best: 175 },
    productivity: { task: 'Wall Tiling', outputPerDay: 12, unit: 'sqm' },
    skillLevel: 'skilled',
    location: 'all',
    description: 'Wall tile installation with adhesive'
  },
  {
    id: 'painting_interior',
    category: 'finishes',
    name: 'Interior Painting',
    unit: 'sqm',
    rates: { good: 35, better: 45, best: 55 },
    productivity: { task: 'Interior Painting (2 coats)', outputPerDay: 80, unit: 'sqm' },
    skillLevel: 'semiskilled',
    location: 'all',
    description: 'Interior wall painting with primer and 2 coats'
  },
  {
    id: 'painting_exterior',
    category: 'finishes',
    name: 'Exterior Painting',
    unit: 'sqm',
    rates: { good: 45, better: 55, best: 65 },
    productivity: { task: 'Exterior Painting (2 coats)', outputPerDay: 60, unit: 'sqm' },
    skillLevel: 'semiskilled',
    location: 'all',
    description: 'Exterior wall painting with weather-proof paint'
  },
  {
    id: 'wall_putty_application',
    category: 'finishes',
    name: 'Wall Putty Application',
    unit: 'sqm',
    rates: { good: 20, better: 25, best: 30 },
    productivity: { task: 'Putty Application', outputPerDay: 100, unit: 'sqm' },
    skillLevel: 'semiskilled',
    location: 'all',
    description: 'Wall putty application for smooth finish'
  },

  // Doors & Windows
  {
    id: 'door_frame_fixing',
    category: 'doors_windows',
    name: 'Door Frame Fixing',
    unit: 'sqm',
    rates: { good: 100, better: 120, best: 140 },
    productivity: { task: 'Door Installation', outputPerDay: 3, unit: 'doors' },
    skillLevel: 'skilled',
    location: 'all',
    description: 'Door frame installation and fitting'
  },
  {
    id: 'window_installation',
    category: 'doors_windows',
    name: 'Window Installation',
    unit: 'sqm',
    rates: { good: 80, better: 95, best: 110 },
    productivity: { task: 'Window Installation', outputPerDay: 4, unit: 'windows' },
    skillLevel: 'skilled',
    location: 'all',
    description: 'Window frame and glass installation'
  },

  // MEP - Based on Delhi NCR rates
  {
    id: 'electrical_wiring',
    category: 'electrical',
    name: 'Electrical Wiring',
    unit: 'point',
    rates: { good: 150, better: 180, best: 210 },
    productivity: { task: 'Concealed Wiring', outputPerDay: 20, unit: 'points' },
    skillLevel: 'skilled',
    location: 'all',
    description: 'Concealed electrical wiring installation'
  },
  {
    id: 'electrical_fitting',
    category: 'electrical',
    name: 'Electrical Fitting Installation',
    unit: 'point',
    rates: { good: 70, better: 85, best: 100 },
    productivity: { task: 'Switch & Socket Installation', outputPerDay: 25, unit: 'points' },
    skillLevel: 'skilled',
    location: 'all',
    description: 'Switch and socket installation'
  },
  {
    id: 'plumbing_installation',
    category: 'plumbing',
    name: 'Plumbing Installation',
    unit: 'point',
    rates: { good: 200, better: 250, best: 300 },
    productivity: { task: 'Concealed Plumbing', outputPerDay: 12, unit: 'points' },
    skillLevel: 'skilled',
    location: 'all',
    description: 'Concealed plumbing pipe installation'
  },
  {
    id: 'sanitary_fitting',
    category: 'plumbing',
    name: 'Sanitary Fitting Installation',
    unit: 'set',
    rates: { good: 380, better: 450, best: 520 },
    productivity: { task: 'Bathroom Fitting', outputPerDay: 2, unit: 'sets' },
    skillLevel: 'skilled',
    location: 'all',
    description: 'Bathroom sanitary fitting installation'
  }
];

// Helper function to get labor rate by ID and quality tier
export function getLaborRate(id: string, qualityTier: 'good' | 'better' | 'best' = 'better'): LaborRate | undefined {
  return laborRates.find(rate => rate.id === id);
}

// Helper function to get labor rates by category
export function getLaborRatesByCategory(category: string): LaborRate[] {
  return laborRates.filter(rate => rate.category === category);
}

// Helper function to calculate total labor cost with quality tier and location
export function calculateLaborCost(
  laborId: string, 
  quantity: number, 
  qualityTier: 'good' | 'better' | 'best' = 'better',
  locationMultiplier: number = 1
): number {
  const laborRate = getLaborRate(laborId, qualityTier);
  if (!laborRate) return 0;
  
  return quantity * laborRate.rates[qualityTier] * locationMultiplier;
}

// Helper function to calculate man-days required
export function calculateManDays(
  laborId: string,
  quantity: number
): number {
  const laborRate = getLaborRate(laborId);
  if (!laborRate) return 0;
  
  return quantity / laborRate.productivity.outputPerDay;
}