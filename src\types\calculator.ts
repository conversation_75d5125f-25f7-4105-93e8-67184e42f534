export type QualityTier = 'good' | 'better' | 'best';
export type Location = 'delhi' | 'gurgaon' | 'noida' | 'ghaziabad';
export type PlotUnit = 'sqft' | 'sqyard' | 'sqmeter';
export type PlotInputMode = 'area' | 'dimensions';

export interface UserInputs {
  plotSize: number;
  plotUnit: PlotUnit;
  plotInputMode: PlotInputMode;
  plotLength?: number;
  plotWidth?: number;
  plotDimensionUnit?: PlotUnit;
  plotShapeAdjustment: number; // Percentage adjustment for non-rectangular plots (default 100%)
  numberOfFloors: number;
  constructionPercentage: number;
  hasBasement: boolean;
  hasStiltParking: boolean;
  location: Location;
}

export interface RoomConfiguration {
  id: string;
  type: string;
  count: number;
  areaPerRoom: number;
  totalArea: number;
}

export interface RoomMaterialSelection {
  roomId: string;
  roomType: string;
  flooringMaterial: string;
  flooringCost: number;
  paintMaterial: string;
  paintCost: number;
  electricalPoints: number;
  plumbingFixtures: number;
}

// New interface for geometric quantities from digital takedown
export interface GeometricQuantities {
  // Basic Areas (in square feet for display, calculated in sqm internally)
  groundCoverageArea: number;
  totalBuiltUpArea: number;
  basementArea: number;
  stiltArea: number;
  totalConstructionArea: number;
  
  // Structural Elements (in cubic meters)
  concreteVolume_Foundations_m3: number;
  concreteVolume_Columns_m3: number;
  concreteVolume_Beams_m3: number;
  concreteVolume_Slabs_m3: number;
  totalConcreteVolume_m3: number;
  
  // Linear Measurements (in meters)
  foundationBeams_linearM: number;
  plinthBeams_linearM: number;
  floorBeams_linearM: number;
  columns_verticalM: number;
  
  // Surface Areas - Gross (in square meters)
  totalWallArea_sqm: number;
  totalFloorArea_sqm: number;
  totalCeilingArea_sqm: number;
  externalWallArea_sqm: number;
  internalWallArea_sqm: number;

  // Surface Areas - Net after opening deductions (IS 1200 compliance)
  totalWallAreaNet_sqm: number;
  externalWallAreaNet_sqm: number;
  internalWallAreaNet_sqm: number;

  // Opening Details (IS 1200:2012 compliance)
  numberOfDoors: number;
  numberOfWindows: number;
  totalDoorArea_sqm: number;
  totalWindowArea_sqm: number;
  totalOpeningArea_sqm: number;

  // Enhanced Plaster Areas (in square meters)
  totalPlasterArea_sqm: number;
  internalPlasterArea_sqm: number;
  externalPlasterArea_sqm: number;
  
  // Detailed Steel Requirements (in kg)
  steel_foundations_kg: number;
  steel_columns_kg: number;
  steel_beams_kg: number;
  steel_slabs_kg: number;
  totalSteel_kg: number;
  
  // Room configuration
  roomConfiguration: RoomConfiguration[];
  
  // Structural specifications
  concreteGrade: string;
  foundationType: string;
  structuralSystem: string;
}

export interface CalculatedQuantities extends GeometricQuantities {
  // Legacy fields for backward compatibility
  totalRCCVolume: number;
  totalTMTSteel: number;
  totalBrickworkArea: number;
  totalPlasterArea: number;
  numberOfDoors: number;
  numberOfWindows: number;
  numberOfBathrooms: number;
  electricalPoints: number;
  plumbingPoints: number;
  steelRatioColumns: number;
  foundationMultiplier: number;
}

export interface CalculationBreakdown {
  formula: string;
  components: {
    name: string;
    quantity: number;
    unit: string;
    rate: number;
    total: number;
    source: string;
  }[];
  totalQuantity: number;
  materialCost: number;
  laborCost: number;
  finalCost: number;
  notes?: string[];
}

export interface CostItem {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  rate: number;
  total: number;
  category: string;
  materialOptions?: string[];
  selectedMaterial?: string;
  editable: boolean;
  roomCustomizable?: boolean;
  isRecommended?: boolean;
  recommendationNote?: string;
  // New fields for transparency
  calculationBreakdown?: CalculationBreakdown;
  isCalculated?: boolean; // True if calculated from bottom-up, false if user-editable
}

export interface CostSection {
  id: string;
  title: string;
  items: CostItem[];
  subtotal: number;
}

export interface CalculationResult {
  totalCost: number;
  ratePerSqft: number;
  sections: CostSection[];
  qualityTier: QualityTier;
  quantities: CalculatedQuantities;
  location: Location;
  roomMaterialSelections: RoomMaterialSelection[];
  // New field for geometric data
  geometricQuantities: GeometricQuantities;
}

export interface ProjectSummary {
  projectOverview: {
    plotSize: number;
    totalBuiltUpArea: number;
    location: string;
    structuralOptions: string[];
  };
  finalCost: number;
  qualityProfile: {
    overallTier: QualityTier;
    structure: string;
    finishing: string;
    fittings: string;
  };
  costBreakdown: {
    structure: number;
    finishes: number;
    fees: number;
    mep: number;
  };
  customChoices: RoomMaterialSelection[];
}