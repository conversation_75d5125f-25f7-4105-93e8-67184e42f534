import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Save, X, Upload, Eye, Layers, Settings, Database, Map, Calculator, HardHat, Building2, DollarSign, Package } from 'lucide-react'
import { Component, Task, componentAPI, taskAPI, uiDefaultsAPI, UIDefaults } from '../../lib/supabase'
import { ComponentForm } from './ComponentForm'
import { TaskManagementForm } from './TaskManagementForm'
import { EngineeringStandardsTab } from './EngineeringStandardsTab'
import { RegionalDataTab } from './RegionalDataTab'
import { SystemConfigurationTab } from './SystemConfigurationTab'
import { LaborRateManagementTab } from './LaborRateManagementTab'
import { MultiBrandManager } from './MultiBrandManager'

interface AdminDashboardProps {
  isOpen: boolean
  onClose: () => void
}

export function AdminDashboard({ isOpen, onClose }: AdminDashboardProps) {
  const [activeTab, setActiveTab] = useState<'components' | 'tasks' | 'defaults' | 'engineering' | 'regional' | 'labor' | 'system' | 'brands'>('components')
  const [components, setComponents] = useState<Component[]>([])
  const [tasks, setTasks] = useState<Task[]>([])
  const [defaults, setDefaults] = useState<UIDefaults | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [editingComponent, setEditingComponent] = useState<Component | null>(null)
  const [showComponentForm, setShowComponentForm] = useState(false)
  const [showTaskForm, setShowTaskForm] = useState(false)
  const [editingTask, setEditingTask] = useState<Task | null>(null)
  const [showMultiBrandManager, setShowMultiBrandManager] = useState(false)

  useEffect(() => {
    if (isOpen) {
      loadData()
    }
  }, [isOpen, activeTab])

  const loadData = async () => {
    setIsLoading(true)
    try {
      if (activeTab === 'components') {
        const data = await componentAPI.getAll()
        setComponents(data)
      } else if (activeTab === 'tasks') {
        const data = await taskAPI.getAll()
        setTasks(data)
      } else if (activeTab === 'defaults') {
        const data = await uiDefaultsAPI.getDefaults()
        setDefaults(data)
      }
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveComponent = async (componentData: Partial<Component>) => {
    try {
      if (editingComponent) {
        await componentAPI.update(editingComponent.id, componentData)
      } else {
        await componentAPI.create(componentData as Omit<Component, 'id' | 'created_at' | 'updated_at'>)
      }
      await loadData()
      setEditingComponent(null)
      setShowComponentForm(false)
    } catch (error) {
      console.error('Error saving component:', error)
    }
  }

  const handleDeleteComponent = async (id: string) => {
    if (confirm('Are you sure you want to delete this component?')) {
      try {
        await componentAPI.delete(id)
        await loadData()
      } catch (error) {
        console.error('Error deleting component:', error)
      }
    }
  }

  const handleSaveTask = async (taskData: any) => {
    try {
      // Task saving logic would go here
      console.log('Saving task:', taskData)
      await loadData()
      setEditingTask(null)
      setShowTaskForm(false)
    } catch (error) {
      console.error('Error saving task:', error)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-7xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">NirmaanAI Admin Control Panel</h2>
            <p className="text-gray-600 mt-1">Manage components, tasks, and engineering standards</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex flex-wrap space-x-1 px-6 overflow-x-auto">
            {[
              { id: 'components', label: 'Components', desc: 'Materials & products', icon: <Database className="w-4 h-4" /> },
              { id: 'tasks', label: 'Tasks', desc: 'Installation recipes', icon: <Layers className="w-4 h-4" /> },
              { id: 'defaults', label: 'UI Defaults', desc: 'Default selections', icon: <Eye className="w-4 h-4" /> },
              { id: 'engineering', label: 'Engineering Standards', desc: 'Technical parameters', icon: <HardHat className="w-4 h-4" /> },
              { id: 'regional', label: 'Regional Data', desc: 'Location-specific data', icon: <Map className="w-4 h-4" /> },
              { id: 'labor', label: 'Labor Rates', desc: 'Workforce costs', icon: <Building2 className="w-4 h-4" /> },
              { id: 'system', label: 'System Config', desc: 'Global settings', icon: <Settings className="w-4 h-4" /> },
              { id: 'brands', label: 'Multi-Brand', desc: 'Brand management', icon: <Package className="w-4 h-4" /> }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-4 border-b-2 font-medium text-sm transition-colors flex items-center gap-2 whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon}
                <div>
                  <div>{tab.label}</div>
                  <div className="text-xs text-gray-400">{tab.desc}</div>
                </div>
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {activeTab === 'components' && (
            <ComponentsTab
              components={components}
              isLoading={isLoading}
              onEdit={setEditingComponent}
              onDelete={handleDeleteComponent}
              onAdd={() => setShowComponentForm(true)}
            />
          )}

          {activeTab === 'tasks' && (
            <TasksTab
              tasks={tasks}
              isLoading={isLoading}
              onEdit={setEditingTask}
              onAdd={() => setShowTaskForm(true)}
            />
          )}

          {activeTab === 'defaults' && (
            <DefaultsTab
              defaults={defaults}
              isLoading={isLoading}
              components={components}
            />
          )}

          {activeTab === 'engineering' && (
            <EngineeringStandardsTab />
          )}

          {activeTab === 'regional' && (
            <RegionalDataTab />
          )}

          {activeTab === 'labor' && (
            <LaborRateManagementTab />
          )}

          {activeTab === 'system' && (
            <SystemConfigurationTab />
          )}

          {activeTab === 'brands' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">Multi-Brand Component Management</h3>
                  <p className="text-gray-600 text-sm mt-1">Manage brand varieties and pricing for different materials</p>
                </div>
                <button
                  onClick={() => setShowMultiBrandManager(true)}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
                >
                  <Package className="w-4 h-4" />
                  Open Brand Manager
                </button>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-semibold text-blue-800 mb-2">Multi-Brand System Features</h4>
                <ul className="text-blue-700 text-sm space-y-1">
                  <li>• Compare pricing across different brands for same materials</li>
                  <li>• Add new brand variants for existing components</li>
                  <li>• Analyze price ranges and market positioning</li>
                  <li>• Manage brand-specific specifications and features</li>
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Component Form Modal */}
        {(showComponentForm || editingComponent) && (
          <ComponentForm
            component={editingComponent}
            tasks={tasks}
            onSave={handleSaveComponent}
            onCancel={() => {
              setEditingComponent(null)
              setShowComponentForm(false)
            }}
          />
        )}

        {/* Task Form Modal */}
        {(showTaskForm || editingTask) && (
          <TaskManagementForm
            task={editingTask}
            onSave={handleSaveTask}
            onCancel={() => {
              setEditingTask(null)
              setShowTaskForm(false)
            }}
          />
        )}

        {/* Multi-Brand Manager Modal */}
        {showMultiBrandManager && (
          <MultiBrandManager
            isOpen={showMultiBrandManager}
            onClose={() => setShowMultiBrandManager(false)}
          />
        )}
      </div>
    </div>
  )
}

function ComponentsTab({ 
  components, 
  isLoading, 
  onEdit, 
  onDelete, 
  onAdd 
}: {
  components: Component[]
  isLoading: boolean
  onEdit: (component: Component) => void
  onDelete: (id: string) => void
  onAdd: () => void
}) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  
  const categories = ['all', ...new Set(components.map(c => c.category))]
  const filteredComponents = selectedCategory === 'all' 
    ? components 
    : components.filter(c => c.category === selectedCategory)

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-semibold text-gray-800">Components Management</h3>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-200 rounded-lg text-sm"
          >
            {categories.map(cat => (
              <option key={cat} value={cat}>
                {cat === 'all' ? 'All Categories' : cat}
              </option>
            ))}
          </select>
        </div>
        <button
          onClick={onAdd}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
        >
          <Plus className="w-4 h-4" />
          Add Component
        </button>
      </div>

      {/* Components Grid */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredComponents.map((component) => (
            <div key={component.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-800">{component.name}</h4>
                  <p className="text-sm text-gray-600">{component.brand}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                      {component.category}
                    </span>
                    {component.sub_category && (
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                        {component.sub_category}
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex gap-1">
                  <button
                    onClick={() => onEdit(component)}
                    className="p-1 hover:bg-gray-100 rounded transition-colors"
                  >
                    <Edit className="w-4 h-4 text-gray-500" />
                  </button>
                  <button
                    onClick={() => onDelete(component.id)}
                    className="p-1 hover:bg-red-100 rounded transition-colors"
                  >
                    <Trash2 className="w-4 h-4 text-red-500" />
                  </button>
                </div>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Price:</span>
                  <span className="font-semibold">₹{Number(component.unit_price).toLocaleString()}/{component.unit}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Model:</span>
                  <span className="text-gray-800">{component.cost_model}</span>
                </div>
                {component.associated_task_id && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Has Task:</span>
                    <span className="text-green-600">Yes</span>
                  </div>
                )}
              </div>

              {component.image_url && (
                <div className="mt-3">
                  <img 
                    src={component.image_url} 
                    alt={component.name}
                    className="w-full h-32 object-cover rounded-lg"
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

function TasksTab({ 
  tasks, 
  isLoading,
  onEdit,
  onAdd
}: { 
  tasks: Task[]
  isLoading: boolean
  onEdit: (task: Task) => void
  onAdd: () => void
}) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">Installation Tasks</h3>
        <button 
          onClick={onAdd}
          className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
        >
          <Plus className="w-4 h-4" />
          Add Task
        </button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-green-600 border-t-transparent"></div>
        </div>
      ) : (
        <div className="space-y-4">
          {tasks.map((task) => (
            <div key={task.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-semibold text-gray-800">{task.name}</h4>
                  <p className="text-gray-600 text-sm mt-1">{task.description}</p>
                  <div className="flex items-center gap-2 mt-2">
                    <span className="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">
                      {task.category}
                    </span>
                    <span className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-full">
                      {task.complexity_level}
                    </span>
                    <span className="text-xs text-gray-500">
                      {task.estimated_duration_hours}h estimated
                    </span>
                  </div>
                </div>
                <div className="flex gap-1">
                  <button 
                    onClick={() => onEdit(task)}
                    className="p-1 hover:bg-gray-100 rounded transition-colors"
                  >
                    <Edit className="w-4 h-4 text-gray-500" />
                  </button>
                </div>
              </div>

              {/* Task Requirements Summary */}
              {task.task_requirements && task.task_requirements.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Requirements:</h5>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {task.task_requirements.map((req: any) => (
                      <div 
                        key={req.id} 
                        className={`text-xs p-2 rounded ${
                          req.requirement_type === 'material' 
                            ? 'bg-blue-50 text-blue-700' 
                            : req.requirement_type === 'labor'
                            ? 'bg-orange-50 text-orange-700'
                            : 'bg-purple-50 text-purple-700'
                        }`}
                      >
                        <div className="font-medium">
                          {req.requirement_type === 'material' && req.components
                            ? req.components.name
                            : req.requirement_type === 'labor' && req.labor_rates
                            ? req.labor_rates.name
                            : req.requirement_type}
                        </div>
                        <div>
                          {req.quantity_per_sqm} per sqm
                          {req.is_optional ? ' (Optional)' : ''}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}

          {tasks.length === 0 && !isLoading && (
            <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
              <Layers className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-700 mb-2">No Tasks Created Yet</h4>
              <p className="text-gray-500 max-w-md mx-auto">
                Tasks define installation recipes that combine materials, labor, and tools. 
                Create your first task to start building your knowledge base.
              </p>
              <button
                onClick={onAdd}
                className="mt-4 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
              >
                Create First Task
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

function DefaultsTab({ 
  defaults, 
  isLoading, 
  components 
}: { 
  defaults: UIDefaults | null
  isLoading: boolean
  components: Component[]
}) {
  const [configData, setConfigData] = useState<Record<string, any>>({})

  useEffect(() => {
    if (defaults) {
      setConfigData(defaults.config_data)
    }
  }, [defaults])

  const handleSaveDefaults = async () => {
    try {
      await uiDefaultsAPI.updateDefaults(configData)
      alert('Defaults updated successfully!')
    } catch (error) {
      console.error('Error updating defaults:', error)
      alert('Error updating defaults')
    }
  }

  const getComponentsByCategory = (category: string) => {
    return components.filter(c => c.category === category)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-purple-600 border-t-transparent"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">UI Default Configurations</h3>
        <button
          onClick={handleSaveDefaults}
          className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
        >
          <Save className="w-4 h-4" />
          Save Defaults
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Room Defaults */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-4">Room Defaults</h4>
          <div className="space-y-4">
            {Object.entries(configData.rooms || {}).map(([roomType, roomConfig]: [string, any]) => (
              <div key={roomType} className="bg-white rounded-lg p-3">
                <h5 className="font-medium text-gray-700 mb-2">{roomType}</h5>
                <div className="space-y-2">
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">Default Flooring</label>
                    <select
                      value={roomConfig.flooring || ''}
                      onChange={(e) => {
                        setConfigData(prev => ({
                          ...prev,
                          rooms: {
                            ...prev.rooms,
                            [roomType]: {
                              ...roomConfig,
                              flooring: e.target.value
                            }
                          }
                        }))
                      }}
                      className="w-full px-2 py-1 border border-gray-200 rounded text-sm"
                    >
                      <option value="">Select flooring...</option>
                      {getComponentsByCategory('Flooring').map(component => (
                        <option key={component.id} value={component.id}>
                          {component.name} - ₹{component.unit_price}/{component.unit}
                        </option>
                      ))}
                    </select>
                  </div>
                  {roomType === 'Bathroom' && (
                    <div>
                      <label className="block text-xs text-gray-600 mb-1">Default Fittings Bundle</label>
                      <select
                        value={roomConfig.fittingsBundle || ''}
                        onChange={(e) => {
                          setConfigData(prev => ({
                            ...prev,
                            rooms: {
                              ...prev.rooms,
                              [roomType]: {
                                ...roomConfig,
                                fittingsBundle: e.target.value
                              }
                            }
                          }))
                        }}
                        className="w-full px-2 py-1 border border-gray-200 rounded text-sm"
                      >
                        <option value="">Select bundle...</option>
                        {getComponentsByCategory('Bathroom Fittings').map(component => (
                          <option key={component.id} value={component.id}>
                            {component.name} - ₹{component.unit_price}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Shell Defaults */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-4">Shell & Structure Defaults</h4>
          <div className="space-y-4">
            <div className="bg-white rounded-lg p-3">
              <h5 className="font-medium text-gray-700 mb-2">Windows</h5>
              <select
                value={configData.shell?.windows || ''}
                onChange={(e) => {
                  setConfigData(prev => ({
                    ...prev,
                    shell: {
                      ...prev.shell,
                      windows: e.target.value
                    }
                  }))
                }}
                className="w-full px-2 py-1 border border-gray-200 rounded text-sm"
              >
                <option value="">Select default windows...</option>
                {getComponentsByCategory('Windows').map(component => (
                  <option key={component.id} value={component.id}>
                    {component.name} - ₹{component.unit_price}/{component.unit}
                  </option>
                ))}
              </select>
            </div>

            <div className="bg-white rounded-lg p-3">
              <h5 className="font-medium text-gray-700 mb-2">Facade Finish</h5>
              <select
                value={configData.facade?.primaryFinish || ''}
                onChange={(e) => {
                  setConfigData(prev => ({
                    ...prev,
                    facade: {
                      ...prev.facade,
                      primaryFinish: e.target.value
                    }
                  }))
                }}
                className="w-full px-2 py-1 border border-gray-200 rounded text-sm"
              >
                <option value="">Select facade finish...</option>
                {getComponentsByCategory('Facade').map(component => (
                  <option key={component.id} value={component.id}>
                    {component.name} - ₹{component.unit_price}/{component.unit}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="bg-white rounded-lg p-3">
              <h5 className="font-medium text-gray-700 mb-2">Quality Tier</h5>
              <select
                value={configData.qualityTier || 'better'}
                onChange={(e) => {
                  setConfigData(prev => ({
                    ...prev,
                    qualityTier: e.target.value
                  }))
                }}
                className="w-full px-2 py-1 border border-gray-200 rounded text-sm"
              >
                <option value="good">Good (Budget)</option>
                <option value="better">Better (Premium)</option>
                <option value="best">Best (Luxury)</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}