import React, { useState, useEffect } from 'react'
import { Home, Bed, Bath, ChefHat, Users, BookOpen, Wind } from 'lucide-react'
import { RoomConfiguration } from '../../types/calculator'

interface InteractiveFloorPlanProps {
  rooms: RoomConfiguration[]
  onRoomSelect: (room: RoomConfiguration) => void
  selectedRoomId?: string
}

const roomIcons = {
  'Bedroom': Bed,
  'Living Room': Users,
  'Kitchen': ChefHat,
  'Bathroom': Bath,
  'Dining Room': Users,
  'Study Room': BookOpen,
  'Balcony': Wind,
  'Store Room': Home
}

export function InteractiveFloorPlan({ rooms, onRoomSelect, selectedRoomId }: InteractiveFloorPlanProps) {
  const [hoveredRoom, setHoveredRoom] = useState<string | null>(null)

  // Calculate grid layout based on total area and room count
  const totalArea = rooms.reduce((sum, room) => sum + room.totalArea, 0)
  const gridSize = Math.ceil(Math.sqrt(totalArea / 100)) // Approximate grid size

  // Generate room positions in a smart layout
  const generateRoomLayout = () => {
    const layout: Array<{ room: RoomConfiguration; x: number; y: number; width: number; height: number }> = []
    
    // Sort rooms by area (largest first) for better layout
    const sortedRooms = [...rooms].sort((a, b) => b.totalArea - a.totalArea)
    
    let currentX = 0
    let currentY = 0
    let rowHeight = 0
    const maxWidth = 400 // SVG viewBox width
    
    sortedRooms.forEach((room) => {
      // Calculate room dimensions based on area (simplified)
      const areaRatio = room.totalArea / totalArea
      const baseSize = Math.sqrt(areaRatio) * 300
      const width = Math.max(60, Math.min(120, baseSize))
      const height = Math.max(40, Math.min(80, baseSize * 0.8))
      
      // Check if room fits in current row
      if (currentX + width > maxWidth) {
        currentX = 0
        currentY += rowHeight + 10
        rowHeight = 0
      }
      
      layout.push({
        room,
        x: currentX,
        y: currentY,
        width,
        height
      })
      
      currentX += width + 10
      rowHeight = Math.max(rowHeight, height)
    })
    
    return layout
  }

  const roomLayout = generateRoomLayout()
  const maxY = Math.max(...roomLayout.map(r => r.y + r.height))

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-gray-800 mb-2">Interactive Floor Plan</h3>
        <p className="text-gray-600">Click on any room to customize its materials and finishes</p>
      </div>

      <div className="relative">
        <svg
          viewBox={`0 0 400 ${maxY + 50}`}
          className="w-full h-auto border border-gray-200 rounded-lg bg-gray-50"
          style={{ maxHeight: '400px' }}
        >
          {/* Grid background */}
          <defs>
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" strokeWidth="0.5"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />

          {/* Rooms */}
          {roomLayout.map(({ room, x, y, width, height }) => {
            const IconComponent = roomIcons[room.type as keyof typeof roomIcons] || Home
            const isSelected = selectedRoomId === room.id
            const isHovered = hoveredRoom === room.id

            return (
              <g key={room.id}>
                {/* Room rectangle */}
                <rect
                  x={x}
                  y={y}
                  width={width}
                  height={height}
                  fill={isSelected ? '#dbeafe' : isHovered ? '#f3f4f6' : '#ffffff'}
                  stroke={isSelected ? '#3b82f6' : isHovered ? '#6b7280' : '#d1d5db'}
                  strokeWidth={isSelected ? 3 : isHovered ? 2 : 1}
                  rx="4"
                  className="cursor-pointer transition-all duration-200"
                  onClick={() => onRoomSelect(room)}
                  onMouseEnter={() => setHoveredRoom(room.id)}
                  onMouseLeave={() => setHoveredRoom(null)}
                />

                {/* Room icon */}
                <foreignObject
                  x={x + width/2 - 12}
                  y={y + height/2 - 20}
                  width="24"
                  height="24"
                  className="pointer-events-none"
                >
                  <IconComponent 
                    className={`w-6 h-6 ${isSelected ? 'text-blue-600' : 'text-gray-600'}`}
                  />
                </foreignObject>

                {/* Room label */}
                <text
                  x={x + width/2}
                  y={y + height/2 + 8}
                  textAnchor="middle"
                  className={`text-xs font-medium pointer-events-none ${
                    isSelected ? 'fill-blue-600' : 'fill-gray-700'
                  }`}
                >
                  {room.type}
                </text>

                {/* Room count badge */}
                {room.count > 1 && (
                  <circle
                    cx={x + width - 8}
                    cy={y + 8}
                    r="8"
                    fill="#ef4444"
                    className="pointer-events-none"
                  />
                )}
                {room.count > 1 && (
                  <text
                    x={x + width - 8}
                    y={y + 12}
                    textAnchor="middle"
                    className="text-xs font-bold fill-white pointer-events-none"
                  >
                    {room.count}
                  </text>
                )}

                {/* Area label */}
                <text
                  x={x + width/2}
                  y={y + height - 8}
                  textAnchor="middle"
                  className="text-xs fill-gray-500 pointer-events-none"
                >
                  {room.totalArea} sq ft
                </text>
              </g>
            )
          })}
        </svg>

        {/* Legend */}
        <div className="mt-4 flex flex-wrap gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border border-gray-300 rounded"></div>
            <span>Unselected</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-blue-100 border-2 border-blue-500 rounded"></div>
            <span>Selected</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span>Multiple rooms</span>
          </div>
        </div>
      </div>

      {/* Room Summary */}
      <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
        {rooms.map((room) => (
          <div
            key={room.id}
            onClick={() => onRoomSelect(room)}
            className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
              selectedRoomId === room.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center gap-2 mb-1">
              {React.createElement(roomIcons[room.type as keyof typeof roomIcons] || Home, {
                className: `w-4 h-4 ${selectedRoomId === room.id ? 'text-blue-600' : 'text-gray-600'}`
              })}
              <span className="font-medium text-sm">{room.type}</span>
              {room.count > 1 && (
                <span className="text-xs bg-red-500 text-white px-1.5 py-0.5 rounded-full">
                  {room.count}
                </span>
              )}
            </div>
            <div className="text-xs text-gray-500">
              {room.totalArea} sq ft total
            </div>
            {room.count > 1 && (
              <div className="text-xs text-gray-500">
                {room.areaPerRoom} sq ft each
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}