-- Add more components for better variety

-- Additional Flooring Options
INSERT INTO components (name, category, sub_category, brand, cost_model, unit_price, unit, image_url, specifications) VALUES
('Nitco Marble Tiles', 'Flooring', 'Marble Tiles', 'Nitco', 'per_sqm', 180, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"size": "600x600mm", "thickness": "10mm", "finish": "Polished"}'),
('Kajaria Wooden Flooring', 'Flooring', 'Wooden Flooring', 'Kajaria', 'per_sqm', 220, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Engineered Wood", "thickness": "8mm", "finish": "Matte"}'),
('Johnson Porcelain Tiles', 'Flooring', 'Porcelain Tiles', 'Johnson', 'per_sqm', 95, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"size": "800x800mm", "thickness": "9mm", "finish": "<PERSON><PERSON>"}'),
('Simpolo Granite Tiles', 'Flooring', 'Granite Tiles', 'Simpolo', 'per_sqm', 150, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"size": "600x600mm", "thickness": "12mm", "finish": "Flamed"}');

-- Additional Wall Finishes
INSERT INTO components (name, category, sub_category, brand, cost_model, unit_price, unit, image_url, specifications) VALUES
('Asian Paints Royale', 'Wall Finishes', 'Premium Emulsion', 'Asian Paints', 'per_sqm', 35, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Luxury Emulsion", "finish": "Velvet Touch", "washability": "High"}'),
('Berger Silk Luxury', 'Wall Finishes', 'Premium Emulsion', 'Berger', 'per_sqm', 32, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Luxury Emulsion", "finish": "Smooth", "washability": "High"}'),
('Dulux Velvet Touch', 'Wall Finishes', 'Premium Emulsion', 'Dulux', 'per_sqm', 38, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Luxury Emulsion", "finish": "Pearl Glo", "washability": "High"}'),
('Nerolac Impressions', 'Wall Finishes', 'Economy Emulsion', 'Nerolac', 'per_sqm', 25, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Economy Emulsion", "finish": "Matte", "washability": "Medium"}');

-- Additional Bathroom Fittings
INSERT INTO components (name, category, sub_category, brand, cost_model, unit_price, unit, image_url, specifications) VALUES
('Hindware WC', 'Bathroom Fittings', 'Water Closet', 'Hindware', 'per_unit', 5500, 'piece', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Wall mounted", "flush": "Dual flush", "warranty": "5 years"}'),
('Cera Washbasin', 'Bathroom Fittings', 'Basin', 'Cera', 'per_unit', 2800, 'piece', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Table top", "material": "Ceramic", "size": "550x400mm"}'),
('Parryware Diverter Set', 'Bathroom Fittings', 'Faucets', 'Parryware', 'per_unit', 4500, 'set', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Concealed", "finish": "Chrome", "cartridge": "Ceramic"}'),
('Hindware Premium Bathroom Bundle', 'Bathroom Fittings', 'Complete Set', 'Hindware', 'per_unit', 14500, 'set', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"includes": ["WC", "Washbasin", "Mirror", "Diverter Set"], "total_value": 14500, "bundle_discount": 0}');

-- Additional Electrical Components
INSERT INTO components (name, category, sub_category, brand, cost_model, unit_price, unit, image_url, specifications) VALUES
('Havells Modular Switches', 'Electrical', 'Switches', 'Havells', 'per_unit', 350, 'point', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Modular", "finish": "White", "warranty": "2 years"}'),
('Legrand Modular Switches', 'Electrical', 'Switches', 'Legrand', 'per_unit', 450, 'point', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Modular", "finish": "Premium", "warranty": "5 years"}'),
('Schneider Modular Switches', 'Electrical', 'Switches', 'Schneider', 'per_unit', 550, 'point', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Modular", "finish": "Luxury", "warranty": "10 years"}'),
('Anchor Roma Switches', 'Electrical', 'Switches', 'Anchor', 'per_unit', 250, 'point', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Modular", "finish": "Basic", "warranty": "1 year"}');

-- Additional Facade Materials
INSERT INTO components (name, category, sub_category, brand, cost_model, unit_price, unit, image_url, specifications) VALUES
('ACP Sheets', 'Facade', 'ACP Cladding', 'Alstrong', 'per_sqm', 150, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"thickness": "4mm", "finish": "Metallic", "fire_rating": "Class B"}'),
('Glass Curtain Wall', 'Facade', 'Glass Cladding', 'Saint Gobain', 'per_sqm', 280, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"thickness": "8mm", "type": "Reflective", "u_value": "5.8 W/m²K"}'),
('Terracotta Cladding', 'Facade', 'Terracotta', 'Hunter Douglas', 'per_sqm', 220, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"thickness": "20mm", "finish": "Natural", "installation": "Ventilated"}'),
('Exposed Brick Finish', 'Facade', 'Brick Cladding', 'Wienerberger', 'per_sqm', 130, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Exposed Brick", "color": "Red", "pattern": "Running Bond"}');

-- Additional Tasks
INSERT INTO tasks (name, description, category, complexity_level, estimated_duration_hours) VALUES
('Install Wooden Flooring', 'Complete wooden flooring installation including underlayment', 'flooring', 'intermediate', 7),
('Install Granite Flooring', 'Granite tile installation with proper leveling and grouting', 'flooring', 'advanced', 9),
('Install ACP Facade', 'Aluminum Composite Panel installation for building facade', 'exterior', 'advanced', 12),
('Install Glass Curtain Wall', 'Glass curtain wall installation with aluminum framing', 'exterior', 'advanced', 16);

-- Link components to tasks
UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Wooden Flooring') 
WHERE name = 'Kajaria Wooden Flooring';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Granite Flooring') 
WHERE name = 'Simpolo Granite Tiles';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install ACP Facade') 
WHERE name = 'ACP Sheets';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Glass Curtain Wall') 
WHERE name = 'Glass Curtain Wall';

-- Add task requirements for new tasks
INSERT INTO task_requirements (task_id, component_id, labor_id, quantity_per_sqm, requirement_type) VALUES
-- Wooden Flooring Recipe
((SELECT id FROM tasks WHERE name = 'Install Wooden Flooring'), 
 (SELECT id FROM components WHERE name = 'Kajaria Wooden Flooring'), 
 NULL, 1.08, 'material'), -- 8% wastage
((SELECT id FROM tasks WHERE name = 'Install Wooden Flooring'), 
 NULL, 
 (SELECT id FROM labor_rates WHERE name = 'Tile Installation'), 1.0, 'labor'),

-- Granite Flooring Recipe
((SELECT id FROM tasks WHERE name = 'Install Granite Flooring'), 
 (SELECT id FROM components WHERE name = 'Simpolo Granite Tiles'), 
 NULL, 1.05, 'material'), -- 5% wastage
((SELECT id FROM tasks WHERE name = 'Install Granite Flooring'), 
 (SELECT id FROM components WHERE name = 'Tile Adhesive'), 
 NULL, 1.2, 'material'), -- More adhesive for heavy tiles
((SELECT id FROM tasks WHERE name = 'Install Granite Flooring'), 
 NULL, 
 (SELECT id FROM labor_rates WHERE name = 'Tile Installation'), 1.2, 'labor'); -- More labor for heavy tiles