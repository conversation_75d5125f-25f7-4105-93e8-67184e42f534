import React, { useState, useEffect } from 'react';
import { X, Palette, Zap, Droplets, Star, Info } from 'lucide-react';
import { RoomConfiguration, RoomMaterialSelection } from '../types/calculator';

interface RoomMaterialCustomizerProps {
  isOpen: boolean;
  onClose: () => void;
  rooms: RoomConfiguration[];
  materialSelections: RoomMaterialSelection[];
  onSave: (selections: RoomMaterialSelection[]) => void;
}

const flooringOptions = [
  { name: 'Ceramic Tiles', rate: 35, unit: 'sq ft', isRecommended: false },
  { name: 'Vitrified Tiles', rate: 75, unit: 'sq ft', isRecommended: true, recommendationNote: 'Best value for durability and maintenance. Superior stain resistance and longevity make this our top recommendation for most rooms.' },
  { name: 'Wooden Laminate', rate: 120, unit: 'sq ft', isRecommended: false },
  { name: 'Italian Marble', rate: 250, unit: 'sq ft', isRecommended: false },
  { name: 'Granite', rate: 180, unit: 'sq ft', isRecommended: false },
  { name: 'Anti-Skid Tiles', rate: 45, unit: 'sq ft', isRecommended: false }
];

const paintOptions = [
  { name: 'Economy Emulsion', rate: 25, unit: 'sq ft', isRecommended: false },
  { name: 'Premium Emulsion', rate: 35, unit: 'sq ft', isRecommended: true, recommendationNote: 'Perfect balance of cost and quality. Better coverage, washability, and color retention than economy options.' },
  { name: 'Luxury Emulsion', rate: 50, unit: 'sq ft', isRecommended: false },
  { name: 'Texture Paint', rate: 65, unit: 'sq ft', isRecommended: false }
];

export function RoomMaterialCustomizer({ 
  isOpen, 
  onClose, 
  rooms, 
  materialSelections, 
  onSave 
}: RoomMaterialCustomizerProps) {
  const [selections, setSelections] = useState<RoomMaterialSelection[]>([]);

  useEffect(() => {
    if (isOpen && rooms.length > 0) {
      // Initialize selections for all rooms
      const initialSelections = rooms.map(room => {
        const existing = materialSelections.find(s => s.roomId === room.id);
        if (existing) return existing;

        return {
          roomId: room.id,
          roomType: room.type,
          flooringMaterial: 'Vitrified Tiles',
          flooringCost: 0,
          paintMaterial: 'Premium Emulsion',
          paintCost: 0,
          electricalPoints: Math.ceil(room.areaPerRoom / 100) * 2,
          plumbingFixtures: room.type === 'Bathroom' ? 1 : 0
        };
      });
      setSelections(initialSelections);
    }
  }, [isOpen, rooms, materialSelections]);

  if (!isOpen) return null;

  const updateSelection = (roomId: string, updates: Partial<RoomMaterialSelection>) => {
    setSelections(prev => prev.map(s => 
      s.roomId === roomId ? { ...s, ...updates } : s
    ));
  };

  const calculateFlooringCost = (roomId: string, material: string) => {
    const room = rooms.find(r => r.id === roomId);
    const option = flooringOptions.find(o => o.name === material);
    if (!room || !option) return 0;
    return room.totalArea * option.rate;
  };

  const calculatePaintCost = (roomId: string, material: string) => {
    const room = rooms.find(r => r.id === roomId);
    const option = paintOptions.find(o => o.name === material);
    if (!room || !option) return 0;
    // Assume wall area is 2.5x floor area for painting
    return room.totalArea * 2.5 * option.rate;
  };

  const handleSave = () => {
    // Calculate costs for all selections
    const updatedSelections = selections.map(selection => {
      const flooringCost = calculateFlooringCost(selection.roomId, selection.flooringMaterial);
      const paintCost = calculatePaintCost(selection.roomId, selection.paintMaterial);
      return {
        ...selection,
        flooringCost,
        paintCost
      };
    });

    onSave(updatedSelections);
    onClose();
  };

  const totalCost = selections.reduce((sum, selection) => {
    const flooringCost = calculateFlooringCost(selection.roomId, selection.flooringMaterial);
    const paintCost = calculatePaintCost(selection.roomId, selection.paintMaterial);
    return sum + flooringCost + paintCost;
  }, 0);

  const RecommendationBadge = ({ option, className = '' }: { option: any, className?: string }) => {
    if (!option.isRecommended) return null;
    
    return (
      <div className={`group relative ${className}`}>
        <div className="flex items-center gap-1">
          <Star className="w-3 h-3 text-yellow-500 fill-current" />
          <span className="text-xs text-yellow-600 font-medium">Recommended</span>
        </div>
        
        {option.recommendationNote && (
          <div className="absolute bottom-full left-0 mb-2 w-80 bg-gray-800 text-white rounded-lg shadow-lg p-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
            <div className="text-xs font-semibold mb-1">Architect's Recommendation</div>
            <div className="text-xs leading-relaxed">{option.recommendationNote}</div>
            <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-800"></div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-7xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-pink-50">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Room Material Customization</h2>
            <p className="text-gray-600 mt-1">Customize flooring and finishes for each room</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          <div className="space-y-6">
            {rooms.map((room) => {
              const selection = selections.find(s => s.roomId === room.id);
              if (!selection) return null;

              const flooringCost = calculateFlooringCost(room.id, selection.flooringMaterial);
              const paintCost = calculatePaintCost(room.id, selection.paintMaterial);
              const selectedFlooringOption = flooringOptions.find(o => o.name === selection.flooringMaterial);
              const selectedPaintOption = paintOptions.find(o => o.name === selection.paintMaterial);

              return (
                <div key={room.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800">
                        {room.type} {room.count > 1 && `(${room.count} rooms)`}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {room.totalArea.toLocaleString()} sq ft total • {room.areaPerRoom.toLocaleString()} sq ft each
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-xl font-bold text-purple-600">
                        ₹{(flooringCost + paintCost).toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-500">Total Cost</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {/* Flooring */}
                    <div>
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                        <Palette className="w-4 h-4" />
                        Flooring Material
                      </label>
                      <select
                        value={selection.flooringMaterial}
                        onChange={(e) => updateSelection(room.id, { flooringMaterial: e.target.value })}
                        className="enhanced-dropdown w-full"
                      >
                        {flooringOptions.map((option) => (
                          <option key={option.name} value={option.name} className="dropdown-option-visible">
                            {option.name} (₹{option.rate}/{option.unit})
                          </option>
                        ))}
                      </select>
                      <div className="flex items-center justify-between mt-2">
                        <div className="text-xs text-gray-500">
                          Cost: ₹{flooringCost.toLocaleString()}
                        </div>
                        {selectedFlooringOption && (
                          <RecommendationBadge option={selectedFlooringOption} />
                        )}
                      </div>
                    </div>

                    {/* Paint */}
                    <div>
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                        <Palette className="w-4 h-4" />
                        Wall Paint
                      </label>
                      <select
                        value={selection.paintMaterial}
                        onChange={(e) => updateSelection(room.id, { paintMaterial: e.target.value })}
                        className="enhanced-dropdown w-full"
                      >
                        {paintOptions.map((option) => (
                          <option key={option.name} value={option.name} className="dropdown-option-visible">
                            {option.name} (₹{option.rate}/{option.unit})
                          </option>
                        ))}
                      </select>
                      <div className="flex items-center justify-between mt-2">
                        <div className="text-xs text-gray-500">
                          Cost: ₹{paintCost.toLocaleString()}
                        </div>
                        {selectedPaintOption && (
                          <RecommendationBadge option={selectedPaintOption} />
                        )}
                      </div>
                    </div>

                    {/* Electrical Points */}
                    <div>
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                        <Zap className="w-4 h-4" />
                        Electrical Points
                      </label>
                      <input
                        type="number"
                        value={selection.electricalPoints}
                        onChange={(e) => updateSelection(room.id, { electricalPoints: Number(e.target.value) })}
                        className="form-input"
                        min="0"
                      />
                      <div className="text-xs text-gray-500 mt-1">
                        Recommended: {Math.ceil(room.areaPerRoom / 100) * 2} points
                      </div>
                    </div>

                    {/* Plumbing Fixtures */}
                    <div>
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                        <Droplets className="w-4 h-4" />
                        Plumbing Fixtures
                      </label>
                      <input
                        type="number"
                        value={selection.plumbingFixtures}
                        onChange={(e) => updateSelection(room.id, { plumbingFixtures: Number(e.target.value) })}
                        className="form-input"
                        min="0"
                      />
                      <div className="text-xs text-gray-500 mt-1">
                        {room.type === 'Bathroom' ? 'Full bathroom set' : 'Additional fixtures'}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-lg font-semibold text-gray-800">
            Total Customization Cost: ₹{totalCost.toLocaleString()}
          </div>
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
            >
              Apply Customization
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}