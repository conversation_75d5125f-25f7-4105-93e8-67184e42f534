import React, { useState, useEffect } from 'react';
import { Check, X, Plus, AlertTriangle, Package } from 'lucide-react';
import { Component, componentAPI } from '../../lib/supabase';

interface BundleItem {
  component: Component;
  isRequired: boolean;
  isIncluded: boolean;
  quantity: number;
}

interface BundledComponentSelectorProps {
  bundleComponent: Component;
  onSelectionChange: (selectedItems: BundleItem[], totalCost: number) => void;
  roomArea: number;
}

export function BundledComponentSelector({ 
  bundleComponent, 
  onSelectionChange, 
  roomArea 
}: BundledComponentSelectorProps) {
  const [bundleItems, setBundleItems] = useState<BundleItem[]>([]);
  const [addOnComponents, setAddOnComponents] = useState<Component[]>([]);
  const [selectedAddOns, setSelectedAddOns] = useState<Component[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadBundleItems();
    loadAddOnComponents();
  }, [bundleComponent]);

  const loadBundleItems = async () => {
    setIsLoading(true);
    try {
      // Parse bundle specifications to get included items
      const bundleSpecs = bundleComponent.specifications || {};
      const includedItems = bundleSpecs.includes || [];

      // Load individual components that make up the bundle
      const allComponents = await componentAPI.getAll(bundleComponent.category);
      
      // Create bundle items based on specifications
      const items: BundleItem[] = [];
      
      // Default bathroom bundle items
      if (bundleComponent.category === 'Bathroom Fittings') {
        const wcComponent = allComponents.find(c => c.name.includes('WC'));
        const basinComponent = allComponents.find(c => c.name.includes('Washbasin'));
        const mirrorComponent = allComponents.find(c => c.name.includes('Mirror'));
        const diverterComponent = allComponents.find(c => c.name.includes('Diverter'));

        if (wcComponent) {
          items.push({
            component: wcComponent,
            isRequired: true,
            isIncluded: true,
            quantity: 1
          });
        }

        if (basinComponent) {
          items.push({
            component: basinComponent,
            isRequired: true,
            isIncluded: true,
            quantity: 1
          });
        }

        if (mirrorComponent) {
          items.push({
            component: mirrorComponent,
            isRequired: false,
            isIncluded: true,
            quantity: 1
          });
        }

        if (diverterComponent) {
          items.push({
            component: diverterComponent,
            isRequired: true,
            isIncluded: true,
            quantity: 1
          });
        }
      }

      setBundleItems(items);
    } catch (error) {
      console.error('Error loading bundle items:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadAddOnComponents = async () => {
    try {
      // Load additional components that can be added to the bundle
      const allComponents = await componentAPI.getAll(bundleComponent.category);
      
      // Filter out components that are already in the bundle
      const addOns = allComponents.filter(component => 
        !bundleItems.some(item => item.component.id === component.id) &&
        !component.name.includes('Bundle')
      );

      setAddOnComponents(addOns);
    } catch (error) {
      console.error('Error loading add-on components:', error);
    }
  };

  const toggleBundleItem = (itemIndex: number) => {
    const item = bundleItems[itemIndex];
    
    // Prevent unchecking required items
    if (item.isRequired && item.isIncluded) {
      return;
    }

    const updatedItems = [...bundleItems];
    updatedItems[itemIndex] = {
      ...item,
      isIncluded: !item.isIncluded
    };
    
    setBundleItems(updatedItems);
    calculateAndNotifyTotal(updatedItems, selectedAddOns);
  };

  const addAddOnComponent = (component: Component) => {
    const newAddOns = [...selectedAddOns, component];
    setSelectedAddOns(newAddOns);
    calculateAndNotifyTotal(bundleItems, newAddOns);
  };

  const removeAddOnComponent = (componentId: string) => {
    const newAddOns = selectedAddOns.filter(c => c.id !== componentId);
    setSelectedAddOns(newAddOns);
    calculateAndNotifyTotal(bundleItems, newAddOns);
  };

  const calculateAndNotifyTotal = (items: BundleItem[], addOns: Component[]) => {
    const bundleItemsCost = items
      .filter(item => item.isIncluded)
      .reduce((sum, item) => sum + (item.component.unit_price * item.quantity), 0);
    
    const addOnsCost = addOns.reduce((sum, component) => sum + component.unit_price, 0);
    
    const totalCost = bundleItemsCost + addOnsCost;
    
    onSelectionChange(items, totalCost);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Bundle Header */}
      <div className="bg-blue-50 rounded-lg p-4 border-2 border-blue-200">
        <div className="flex items-center gap-3 mb-2">
          <Package className="w-5 h-5 text-blue-600" />
          <h3 className="font-semibold text-blue-800">{bundleComponent.name}</h3>
        </div>
        <p className="text-blue-700 text-sm">
          Complete bathroom fittings bundle with all essential items. 
          Customize by unchecking optional items or adding extras.
        </p>
      </div>

      {/* Bundle Items Checklist */}
      <div>
        <h4 className="font-semibold text-gray-800 mb-3">Included in Bundle</h4>
        <div className="space-y-3">
          {bundleItems.map((item, index) => (
            <div
              key={item.component.id}
              className={`border rounded-lg p-3 transition-all ${
                item.isIncluded ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <div className="mt-1">
                    <input
                      type="checkbox"
                      checked={item.isIncluded}
                      onChange={() => toggleBundleItem(index)}
                      disabled={item.isRequired && item.isIncluded}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h5 className="font-medium text-gray-800">{item.component.name}</h5>
                      {item.isRequired && (
                        <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">
                          Required
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{item.component.brand}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-sm font-semibold text-green-600">
                        ₹{(item.component.unit_price * item.quantity).toLocaleString()}
                      </span>
                      <span className="text-xs text-gray-500">
                        (₹{item.component.unit_price.toLocaleString()} × {item.quantity})
                      </span>
                    </div>
                  </div>
                </div>
                
                {item.isRequired && item.isIncluded && (
                  <div className="flex items-center gap-1 text-xs text-red-600">
                    <AlertTriangle className="w-3 h-3" />
                    <span>Essential</span>
                  </div>
                )}
              </div>

              {/* Warning for trying to uncheck required items */}
              {item.isRequired && !item.isIncluded && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                  <div className="flex items-center gap-2 text-red-700 text-xs">
                    <AlertTriangle className="w-3 h-3" />
                    <span>This item is essential for a functional bathroom and cannot be removed.</span>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Add-on Section */}
      <div>
        <h4 className="font-semibold text-gray-800 mb-3">Available Add-ons</h4>
        
        {/* Selected Add-ons */}
        {selectedAddOns.length > 0 && (
          <div className="mb-4">
            <h5 className="text-sm font-medium text-gray-700 mb-2">Selected Add-ons</h5>
            <div className="space-y-2">
              {selectedAddOns.map((component) => (
                <div key={component.id} className="flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded">
                  <div>
                    <span className="font-medium text-gray-800">{component.name}</span>
                    <span className="text-sm text-green-600 ml-2">
                      +₹{component.unit_price.toLocaleString()}
                    </span>
                  </div>
                  <button
                    onClick={() => removeAddOnComponent(component.id)}
                    className="p-1 hover:bg-red-100 rounded transition-colors"
                  >
                    <X className="w-4 h-4 text-red-500" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Available Add-ons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {addOnComponents
            .filter(component => !selectedAddOns.some(selected => selected.id === component.id))
            .map((component) => (
              <div
                key={component.id}
                className="border border-gray-200 rounded-lg p-3 hover:border-blue-300 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h6 className="font-medium text-gray-800">{component.name}</h6>
                    <p className="text-sm text-gray-600">{component.brand}</p>
                    <div className="text-sm font-semibold text-blue-600 mt-1">
                      +₹{component.unit_price.toLocaleString()}
                    </div>
                  </div>
                  <button
                    onClick={() => addAddOnComponent(component)}
                    className="p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
        </div>

        {addOnComponents.length === 0 && (
          <div className="text-center py-6 text-gray-500">
            <Package className="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p>No additional add-ons available for this bundle</p>
          </div>
        )}
      </div>

      {/* Total Cost Summary */}
      <div className="bg-gray-50 rounded-lg p-4 border-2 border-gray-200">
        <h4 className="font-semibold text-gray-800 mb-2">Bundle Summary</h4>
        <div className="space-y-1 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Included Items:</span>
            <span className="font-medium">
              {bundleItems.filter(item => item.isIncluded).length} of {bundleItems.length}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Add-ons:</span>
            <span className="font-medium">{selectedAddOns.length}</span>
          </div>
          <div className="border-t border-gray-300 pt-2 mt-2">
            <div className="flex justify-between">
              <span className="font-semibold text-gray-800">Total Bundle Cost:</span>
              <span className="text-lg font-bold text-green-600">
                {formatCurrency(
                  bundleItems
                    .filter(item => item.isIncluded)
                    .reduce((sum, item) => sum + (item.component.unit_price * item.quantity), 0) +
                  selectedAddOns.reduce((sum, component) => sum + component.unit_price, 0)
                )}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}