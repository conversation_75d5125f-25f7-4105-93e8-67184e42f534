@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base accessibility improvements */
@layer base {
  html {
    font-size: 16px;
    line-height: 1.5;
  }
  
  body {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
  }
  
  /* Ensure minimum touch target size */
  button, 
  input[type="button"], 
  input[type="submit"], 
  input[type="reset"], 
  input[type="checkbox"], 
  input[type="radio"], 
  select,
  a {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Focus indicators */
  *:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
  
  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
  
  .focus\:not-sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }
  
  /* COMPREHENSIVE INPUT STYLING FOR MAXIMUM VISIBILITY */
  input[type="number"],
  input[type="text"],
  input[type="email"],
  input[type="tel"] {
    background-color: #ffffff !important;
    border: 3px solid #2563eb !important;
    border-radius: 0.75rem !important;
    padding: 0.875rem 1.25rem !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  }
  
  input[type="number"]:focus,
  input[type="text"]:focus,
  input[type="email"]:focus,
  input[type="tel"]:focus {
    border-color: #1d4ed8 !important;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
    outline: none !important;
  }
  
  input[type="number"]:hover,
  input[type="text"]:hover,
  input[type="email"]:hover,
  input[type="tel"]:hover {
    border-color: #1d4ed8 !important;
    box-shadow: 0 6px 8px -2px rgba(0, 0, 0, 0.15) !important;
  }
  
  /* COMPREHENSIVE DROPDOWN STYLING FOR MAXIMUM VISIBILITY */
  select {
    background-color: #ffffff !important;
    border: 3px solid #2563eb !important;
    border-radius: 0.75rem !important;
    padding: 0.875rem 1.25rem !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%232563eb' stroke-linecap='round' stroke-linejoin='round' stroke-width='2.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
    background-position: right 0.75rem center !important;
    background-repeat: no-repeat !important;
    background-size: 1.5em 1.5em !important;
    padding-right: 3rem !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  }
  
  select:focus {
    border-color: #1d4ed8 !important;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
    outline: none !important;
  }
  
  select:hover {
    border-color: #1d4ed8 !important;
    box-shadow: 0 6px 8px -2px rgba(0, 0, 0, 0.15) !important;
  }
  
  /* CRITICAL: Dropdown options styling for maximum visibility */
  select option {
    background-color: #ffffff !important;
    color: #1f2937 !important;
    padding: 0.75rem !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    border: 1px solid #e5e7eb !important;
    margin: 2px 0 !important;
  }
  
  select option:checked,
  select option:selected {
    background-color: #2563eb !important;
    color: #ffffff !important;
    font-weight: 700 !important;
  }
  
  select option:hover {
    background-color: #dbeafe !important;
    color: #1e40af !important;
  }
  
  /* Force visibility for all select elements */
  select[class*="border-gray"],
  select[class*="border-blue"],
  select[class*="enhanced-dropdown"],
  select[class*="material-dropdown"] {
    background-color: #ffffff !important;
    border: 3px solid #2563eb !important;
    color: #1f2937 !important;
    font-weight: 600 !important;
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    select,
    input[type="number"],
    input[type="text"],
    input[type="email"],
    input[type="tel"] {
      border: 4px solid #000000 !important;
      background-color: #ffffff !important;
      color: #000000 !important;
      font-weight: 700 !important;
    }
    
    select option {
      background-color: #ffffff !important;
      color: #000000 !important;
      border: 2px solid #000000 !important;
      font-weight: 700 !important;
    }
    
    .bg-gradient-to-br {
      background: #000;
      color: #fff;
    }
    
    .text-gray-600 {
      color: #000;
    }
    
    .border-gray-200 {
      border-color: #000;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
  
  /* Dark mode support with forced visibility */
  @media (prefers-color-scheme: dark) {
    select,
    input[type="number"],
    input[type="text"],
    input[type="email"],
    input[type="tel"] {
      background-color: #ffffff !important;
      color: #1f2937 !important;
      border-color: #2563eb !important;
    }
    
    select option {
      background-color: #ffffff !important;
      color: #1f2937 !important;
    }
    
    .bg-white {
      background-color: #1f2937;
      color: #f9fafb;
    }
    
    .text-gray-800 {
      color: #f9fafb;
    }
    
    .text-gray-600 {
      color: #d1d5db;
    }
    
    .border-gray-200 {
      border-color: #374151;
    }
  }
}

/* Custom components */
@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 min-h-[44px];
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-3 px-6 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 min-h-[44px];
  }
  
  .form-input {
    background-color: #ffffff !important;
    border: 3px solid #2563eb !important;
    border-radius: 0.75rem !important;
    padding: 0.875rem 1.25rem !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    width: 100%;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    min-height: 44px;
  }
  
  .form-input:focus {
    border-color: #1d4ed8 !important;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
    outline: none !important;
  }
  
  .form-input:hover {
    border-color: #1d4ed8 !important;
    box-shadow: 0 6px 8px -2px rgba(0, 0, 0, 0.15) !important;
  }
  
  .form-label {
    @apply block text-sm font-semibold text-gray-700 mb-2;
  }
  
  /* MAXIMUM VISIBILITY DROPDOWN CLASSES */
  .enhanced-dropdown {
    background-color: #ffffff !important;
    border: 3px solid #2563eb !important;
    border-radius: 0.75rem !important;
    padding: 0.875rem 1.25rem !important;
    color: #1f2937 !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%232563eb' stroke-linecap='round' stroke-linejoin='round' stroke-width='2.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
    background-position: right 0.75rem center !important;
    background-repeat: no-repeat !important;
    background-size: 1.5em 1.5em !important;
    padding-right: 3rem !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
  }
  
  .enhanced-dropdown:hover {
    border-color: #1d4ed8 !important;
    box-shadow: 0 6px 8px -2px rgba(0, 0, 0, 0.15) !important;
  }
  
  .enhanced-dropdown:focus {
    border-color: #1d4ed8 !important;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.25) !important;
    outline: none !important;
  }
  
  /* Material selection dropdown with maximum visibility */
  .material-dropdown {
    background-color: #ffffff !important;
    border: 3px solid #2563eb !important;
    border-radius: 0.5rem !important;
    padding: 0.75rem 1rem !important;
    color: #1f2937 !important;
    font-weight: 600 !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%232563eb' stroke-linecap='round' stroke-linejoin='round' stroke-width='2.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
    background-position: right 0.5rem center !important;
    background-repeat: no-repeat !important;
    background-size: 1.25em 1.25em !important;
    padding-right: 2.5rem !important;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
  }
  
  .material-dropdown:hover {
    border-color: #1d4ed8 !important;
    background-color: #f0f9ff !important;
  }
  
  .material-dropdown:focus {
    border-color: #1d4ed8 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25) !important;
    background-color: #ffffff !important;
  }
  
  /* Critical timeline styling with improved contrast */
  .critical-timeline {
    @apply bg-yellow-50 border-2 border-yellow-400 rounded-lg p-4;
  }
  
  .critical-timeline .timeline-item {
    @apply flex items-center gap-2 text-gray-800;
  }
  
  .critical-timeline .timeline-dot {
    @apply w-2 h-2 rounded-full border-2 border-gray-500;
  }
  
  .critical-timeline .timeline-dot.high {
    @apply bg-red-500 border-red-700;
  }
  
  .critical-timeline .timeline-dot.medium {
    @apply bg-yellow-500 border-yellow-700;
  }
  
  .critical-timeline .timeline-dot.low {
    @apply bg-green-500 border-green-700;
  }
  
  /* Calculation breakdown specific styles */
  .calculation-breakdown-table {
    @apply border-collapse border-2 border-gray-300;
  }
  
  .calculation-breakdown-table th,
  .calculation-breakdown-table td {
    @apply border border-gray-300 px-4 py-2;
  }
  
  .calculation-breakdown-table th {
    @apply bg-gray-100 font-semibold text-gray-800;
  }
  
  .calculation-breakdown-table td {
    @apply text-gray-700;
  }
}

/* Ensure proper color contrast */
@layer utilities {
  .text-contrast {
    color: contrast(var(--bg-color) vs #000000, #ffffff);
  }
  
  /* High contrast borders */
  .border-contrast {
    border-color: contrast(var(--bg-color) vs #000000, #ffffff);
  }
  
  /* FORCED DROPDOWN VISIBILITY UTILITIES */
  .dropdown-visible {
    background-color: #ffffff !important;
    color: #1f2937 !important;
    border: 3px solid #2563eb !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  }
  
  .dropdown-option-visible {
    background-color: #ffffff !important;
    color: #1f2937 !important;
    font-weight: 600 !important;
    padding: 0.75rem !important;
    border: 1px solid #e5e7eb !important;
  }
  
  .dropdown-option-visible:hover {
    background-color: #dbeafe !important;
    color: #1e40af !important;
  }
  
  .dropdown-option-visible:checked,
  .dropdown-option-visible:selected {
    background-color: #2563eb !important;
    color: #ffffff !important;
    font-weight: 700 !important;
  }
  
  /* Animation utilities */
  .animate-pulse-soft {
    animation: pulse-soft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  @keyframes pulse-soft {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }
  
  .animate-highlight {
    animation: highlight 2s ease-out;
  }
  
  @keyframes highlight {
    0% {
      background-color: rgba(59, 130, 246, 0.1);
      transform: scale(1.02);
    }
    100% {
      background-color: transparent;
      transform: scale(1);
    }
  }
  
  /* Border width utilities for better visibility */
  .border-3 {
    border-width: 3px;
  }
  
  .border-4 {
    border-width: 4px;
  }
  
  .ring-3 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  }
  
  .ring-4 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  }
}

/* FORCE OVERRIDE FOR ALL EXISTING INPUT AND DROPDOWN CLASSES */
select[class*="border"],
select[class*="bg-"],
select[class*="text-"],
input[type="number"][class*="border"],
input[type="text"][class*="border"],
input[type="email"][class*="border"],
input[type="tel"][class*="border"],
.form-select,
.custom-select,
input[type="select"] {
  background-color: #ffffff !important;
  border: 3px solid #2563eb !important;
  color: #1f2937 !important;
  font-weight: 600 !important;
  padding: 0.875rem 1.25rem !important;
  border-radius: 0.75rem !important;
}

/* Specific overrides for problematic selectors */
.w-full.px-3.py-2.border.border-gray-200.rounded-lg,
.w-full.px-3.py-3.text-base.border.border-gray-200.rounded-lg,
.text-sm.bg-white.border-2.border-blue-200.rounded-lg,
.w-full.px-4.py-3.text-base.border-2.border-gray-300.rounded-lg {
  background-color: #ffffff !important;
  border: 3px solid #2563eb !important;
  color: #1f2937 !important;
  font-weight: 600 !important;
}