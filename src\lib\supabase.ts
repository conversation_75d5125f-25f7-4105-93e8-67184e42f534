import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database Types
export interface Component {
  id: string
  name: string
  category: string
  sub_category?: string
  brand?: string
  image_url?: string
  cost_model: 'per_unit' | 'per_sqm' | 'task_based'
  unit_price: number
  unit: string
  associated_task_id?: string
  specifications: Record<string, any>
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Task {
  id: string
  name: string
  description?: string
  category: string
  complexity_level: 'basic' | 'intermediate' | 'advanced'
  estimated_duration_hours: number
  is_active: boolean
  created_at: string
  updated_at: string
  task_requirements?: TaskRequirement[]
}

export interface LaborRate {
  id: string
  name: string
  category: string
  skill_level: 'unskilled' | 'semiskilled' | 'skilled' | 'specialist'
  unit: string
  rates: {
    good: number
    better: number
    best: number
  }
  productivity: {
    output_per_day: number
    unit: string
  }
  location: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface TaskRequirement {
  id: string
  task_id: string
  component_id?: string
  labor_id?: string
  quantity_per_sqm: number
  is_optional: boolean
  requirement_type: 'material' | 'labor' | 'tool'
  created_at: string
  components?: Component
  labor_rates?: LaborRate
}

export interface UserProject {
  id: string
  user_id: string
  project_name: string
  project_data: Record<string, any>
  room_configurations: any[]
  selected_components: Record<string, any>
  total_cost: number
  is_shared: boolean
  share_token?: string
  created_at: string
  updated_at: string
}

export interface UserOverride {
  id: string
  project_id: string
  component_id: string
  original_quantity?: number
  override_quantity?: number
  original_rate?: number
  override_rate?: number
  override_reason?: string
  created_at: string
}

export interface UIDefaults {
  id: string
  config_name: string
  config_data: Record<string, any>
  version: number
  is_active: boolean
  created_by?: string
  created_at: string
  updated_at: string
}

// New interfaces for engineering standards
export interface EngineeringStandard {
  id: string
  category: string
  name: string
  value: number | string
  unit: string
  description?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface ConcreteMix {
  id: string
  grade: string
  cement_bags_per_m3: number
  sand_cft_per_m3: number
  aggregate_cft_per_m3: number
  water_cement_ratio: number
  applications?: string[]
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface SteelReinforcement {
  id: string
  structural_element: string
  steel_ratio_kg_per_m3: number
  notes?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface MaterialConsumption {
  id: string
  material: string
  consumption_value: number
  unit: string
  description?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface WastageFactor {
  id: string
  material: string
  wastage_percentage: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface LocationMultiplier {
  id: string
  location: string
  material_multiplier: number
  labor_multiplier: number
  notes?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface ProfessionalFee {
  id: string
  service: string
  tier1_percentage: number
  tier2_percentage: number
  tier3_percentage: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface RegulatoryFee {
  id: string
  fee_name: string
  authority: string
  rate: number
  unit: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface SystemConfig {
  id: string
  gst_materials: number
  gst_services: number
  default_contingency: number
  default_profit_margin: number
  created_at: string
  updated_at: string
}

// New interface for comprehensive engineering standards
export interface EngineeringStandards {
  structuralAssumptions: {
    foundationDepth: number;
    gridSpacing: number;
    slabThickness: number;
    floorHeight?: number; // NEW: Admin configurable floor height
    beamWidth?: number; // NEW: Admin configurable beam width (mm)
    beamDepth?: number; // NEW: Admin configurable beam depth (mm)
    columnSizing?: {
      lowRise?: number; // NEW: Column size for ≤2 floors (mm)
      midRise?: number; // NEW: Column size for 3-4 floors (mm)
      highRise?: number; // NEW: Column size for 5+ floors (mm)
    };
    // Legacy format for backward compatibility
    columnSizingLegacy?: {
      floors: string;
      column_size: string;
      beam_width: number;
    }[];
  };
  concreteMixes: Record<string, ConcreteMix>;
  steelRatios: {
    foundations: Record<string, number>;
    columns: Record<string, number>;
    beams: Record<string, number>;
    slabs: Record<string, number>;
  };
  materialConsumption: {
    bricksPerSqm_230mm: number;
    bricksPerSqm_115mm: number;
    aacBlocksPerSqm_200mm: number;
    aacBlocksPerSqm_150mm: number;
    wastageFactors: Record<string, number>;
  };
  regionalData: {
    location: string;
    materialMultiplier: number;
    laborMultiplier: number;
    authority: string;
    approvalFeeRate: number;
    professionalFees: {
      architect: {
        tier1: number;
        tier2: number;
        tier3: number;
      };
      structural: {
        tier1: number;
        tier2: number;
        tier3: number;
      };
      mep: {
        tier1: number;
        tier2: number;
        tier3: number;
      };
    };
    regulatoryFees: RegulatoryFee[];
  };
  systemConfig: {
    gstOnMaterials: number;
    gstOnServices: number;
    defaultContingency: number;
    defaultProfitMargin: number;
  };
  // NEW: Admin configurable material rates
  materialRates?: {
    excavation?: number; // ₹/m³
    plaster?: number; // ₹/sqm
    electrical?: number; // ₹/point
    plumbing?: number; // ₹/point
    waterproofing?: {
      good?: number; // ₹/sqm
      better?: number; // ₹/sqm
      best?: number; // ₹/sqm
    };
  };
  // NEW: Admin configurable calculation factors
  calculationFactors?: {
    plasterCoverageFactor?: number; // Internal plaster coverage multiplier
    doorDensity?: number; // sqm per door
    windowDensity?: number; // sqm per window
    excavationExtraFactor?: number; // Extra excavation percentage
    internalWallFactor?: number; // Internal wall area factor
    openingDistribution?: {
      external?: number; // % of openings on external walls
      internal?: number; // % of openings on internal walls
    };
  };
  // NEW: Admin configurable opening sizes (IS 1200 compliant)
  openingSizes?: {
    doorHeight?: number; // meters
    doorWidth?: number; // meters
    windowHeight?: number; // meters
    windowWidth?: number; // meters
  };
}

// API Functions
export const componentAPI = {
  async getAll(category?: string) {
    let query = supabase
      .from('components')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (category) {
      query = query.eq('category', category)
    }

    const { data, error } = await query
    if (error) throw error
    return data as Component[]
  },

  async getById(id: string) {
    const { data, error } = await supabase
      .from('components')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data as Component
  },

  async create(component: Omit<Component, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('components')
      .insert(component)
      .select()
      .single()

    if (error) throw error
    return data as Component
  },

  async update(id: string, updates: Partial<Component>) {
    const { data, error } = await supabase
      .from('components')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as Component
  },

  async delete(id: string) {
    const { error } = await supabase
      .from('components')
      .update({ is_active: false })
      .eq('id', id)

    if (error) throw error
  }
}

export const taskAPI = {
  async getAll() {
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        task_requirements (
          *,
          components (*),
          labor_rates (*)
        )
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  async getById(id: string) {
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        task_requirements (
          *,
          components (*),
          labor_rates (*)
        )
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  async create(task: Omit<Task, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('tasks')
      .insert(task)
      .select()
      .single()

    if (error) throw error
    return data as Task
  },

  async createTaskRequirement(requirement: Omit<TaskRequirement, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('task_requirements')
      .insert(requirement)
      .select()
      .single()

    if (error) throw error
    return data as TaskRequirement
  },

  async updateTaskRequirements(taskId: string, requirements: Omit<TaskRequirement, 'id' | 'created_at'>[]) {
    // First delete existing requirements
    const { error: deleteError } = await supabase
      .from('task_requirements')
      .delete()
      .eq('task_id', taskId)

    if (deleteError) throw deleteError

    // Then insert new requirements
    const { data, error } = await supabase
      .from('task_requirements')
      .insert(requirements.map(req => ({ ...req, task_id: taskId })))
      .select()

    if (error) throw error
    return data as TaskRequirement[]
  }
}

export const laborRateAPI = {
  async getAll() {
    const { data, error } = await supabase
      .from('labor_rates')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data as LaborRate[]
  },

  async getById(id: string) {
    const { data, error } = await supabase
      .from('labor_rates')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data as LaborRate
  },

  async create(laborRate: Omit<LaborRate, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('labor_rates')
      .insert(laborRate)
      .select()
      .single()

    if (error) throw error
    return data as LaborRate
  },

  async update(id: string, updates: Partial<LaborRate>) {
    const { data, error } = await supabase
      .from('labor_rates')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as LaborRate
  }
}

export const uiDefaultsAPI = {
  async getDefaults() {
    const { data, error } = await supabase
      .from('ui_defaults')
      .select('*')
      .eq('config_name', 'default_selections')
      .eq('is_active', true)
      .single()

    if (error) throw error
    return data as UIDefaults
  },

  async updateDefaults(configData: Record<string, any>) {
    const { data, error } = await supabase
      .from('ui_defaults')
      .update({ 
        config_data: configData,
        updated_at: new Date().toISOString()
      })
      .eq('config_name', 'default_selections')
      .select()
      .single()

    if (error) throw error
    return data as UIDefaults
  }
}

export const projectAPI = {
  async create(project: Omit<UserProject, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('user_projects')
      .insert(project)
      .select()
      .single()

    if (error) throw error
    return data as UserProject
  },

  async getUserProjects(userId: string) {
    const { data, error } = await supabase
      .from('user_projects')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })

    if (error) throw error
    return data as UserProject[]
  },

  async update(id: string, updates: Partial<UserProject>) {
    const { data, error } = await supabase
      .from('user_projects')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as UserProject
  }
}

export const overrideAPI = {
  async create(override: Omit<UserOverride, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('user_overrides')
      .insert(override)
      .select()
      .single()

    if (error) throw error
    return data as UserOverride
  },

  async getByProject(projectId: string) {
    const { data, error } = await supabase
      .from('user_overrides')
      .select('*')
      .eq('project_id', projectId)

    if (error) throw error
    return data as UserOverride[]
  }
}

// New API functions for configurable parameters
export const materialRatesAPI = {
  async getByCategory(category: string, qualityTier: string = 'better', location: string = 'delhi') {
    const { data, error } = await supabase
      .from('material_rates')
      .select('*')
      .eq('category', category)
      .eq('quality_tier', qualityTier)
      .eq('location', location)
      .eq('is_active', true)
      .order('name')

    if (error) {
      console.error('Error fetching material rates:', error)
      return []
    }
    return data || []
  },

  async getAll(location: string = 'delhi') {
    const { data, error } = await supabase
      .from('material_rates')
      .select('*')
      .eq('location', location)
      .eq('is_active', true)
      .order('category, quality_tier, name')

    if (error) {
      console.error('Error fetching all material rates:', error)
      return []
    }
    return data || []
  },

  async getWaterproofingRates(location: string = 'delhi') {
    const { data, error } = await supabase
      .from('material_rates')
      .select('quality_tier, rate')
      .eq('category', 'waterproofing')
      .eq('name', 'Membrane Waterproofing')
      .eq('location', location)
      .eq('is_active', true)

    if (error) {
      console.error('Error fetching waterproofing rates:', error)
      return { good: 60, better: 85, best: 120 }
    }

    const rates = { good: 60, better: 85, best: 120 }
    data?.forEach(item => {
      rates[item.quality_tier as keyof typeof rates] = item.rate
    })
    return rates
  }
}

export const calculationFactorsAPI = {
  async getByCategory(category: string) {
    const { data, error } = await supabase
      .from('calculation_factors')
      .select('*')
      .eq('category', category)
      .eq('is_active', true)
      .order('factor_name')

    if (error) {
      console.error('Error fetching calculation factors:', error)
      return []
    }
    return data || []
  },

  async getAll() {
    const { data, error } = await supabase
      .from('calculation_factors')
      .select('*')
      .eq('is_active', true)
      .order('category, factor_name')

    if (error) {
      console.error('Error fetching all calculation factors:', error)
      return []
    }
    return data || []
  },

  async getFactorValue(factorName: string): Promise<number | null> {
    const { data, error } = await supabase
      .from('calculation_factors')
      .select('factor_value')
      .eq('factor_name', factorName)
      .eq('is_active', true)
      .single()

    if (error) {
      console.error(`Error fetching factor ${factorName}:`, error)
      return null
    }
    return data?.factor_value || null
  }
}

export const openingSizesAPI = {
  async getByType(openingType: string) {
    const { data, error } = await supabase
      .from('opening_sizes')
      .select('*')
      .eq('opening_type', openingType)
      .eq('is_active', true)
      .order('dimension_type')

    if (error) {
      console.error('Error fetching opening sizes:', error)
      return []
    }
    return data || []
  },

  async getAll() {
    const { data, error } = await supabase
      .from('opening_sizes')
      .select('*')
      .eq('is_active', true)
      .order('opening_type, dimension_type')

    if (error) {
      console.error('Error fetching all opening sizes:', error)
      return []
    }
    return data || []
  },

  async getDoorDimensions() {
    const { data, error } = await supabase
      .from('opening_sizes')
      .select('dimension_type, value')
      .eq('opening_type', 'door')
      .eq('is_active', true)

    if (error) {
      console.error('Error fetching door dimensions:', error)
      return { height: 2.1, width: 1.0 }
    }

    const dimensions = { height: 2.1, width: 1.0 }
    data?.forEach(item => {
      dimensions[item.dimension_type as keyof typeof dimensions] = item.value
    })
    return dimensions
  },

  async getWindowDimensions() {
    const { data, error } = await supabase
      .from('opening_sizes')
      .select('dimension_type, value')
      .eq('opening_type', 'window')
      .eq('is_active', true)

    if (error) {
      console.error('Error fetching window dimensions:', error)
      return { height: 1.5, width: 1.2 }
    }

    const dimensions = { height: 1.5, width: 1.2 }
    data?.forEach(item => {
      dimensions[item.dimension_type as keyof typeof dimensions] = item.value
    })
    return dimensions
  }
}

// New API functions for engineering standards
export const engineeringStandardsAPI = {
  async getStructuralAssumptions() {
    const { data, error } = await supabase
      .from('engineering_standards')
      .select('*')
      .eq('category', 'structural')
      .eq('is_active', true)

    if (error) throw error
    return data as EngineeringStandard[]
  },

  async getConcreteMixes() {
    const { data, error } = await supabase
      .from('concrete_mixes')
      .select('*')
      .eq('is_active', true)
      .order('grade')

    if (error) throw error
    return data as ConcreteMix[]
  },

  async getSteelReinforcement() {
    const { data, error } = await supabase
      .from('steel_reinforcement')
      .select('*')
      .eq('is_active', true)

    if (error) throw error
    return data as SteelReinforcement[]
  },

  async getMaterialConsumption() {
    const { data, error } = await supabase
      .from('material_consumption')
      .select('*')
      .eq('is_active', true)

    if (error) throw error
    return data as MaterialConsumption[]
  },

  async getWastageFactors() {
    const { data, error } = await supabase
      .from('wastage_factors')
      .select('*')
      .eq('is_active', true)

    if (error) throw error
    return data as WastageFactor[]
  }
}

// New API functions for regional data
export const regionalDataAPI = {
  async getLocationMultipliers() {
    const { data, error } = await supabase
      .from('location_multipliers')
      .select('*')
      .eq('is_active', true)

    if (error) throw error
    return data as LocationMultiplier[]
  },

  async getProfessionalFees() {
    const { data, error } = await supabase
      .from('professional_fees')
      .select('*')
      .eq('is_active', true)

    if (error) throw error
    return data as ProfessionalFee[]
  },

  async getRegulatoryFees() {
    const { data, error } = await supabase
      .from('regulatory_fees')
      .select('*')
      .eq('is_active', true)

    if (error) throw error
    return data as RegulatoryFee[]
  }
}

// System configuration API
export const systemConfigAPI = {
  async getSystemConfig() {
    const { data, error } = await supabase
      .from('system_config')
      .select('*')
      .single()

    if (error) throw error
    return data as SystemConfig
  },

  async updateSystemConfig(updates: Partial<SystemConfig>) {
    const { data, error } = await supabase
      .from('system_config')
      .update(updates)
      .eq('id', 1) // Assuming there's only one system config record
      .select()
      .single()

    if (error) throw error
    return data as SystemConfig
  }
}

// New function to fetch all calculation standards in one call
export async function fetchCalculationStandards(locationId: string): Promise<EngineeringStandards> {
  try {
    // Fetch all required data in parallel including new configurable parameters
    const [
      structuralAssumptions,
      concreteMixes,
      steelReinforcement,
      materialConsumption,
      wastageFactors,
      locationMultipliers,
      professionalFees,
      regulatoryFees,
      systemConfig,
      materialRates,
      calculationFactors,
      openingSizes
    ] = await Promise.all([
      engineeringStandardsAPI.getStructuralAssumptions(),
      engineeringStandardsAPI.getConcreteMixes(),
      engineeringStandardsAPI.getSteelReinforcement(),
      engineeringStandardsAPI.getMaterialConsumption(),
      engineeringStandardsAPI.getWastageFactors(),
      regionalDataAPI.getLocationMultipliers(),
      regionalDataAPI.getProfessionalFees(),
      regionalDataAPI.getRegulatoryFees(),
      systemConfigAPI.getSystemConfig(),
      materialRatesAPI.getAll(locationId),
      calculationFactorsAPI.getAll(),
      openingSizesAPI.getAll()
    ]);

    // Process structural assumptions
    const foundationDepth = parseFloat(structuralAssumptions.find(s => s.name === 'Foundation Depth')?.value as string || '1.5');
    const gridSpacing = parseFloat(structuralAssumptions.find(s => s.name === 'Grid Spacing')?.value as string || '3.5');
    const slabThickness = parseFloat(structuralAssumptions.find(s => s.name === 'Slab Thickness')?.value as string || '150');

    // Process column sizing
    const columnSizing = structuralAssumptions
      .filter(s => s.name.includes('Column Size'))
      .map(s => {
        const floors = s.name.replace('Column Size ', '');
        const parts = (s.value as string).split('x');
        return {
          floors,
          column_size: s.value as string,
          beam_width: parseInt(parts[0]) - 50 // Beam width is typically 50mm less than column width
        };
      });

    // Process concrete mixes
    const concreteMixesMap: Record<string, ConcreteMix> = {};
    concreteMixes.forEach(mix => {
      concreteMixesMap[mix.grade] = mix;
    });

    // Process steel ratios
    const steelRatiosMap = {
      foundations: {} as Record<string, number>,
      columns: {} as Record<string, number>,
      beams: {} as Record<string, number>,
      slabs: {} as Record<string, number>
    };

    steelReinforcement.forEach(item => {
      const element = item.structural_element.toLowerCase();
      if (element.includes('foundation')) {
        steelRatiosMap.foundations[element.replace(' ', '_')] = item.steel_ratio_kg_per_m3;
      } else if (element.includes('column')) {
        steelRatiosMap.columns[element.replace(' ', '_')] = item.steel_ratio_kg_per_m3;
      } else if (element.includes('beam')) {
        steelRatiosMap.beams[element.replace(' ', '_')] = item.steel_ratio_kg_per_m3;
      } else if (element.includes('slab')) {
        steelRatiosMap.slabs[element.replace(' ', '_')] = item.steel_ratio_kg_per_m3;
      }
    });

    // Process material consumption
    const bricksPerSqm_230mm = materialConsumption.find(m => m.material === 'Red Clay Bricks (230mm wall)')?.consumption_value || 110;
    const bricksPerSqm_115mm = materialConsumption.find(m => m.material === 'Red Clay Bricks (115mm wall)')?.consumption_value || 55;
    const aacBlocksPerSqm_200mm = materialConsumption.find(m => m.material === 'AAC Blocks (200mm wall)')?.consumption_value || 8.33;
    const aacBlocksPerSqm_150mm = materialConsumption.find(m => m.material === 'AAC Blocks (150mm wall)')?.consumption_value || 11.11;

    // Process wastage factors
    const wastageFactorsMap: Record<string, number> = {};
    wastageFactors.forEach(factor => {
      wastageFactorsMap[factor.material.toLowerCase()] = factor.wastage_percentage;
    });

    // Process location data
    const locationData = locationMultipliers.find(l => l.location.toLowerCase() === locationId.toLowerCase());
    if (!locationData) {
      throw new Error(`Location data not found for: ${locationId}`);
    }

    // Process professional fees
    const architectFees = professionalFees.find(f => f.service.includes('Architect'));
    const structuralFees = professionalFees.find(f => f.service.includes('Structural'));
    const mepFees = professionalFees.find(f => f.service.includes('MEP'));

    // Process new configurable parameters
    const floorHeight = structuralAssumptions.find(s => s.name === 'floor_height')?.value || 3.0;
    const beamWidth = structuralAssumptions.find(s => s.name === 'beam_width')?.value || 300;
    const beamDepth = structuralAssumptions.find(s => s.name === 'beam_depth')?.value || 450;
    const columnSizeLowRise = structuralAssumptions.find(s => s.name === 'column_size_low_rise')?.value || 350;
    const columnSizeMidRise = structuralAssumptions.find(s => s.name === 'column_size_mid_rise')?.value || 400;
    const columnSizeHighRise = structuralAssumptions.find(s => s.name === 'column_size_high_rise')?.value || 450;

    // Process material rates
    const materialRatesMap: any = {};
    materialRates.forEach(rate => {
      if (!materialRatesMap[rate.category]) {
        materialRatesMap[rate.category] = {};
      }
      if (rate.category === 'waterproofing') {
        if (!materialRatesMap[rate.category][rate.quality_tier]) {
          materialRatesMap[rate.category][rate.quality_tier] = rate.rate;
        }
      } else {
        materialRatesMap[rate.category] = rate.rate;
      }
    });

    // Process calculation factors
    const calculationFactorsMap: any = {};
    calculationFactors.forEach(factor => {
      calculationFactorsMap[factor.factor_name] = factor.factor_value;
    });

    // Process opening sizes
    const openingSizesMap: any = {};
    openingSizes.forEach(opening => {
      if (!openingSizesMap[opening.opening_type]) {
        openingSizesMap[opening.opening_type] = {};
      }
      openingSizesMap[opening.opening_type][opening.dimension_type] = opening.value;
    });

    // Construct the final standards object
    const standards: EngineeringStandards = {
      structuralAssumptions: {
        foundationDepth,
        gridSpacing,
        slabThickness,
        floorHeight,
        beamWidth,
        beamDepth,
        columnSizing: {
          lowRise: columnSizeLowRise,
          midRise: columnSizeMidRise,
          highRise: columnSizeHighRise
        },
        columnSizingLegacy: columnSizing.length > 0 ? columnSizing : [
          { floors: 'G+1', column_size: '350 x 350', beam_width: 300 },
          { floors: 'G+2 to G+3', column_size: '400 x 400', beam_width: 300 },
          { floors: 'G+4 and above', column_size: '450 x 450', beam_width: 350 }
        ]
      },
      concreteMixes: concreteMixesMap,
      steelRatios: steelRatiosMap,
      materialConsumption: {
        bricksPerSqm_230mm,
        bricksPerSqm_115mm,
        aacBlocksPerSqm_200mm,
        aacBlocksPerSqm_150mm,
        wastageFactors: wastageFactorsMap
      },
      regionalData: {
        location: locationId,
        materialMultiplier: locationData.material_multiplier,
        laborMultiplier: locationData.labor_multiplier,
        authority: locationData.notes?.split(',')[0] || 'Local Authority',
        approvalFeeRate: regulatoryFees.find(f => f.fee_name.includes('Plan Approval'))?.rate || 400,
        professionalFees: {
          architect: {
            tier1: architectFees?.tier1_percentage || 4,
            tier2: architectFees?.tier2_percentage || 6,
            tier3: architectFees?.tier3_percentage || 8
          },
          structural: {
            tier1: structuralFees?.tier1_percentage || 1.5,
            tier2: structuralFees?.tier2_percentage || 2,
            tier3: structuralFees?.tier3_percentage || 2.5
          },
          mep: {
            tier1: mepFees?.tier1_percentage || 1,
            tier2: mepFees?.tier2_percentage || 1.5,
            tier3: mepFees?.tier3_percentage || 2
          }
        },
        regulatoryFees: regulatoryFees
      },
      systemConfig: {
        gstOnMaterials: systemConfig?.gst_materials || 18,
        gstOnServices: systemConfig?.gst_services || 18,
        defaultContingency: systemConfig?.default_contingency || 10,
        defaultProfitMargin: systemConfig?.default_profit_margin || 15
      },
      // NEW: Admin configurable material rates
      materialRates: {
        excavation: materialRatesMap.excavation || 450,
        plaster: materialRatesMap.plaster || 85,
        electrical: materialRatesMap.electrical || 350,
        plumbing: materialRatesMap.plumbing || 500,
        waterproofing: {
          good: materialRatesMap.waterproofing?.good || 60,
          better: materialRatesMap.waterproofing?.better || 85,
          best: materialRatesMap.waterproofing?.best || 120
        }
      },
      // NEW: Admin configurable calculation factors
      calculationFactors: {
        plasterCoverageFactor: calculationFactorsMap.plaster_coverage_factor || 3.0,
        doorDensity: calculationFactorsMap.door_density || 200,
        windowDensity: calculationFactorsMap.window_density || 150,
        excavationExtraFactor: calculationFactorsMap.excavation_extra_factor || 1.2,
        internalWallFactor: calculationFactorsMap.internal_wall_factor || 0.8,
        openingDistribution: {
          external: calculationFactorsMap.opening_external_distribution || 0.7,
          internal: calculationFactorsMap.opening_internal_distribution || 0.3
        }
      },
      // NEW: Admin configurable opening sizes (IS 1200 compliant)
      openingSizes: {
        doorHeight: openingSizesMap.door?.height || 2.1,
        doorWidth: openingSizesMap.door?.width || 1.0,
        windowHeight: openingSizesMap.window?.height || 1.5,
        windowWidth: openingSizesMap.window?.width || 1.2
      }
    };

    return standards;
  } catch (error) {
    console.error('Error fetching calculation standards:', error);
    throw error;
  }
}