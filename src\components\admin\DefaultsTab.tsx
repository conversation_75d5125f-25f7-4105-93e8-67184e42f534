import React, { useState, useEffect } from 'react';
import { Save, RefreshCw, AlertTriangle } from 'lucide-react';
import { Component, UIDefaults, componentAPI, uiDefaultsAPI } from '../../lib/supabase';

interface DefaultsTabProps {
  defaults: UIDefaults | null;
  isLoading: boolean;
  components: Component[];
}

export function DefaultsTab({ 
  defaults, 
  isLoading, 
  components 
}: DefaultsTabProps) {
  const [configData, setConfigData] = useState<Record<string, any>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');

  useEffect(() => {
    if (defaults) {
      setConfigData(defaults.config_data || {});
    } else {
      // Initialize with empty structure if no defaults exist
      setConfigData({
        qualityTier: 'better',
        rooms: {
          'Bedroom': { flooring: '', wallFinish: '' },
          'Living Room': { flooring: '', wallFinish: '' },
          'Kitchen': { flooring: '', wallFinish: '' },
          'Bathroom': { flooring: '', wallFinish: '', fittingsBundle: '' },
          'Dining Room': { flooring: '', wallFinish: '' }
        },
        shell: {
          windows: '',
          externalWalls: ''
        },
        facade: {
          primaryFinish: ''
        }
      });
    }
  }, [defaults]);

  const handleSaveDefaults = async () => {
    setIsSaving(true);
    setSaveStatus('idle');
    
    try {
      await uiDefaultsAPI.updateDefaults(configData);
      setSaveStatus('success');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (error) {
      console.error('Error updating defaults:', error);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const getComponentsByCategory = (category: string) => {
    return components.filter(c => c.category === category);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-purple-600 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">UI Default Configurations</h3>
        <button
          onClick={handleSaveDefaults}
          disabled={isSaving}
          className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors disabled:bg-purple-400"
        >
          {isSaving ? (
            <>
              <RefreshCw className="w-4 h-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4" />
              Save Defaults
            </>
          )}
        </button>
      </div>

      {/* Save Status */}
      {saveStatus === 'success' && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-3 flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-green-700">Default settings saved successfully!</span>
        </div>
      )}
      
      {saveStatus === 'error' && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-center gap-2">
          <AlertTriangle className="w-4 h-4 text-red-500" />
          <span className="text-red-700">Error saving defaults. Please try again.</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Room Defaults */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-4">Room Defaults</h4>
          <div className="space-y-4">
            {Object.entries(configData.rooms || {}).map(([roomType, roomConfig]: [string, any]) => (
              <div key={roomType} className="bg-white rounded-lg p-3">
                <h5 className="font-medium text-gray-700 mb-2">{roomType}</h5>
                <div className="space-y-2">
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">Default Flooring</label>
                    <select
                      value={roomConfig.flooring || ''}
                      onChange={(e) => {
                        setConfigData(prev => ({
                          ...prev,
                          rooms: {
                            ...prev.rooms,
                            [roomType]: {
                              ...roomConfig,
                              flooring: e.target.value
                            }
                          }
                        }));
                      }}
                      className="w-full px-2 py-1 border border-gray-200 rounded text-sm"
                    >
                      <option value="">Select flooring...</option>
                      {getComponentsByCategory('Flooring').map(component => (
                        <option key={component.id} value={component.id}>
                          {component.name} - ₹{component.unit_price}/{component.unit}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-xs text-gray-600 mb-1">Default Wall Finish</label>
                    <select
                      value={roomConfig.wallFinish || ''}
                      onChange={(e) => {
                        setConfigData(prev => ({
                          ...prev,
                          rooms: {
                            ...prev.rooms,
                            [roomType]: {
                              ...roomConfig,
                              wallFinish: e.target.value
                            }
                          }
                        }));
                      }}
                      className="w-full px-2 py-1 border border-gray-200 rounded text-sm"
                    >
                      <option value="">Select wall finish...</option>
                      {getComponentsByCategory('Wall Finishes').map(component => (
                        <option key={component.id} value={component.id}>
                          {component.name} - ₹{component.unit_price}/{component.unit}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  {roomType === 'Bathroom' && (
                    <div>
                      <label className="block text-xs text-gray-600 mb-1">Default Fittings Bundle</label>
                      <select
                        value={roomConfig.fittingsBundle || ''}
                        onChange={(e) => {
                          setConfigData(prev => ({
                            ...prev,
                            rooms: {
                              ...prev.rooms,
                              [roomType]: {
                                ...roomConfig,
                                fittingsBundle: e.target.value
                              }
                            }
                          }));
                        }}
                        className="w-full px-2 py-1 border border-gray-200 rounded text-sm"
                      >
                        <option value="">Select bundle...</option>
                        {getComponentsByCategory('Bathroom Fittings').map(component => (
                          <option key={component.id} value={component.id}>
                            {component.name} - ₹{component.unit_price}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Shell Defaults */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-4">Shell & Structure Defaults</h4>
          <div className="space-y-4">
            <div className="bg-white rounded-lg p-3">
              <h5 className="font-medium text-gray-700 mb-2">Windows</h5>
              <select
                value={configData.shell?.windows || ''}
                onChange={(e) => {
                  setConfigData(prev => ({
                    ...prev,
                    shell: {
                      ...prev.shell,
                      windows: e.target.value
                    }
                  }));
                }}
                className="w-full px-2 py-1 border border-gray-200 rounded text-sm"
              >
                <option value="">Select default windows...</option>
                {getComponentsByCategory('Windows').map(component => (
                  <option key={component.id} value={component.id}>
                    {component.name} - ₹{component.unit_price}/{component.unit}
                  </option>
                ))}
              </select>
            </div>

            <div className="bg-white rounded-lg p-3">
              <h5 className="font-medium text-gray-700 mb-2">External Walls</h5>
              <select
                value={configData.shell?.externalWalls || ''}
                onChange={(e) => {
                  setConfigData(prev => ({
                    ...prev,
                    shell: {
                      ...prev.shell,
                      externalWalls: e.target.value
                    }
                  }));
                }}
                className="w-full px-2 py-1 border border-gray-200 rounded text-sm"
              >
                <option value="">Select wall material...</option>
                {getComponentsByCategory('Masonry').map(component => (
                  <option key={component.id} value={component.id}>
                    {component.name} - ₹{component.unit_price}/{component.unit}
                  </option>
                ))}
              </select>
            </div>

            <div className="bg-white rounded-lg p-3">
              <h5 className="font-medium text-gray-700 mb-2">Facade Finish</h5>
              <select
                value={configData.facade?.primaryFinish || ''}
                onChange={(e) => {
                  setConfigData(prev => ({
                    ...prev,
                    facade: {
                      ...prev.facade,
                      primaryFinish: e.target.value
                    }
                  }));
                }}
                className="w-full px-2 py-1 border border-gray-200 rounded text-sm"
              >
                <option value="">Select facade finish...</option>
                {getComponentsByCategory('Facade').map(component => (
                  <option key={component.id} value={component.id}>
                    {component.name} - ₹{component.unit_price}/{component.unit}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="bg-white rounded-lg p-3">
              <h5 className="font-medium text-gray-700 mb-2">Quality Tier</h5>
              <select
                value={configData.qualityTier || 'better'}
                onChange={(e) => {
                  setConfigData(prev => ({
                    ...prev,
                    qualityTier: e.target.value
                  }));
                }}
                className="w-full px-2 py-1 border border-gray-200 rounded text-sm"
              >
                <option value="good">Good (Budget)</option>
                <option value="better">Better (Premium)</option>
                <option value="best">Best (Luxury)</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}