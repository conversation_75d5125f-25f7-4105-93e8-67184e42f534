/*
  # Create Engineering Standards and Configuration Tables

  1. New Tables
    - `engineering_standards` - Basic structural parameters and assumptions
    - `concrete_mixes` - Concrete mix designs for different grades
    - `steel_reinforcement` - Steel reinforcement ratios for structural elements
    - `material_consumption` - Material consumption ratios and parameters
    - `wastage_factors` - Material wastage percentages
    - `location_multipliers` - Regional cost multipliers for different locations
    - `professional_fees` - Professional service fee structures
    - `regulatory_fees` - Government and regulatory fees
    - `system_config` - Global system configuration parameters

  2. Security
    - Enable RLS on all tables
    - Add policies for admin management and public viewing
*/

-- Engineering Standards table
CREATE TABLE IF NOT EXISTS engineering_standards (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  category text NOT NULL,
  name text NOT NULL,
  value text NOT NULL,
  unit text NOT NULL,
  description text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE engineering_standards ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Engineering standards are manageable by admins"
  ON engineering_standards
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM admin_users
    WHERE admin_users.email = (auth.jwt() ->> 'email'::text)
    AND admin_users.is_active = true
  ));

CREATE POLICY "Engineering standards are viewable by everyone"
  ON engineering_standards
  FOR SELECT
  TO public
  USING (is_active = true);

-- Concrete Mixes table
CREATE TABLE IF NOT EXISTS concrete_mixes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  grade text NOT NULL,
  cement_bags_per_m3 numeric NOT NULL,
  sand_cft_per_m3 numeric NOT NULL,
  aggregate_cft_per_m3 numeric NOT NULL,
  water_cement_ratio numeric NOT NULL,
  applications text[],
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE concrete_mixes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Concrete mixes are manageable by admins"
  ON concrete_mixes
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM admin_users
    WHERE admin_users.email = (auth.jwt() ->> 'email'::text)
    AND admin_users.is_active = true
  ));

CREATE POLICY "Concrete mixes are viewable by everyone"
  ON concrete_mixes
  FOR SELECT
  TO public
  USING (is_active = true);

-- Steel Reinforcement table
CREATE TABLE IF NOT EXISTS steel_reinforcement (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  structural_element text NOT NULL,
  steel_ratio_kg_per_m3 numeric NOT NULL,
  notes text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE steel_reinforcement ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Steel reinforcement are manageable by admins"
  ON steel_reinforcement
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM admin_users
    WHERE admin_users.email = (auth.jwt() ->> 'email'::text)
    AND admin_users.is_active = true
  ));

CREATE POLICY "Steel reinforcement are viewable by everyone"
  ON steel_reinforcement
  FOR SELECT
  TO public
  USING (is_active = true);

-- Material Consumption table
CREATE TABLE IF NOT EXISTS material_consumption (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  material text NOT NULL,
  consumption_value numeric NOT NULL,
  unit text NOT NULL,
  description text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE material_consumption ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Material consumption are manageable by admins"
  ON material_consumption
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM admin_users
    WHERE admin_users.email = (auth.jwt() ->> 'email'::text)
    AND admin_users.is_active = true
  ));

CREATE POLICY "Material consumption are viewable by everyone"
  ON material_consumption
  FOR SELECT
  TO public
  USING (is_active = true);

-- Wastage Factors table
CREATE TABLE IF NOT EXISTS wastage_factors (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  material text NOT NULL,
  wastage_percentage numeric NOT NULL,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE wastage_factors ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Wastage factors are manageable by admins"
  ON wastage_factors
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM admin_users
    WHERE admin_users.email = (auth.jwt() ->> 'email'::text)
    AND admin_users.is_active = true
  ));

CREATE POLICY "Wastage factors are viewable by everyone"
  ON wastage_factors
  FOR SELECT
  TO public
  USING (is_active = true);

-- Location Multipliers table
CREATE TABLE IF NOT EXISTS location_multipliers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  location text NOT NULL,
  material_multiplier numeric NOT NULL,
  labor_multiplier numeric NOT NULL,
  notes text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE location_multipliers ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Location multipliers are manageable by admins"
  ON location_multipliers
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM admin_users
    WHERE admin_users.email = (auth.jwt() ->> 'email'::text)
    AND admin_users.is_active = true
  ));

CREATE POLICY "Location multipliers are viewable by everyone"
  ON location_multipliers
  FOR SELECT
  TO public
  USING (is_active = true);

-- Professional Fees table
CREATE TABLE IF NOT EXISTS professional_fees (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  service text NOT NULL,
  tier1_percentage numeric NOT NULL,
  tier2_percentage numeric NOT NULL,
  tier3_percentage numeric NOT NULL,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE professional_fees ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Professional fees are manageable by admins"
  ON professional_fees
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM admin_users
    WHERE admin_users.email = (auth.jwt() ->> 'email'::text)
    AND admin_users.is_active = true
  ));

CREATE POLICY "Professional fees are viewable by everyone"
  ON professional_fees
  FOR SELECT
  TO public
  USING (is_active = true);

-- Regulatory Fees table
CREATE TABLE IF NOT EXISTS regulatory_fees (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  fee_name text NOT NULL,
  authority text NOT NULL,
  rate numeric NOT NULL,
  unit text NOT NULL,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE regulatory_fees ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Regulatory fees are manageable by admins"
  ON regulatory_fees
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM admin_users
    WHERE admin_users.email = (auth.jwt() ->> 'email'::text)
    AND admin_users.is_active = true
  ));

CREATE POLICY "Regulatory fees are viewable by everyone"
  ON regulatory_fees
  FOR SELECT
  TO public
  USING (is_active = true);

-- System Config table
CREATE TABLE IF NOT EXISTS system_config (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  gst_materials numeric NOT NULL DEFAULT 18.0,
  gst_services numeric NOT NULL DEFAULT 18.0,
  default_contingency numeric NOT NULL DEFAULT 10.0,
  default_profit_margin numeric NOT NULL DEFAULT 15.0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE system_config ENABLE ROW LEVEL SECURITY;

CREATE POLICY "System config is manageable by admins"
  ON system_config
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM admin_users
    WHERE admin_users.email = (auth.jwt() ->> 'email'::text)
    AND admin_users.is_active = true
  ));

CREATE POLICY "System config is viewable by everyone"
  ON system_config
  FOR SELECT
  TO public
  USING (true);

-- Insert default data

-- Engineering Standards
INSERT INTO engineering_standards (category, name, value, unit, description) VALUES
('structural', 'Foundation Depth', '2.5', 'm', 'For expansive soil in Delhi/NCR (IS 1904)'),
('structural', 'Grid Spacing', '3.5', 'm', 'More economical for residential projects'),
('structural', 'Slab Thickness', '150', 'mm', 'For spans up to 4m (IS 456:2000)');

-- Concrete Mixes
INSERT INTO concrete_mixes (grade, cement_bags_per_m3, sand_cft_per_m3, aggregate_cft_per_m3, water_cement_ratio) VALUES
('M20', 8.5, 15.0, 29.0, 0.50),
('M25', 10.5, 12.5, 25.0, 0.45),
('M30', 12.5, 11.0, 22.0, 0.45);

-- Steel Reinforcement
INSERT INTO steel_reinforcement (structural_element, steel_ratio_kg_per_m3) VALUES
('Isolated Footings', 120),
('Strip Footings', 140),
('Raft Foundation', 160),
('Ground Floor Columns', 200),
('Typical Floor Columns', 180),
('Basement Columns', 220),
('Main Beams', 160),
('Secondary Beams', 140),
('One-Way Slab', 110),
('Two-Way Slab', 120),
('Cantilever Slab', 160);

-- Material Consumption
INSERT INTO material_consumption (material, consumption_value, unit, description) VALUES
('Red Clay Bricks (230mm wall)', 98, 'pieces', 'Corrected standard brick consumption'),
('Red Clay Bricks (115mm wall)', 49, 'pieces', 'Corrected standard brick consumption'),
('AAC Blocks (200mm wall)', 8.33, 'pieces', 'Standard AAC block consumption'),
('AAC Blocks (150mm wall)', 11.11, 'pieces', 'Standard AAC block consumption');

-- Wastage Factors
INSERT INTO wastage_factors (material, wastage_percentage) VALUES
('cement', 3),
('sand', 8),
('aggregate', 5),
('steel', 5),
('bricks', 7),
('paint', 5);

-- Location Multipliers
INSERT INTO location_multipliers (location, material_multiplier, labor_multiplier, notes) VALUES
('Delhi', 1.0, 1.0, 'Baseline'),
('Gurgaon', 1.15, 1.30, 'Higher transportation and premium labor market'),
('Noida', 1.05, 1.10, 'Competitive but slightly higher than Delhi'),
('Ghaziabad', 0.95, 0.90, 'Lower material and labor costs');

-- Professional Fees
INSERT INTO professional_fees (service, tier1_percentage, tier2_percentage, tier3_percentage) VALUES
('Architect (Design + Supervision)', 4.0, 6.0, 8.0),
('Structural Engineer', 1.5, 2.0, 2.5),
('MEP Consultant', 1.0, 1.5, 2.0);

-- Regulatory Fees
INSERT INTO regulatory_fees (fee_name, authority, rate, unit) VALUES
('Plan Approval', 'MCD', 550, 'per sqm'),
('Fire NOC', 'Fire Dept.', 20000, 'Lump Sum'),
('Environment Clearance', 'MoEF', 15000, 'Lump Sum');

-- System Config (single record)
INSERT INTO system_config (gst_materials, gst_services, default_contingency, default_profit_margin) VALUES
(18.0, 18.0, 10.0, 15.0);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_engineering_standards_category ON engineering_standards(category);
CREATE INDEX IF NOT EXISTS idx_engineering_standards_active ON engineering_standards(is_active);
CREATE INDEX IF NOT EXISTS idx_concrete_mixes_grade ON concrete_mixes(grade);
CREATE INDEX IF NOT EXISTS idx_steel_reinforcement_element ON steel_reinforcement(structural_element);
CREATE INDEX IF NOT EXISTS idx_material_consumption_material ON material_consumption(material);
CREATE INDEX IF NOT EXISTS idx_wastage_factors_material ON wastage_factors(material);
CREATE INDEX IF NOT EXISTS idx_location_multipliers_location ON location_multipliers(location);
CREATE INDEX IF NOT EXISTS idx_professional_fees_service ON professional_fees(service);
CREATE INDEX IF NOT EXISTS idx_regulatory_fees_name ON regulatory_fees(fee_name);