import React, { useState, useEffect } from 'react';
import { Component } from '../../lib/supabase';
import { validatePrice, ValidationResult, getPriceRange, getQualityTierMultiplier, getRegionalMultiplier } from '../../utils/priceValidation';

interface ComponentPriceManagerProps {
  components: Component[];
  onUpdateComponent: (componentId: string, updates: Partial<Component>) => Promise<void>;
}

interface PriceValidationAlert {
  componentId: string;
  validation: ValidationResult;
}

export function ComponentPriceManager({ components, onUpdateComponent }: ComponentPriceManagerProps) {
  const [editingComponent, setEditingComponent] = useState<string | null>(null);
  const [priceAlerts, setPriceAlerts] = useState<PriceValidationAlert[]>([]);
  const [filterSeverity, setFilterSeverity] = useState<'all' | 'critical' | 'error' | 'warning'>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Validate all component prices on load
  useEffect(() => {
    const alerts: PriceValidationAlert[] = [];
    
    components.forEach(component => {
      const validation = validatePrice(
        component.unit_price,
        component.category,
        component.sub_category || '',
        component.name
      );
      
      if (!validation.isValid || validation.severity === 'warning' || validation.severity === 'critical') {
        alerts.push({
          componentId: component.id,
          validation
        });
      }
    });
    
    setPriceAlerts(alerts);
  }, [components]);

  const handlePriceUpdate = async (componentId: string, newPrice: number) => {
    const component = components.find(c => c.id === componentId);
    if (!component) return;

    const validation = validatePrice(newPrice, component.category, component.sub_category || '', component.name);
    
    // Show confirmation for critical/error prices
    if (validation.severity === 'critical' || validation.severity === 'error') {
      const confirmed = window.confirm(
        `${validation.message}\n\nDo you want to proceed with this price?`
      );
      if (!confirmed) return;
    }

    try {
      await onUpdateComponent(componentId, { unit_price: newPrice });
      setEditingComponent(null);
    } catch (error) {
      console.error('Error updating component price:', error);
      alert('Failed to update component price');
    }
  };

  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return '🚨';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      default: return 'ℹ️';
    }
  };

  const getAlertColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 border-red-500 text-red-800';
      case 'error': return 'bg-red-50 border-red-400 text-red-700';
      case 'warning': return 'bg-yellow-50 border-yellow-400 text-yellow-700';
      default: return 'bg-blue-50 border-blue-400 text-blue-700';
    }
  };

  const filteredAlerts = priceAlerts.filter(alert => {
    if (filterSeverity !== 'all' && alert.validation.severity !== filterSeverity) return false;
    
    const component = components.find(c => c.id === alert.componentId);
    if (!component) return false;
    
    if (selectedCategory !== 'all' && component.category !== selectedCategory) return false;
    
    if (searchTerm && !component.name.toLowerCase().includes(searchTerm.toLowerCase())) return false;
    
    return true;
  });

  const categories = [...new Set(components.map(c => c.category))];
  const criticalCount = priceAlerts.filter(a => a.validation.severity === 'critical').length;
  const errorCount = priceAlerts.filter(a => a.validation.severity === 'error').length;
  const warningCount = priceAlerts.filter(a => a.validation.severity === 'warning').length;

  return (
    <div className="space-y-6">
      {/* Summary Dashboard */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Price Validation Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <span className="text-2xl mr-2">🚨</span>
              <div>
                <p className="text-sm text-red-600">Critical Issues</p>
                <p className="text-2xl font-bold text-red-800">{criticalCount}</p>
              </div>
            </div>
          </div>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center">
              <span className="text-2xl mr-2">❌</span>
              <div>
                <p className="text-sm text-orange-600">Errors</p>
                <p className="text-2xl font-bold text-orange-800">{errorCount}</p>
              </div>
            </div>
          </div>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <span className="text-2xl mr-2">⚠️</span>
              <div>
                <p className="text-sm text-yellow-600">Warnings</p>
                <p className="text-2xl font-bold text-yellow-800">{warningCount}</p>
              </div>
            </div>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <span className="text-2xl mr-2">✅</span>
              <div>
                <p className="text-sm text-green-600">Valid Prices</p>
                <p className="text-2xl font-bold text-green-800">{components.length - priceAlerts.length}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Filter by Severity</label>
            <select
              value={filterSeverity}
              onChange={(e) => setFilterSeverity(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="all">All Issues</option>
              <option value="critical">Critical Only</option>
              <option value="error">Errors Only</option>
              <option value="warning">Warnings Only</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Filter by Category</label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Search Components</label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by component name..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
        </div>
      </div>

      {/* Price Alerts */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Price Validation Alerts</h3>
        
        {filteredAlerts.length === 0 ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
            <span className="text-2xl">✅</span>
            <p className="text-green-800 font-medium">No price issues found with current filters!</p>
          </div>
        ) : (
          filteredAlerts.map(alert => {
            const component = components.find(c => c.id === alert.componentId);
            if (!component) return null;

            const priceRange = getPriceRange(component.category, component.sub_category || '');

            return (
              <div
                key={alert.componentId}
                className={`border rounded-lg p-4 ${getAlertColor(alert.validation.severity)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <span className="text-xl mr-2">{getAlertIcon(alert.validation.severity)}</span>
                      <h4 className="font-semibold">{component.name}</h4>
                      <span className="ml-2 px-2 py-1 bg-gray-200 text-gray-700 text-xs rounded">
                        {component.category} - {component.sub_category}
                      </span>
                    </div>
                    
                    <p className="text-sm mb-3">{alert.validation.message}</p>
                    
                    {priceRange && (
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="font-medium">Current Price:</span>
                          <p className="text-lg font-bold">₹{component.unit_price}/{component.unit}</p>
                        </div>
                        <div>
                          <span className="font-medium">Market Range:</span>
                          <p>₹{priceRange.min} - ₹{priceRange.max}</p>
                        </div>
                        <div>
                          <span className="font-medium">Market Benchmark:</span>
                          <p>₹{priceRange.marketBenchmark}</p>
                        </div>
                        <div>
                          <span className="font-medium">Deviation:</span>
                          <p className={alert.validation.deviationPercentage < 0 ? 'text-red-600' : 'text-orange-600'}>
                            {alert.validation.deviationPercentage > 0 ? '+' : ''}{alert.validation.deviationPercentage.toFixed(1)}%
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="ml-4">
                    {editingComponent === component.id ? (
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          defaultValue={component.unit_price}
                          className="w-24 px-2 py-1 border border-gray-300 rounded text-sm"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              const newPrice = parseFloat((e.target as HTMLInputElement).value);
                              handlePriceUpdate(component.id, newPrice);
                            }
                          }}
                          autoFocus
                        />
                        <button
                          onClick={() => setEditingComponent(null)}
                          className="px-2 py-1 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400"
                        >
                          Cancel
                        </button>
                      </div>
                    ) : (
                      <button
                        onClick={() => setEditingComponent(component.id)}
                        className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                      >
                        Edit Price
                      </button>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
}
