// Test script to verify database connection and new tables
// Run this in browser console to test the new configurable parameters

async function testDatabaseConnection() {
  console.log('🔍 Testing Database Connection...');
  
  try {
    // Test material rates
    console.log('\n📊 Testing Material Rates...');
    const materialRatesResponse = await fetch('/api/supabase/material_rates');
    if (materialRatesResponse.ok) {
      const materialRates = await materialRatesResponse.json();
      console.log('✅ Material Rates:', materialRates.length, 'records found');
      console.log('Sample:', materialRates.slice(0, 3));
    }
    
    // Test calculation factors
    console.log('\n🧮 Testing Calculation Factors...');
    const factorsResponse = await fetch('/api/supabase/calculation_factors');
    if (factorsResponse.ok) {
      const factors = await factorsResponse.json();
      console.log('✅ Calculation Factors:', factors.length, 'records found');
      console.log('Sample:', factors.slice(0, 3));
    }
    
    // Test opening sizes
    console.log('\n📐 Testing Opening Sizes...');
    const openingsResponse = await fetch('/api/supabase/opening_sizes');
    if (openingsResponse.ok) {
      const openings = await openingsResponse.json();
      console.log('✅ Opening Sizes:', openings.length, 'records found');
      console.log('Sample:', openings.slice(0, 3));
    }
    
    // Test engineering standards
    console.log('\n🏗️ Testing Engineering Standards...');
    const standardsResponse = await fetch('/api/supabase/engineering_standards');
    if (standardsResponse.ok) {
      const standards = await standardsResponse.json();
      console.log('✅ Engineering Standards:', standards.length, 'records found');
      const structuralParams = standards.filter(s => s.category === 'structural');
      console.log('Structural Parameters:', structuralParams.length);
    }
    
    console.log('\n🎉 Database Connection Test Complete!');
    
  } catch (error) {
    console.error('❌ Database Connection Error:', error);
  }
}

// Alternative direct Supabase test
async function testSupabaseDirect() {
  console.log('🔍 Testing Direct Supabase Connection...');
  
  try {
    // Import Supabase client (this would work in the actual app)
    // const { supabase } = await import('./src/lib/supabase.ts');
    
    console.log('📊 Testing Material Rates Query...');
    // const { data: materialRates, error: mrError } = await supabase
    //   .from('material_rates')
    //   .select('*')
    //   .limit(5);
    
    // if (mrError) throw mrError;
    // console.log('✅ Material Rates:', materialRates);
    
    console.log('🧮 Testing Calculation Factors Query...');
    // const { data: factors, error: cfError } = await supabase
    //   .from('calculation_factors')
    //   .select('*')
    //   .limit(5);
    
    // if (cfError) throw cfError;
    // console.log('✅ Calculation Factors:', factors);
    
    console.log('📐 Testing Opening Sizes Query...');
    // const { data: openings, error: osError } = await supabase
    //   .from('opening_sizes')
    //   .select('*')
    //   .limit(5);
    
    // if (osError) throw osError;
    // console.log('✅ Opening Sizes:', openings);
    
    console.log('🎉 Direct Supabase Test Complete!');
    
  } catch (error) {
    console.error('❌ Direct Supabase Error:', error);
  }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testDatabaseConnection, testSupabaseDirect };
}

// Auto-run if in browser
if (typeof window !== 'undefined') {
  console.log('🚀 Database Test Script Loaded');
  console.log('Run testDatabaseConnection() to test the new configurable parameters');
}
