import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, GitCompare, TrendingUp, TrendingDown, Minus, Plus, AlertTriangle } from 'lucide-react';
import { CalculationResult, UserInputs } from '../types/calculator';

interface SavedEstimate {
  id: string;
  name: string;
  timestamp: Date;
  result: CalculationResult;
  inputs: UserInputs;
}

interface ConfigurationChange {
  type: 'added' | 'removed' | 'changed';
  category: string;
  description: string;
  impact: 'positive' | 'negative' | 'neutral';
  costImpact: number;
}

interface EstimateComparisonProps {
  currentResult: CalculationResult;
  currentInputs: UserInputs;
  isOpen: boolean;
  onClose: () => void;
}

export function EstimateComparison({ currentResult, currentInputs, isOpen, onClose }: EstimateComparisonProps) {
  const [savedEstimates, setSavedEstimates] = useState<SavedEstimate[]>([]);
  const [selectedEstimates, setSelectedEstimates] = useState<string[]>([]);
  const [estimateName, setEstimateName] = useState('');

  if (!isOpen) return null;

  const saveCurrentEstimate = () => {
    if (!estimateName.trim()) return;
    
    const newEstimate: SavedEstimate = {
      id: Date.now().toString(),
      name: estimateName.trim(),
      timestamp: new Date(),
      result: currentResult,
      inputs: currentInputs
    };
    
    setSavedEstimates(prev => [...prev, newEstimate]);
    setEstimateName('');
  };

  const toggleEstimateSelection = (id: string) => {
    setSelectedEstimates(prev => {
      if (prev.includes(id)) {
        return prev.filter(estimateId => estimateId !== id);
      } else if (prev.length < 2) {
        return [...prev, id];
      }
      return prev;
    });
  };

  const getSelectedEstimatesData = () => {
    return selectedEstimates.map(id => 
      savedEstimates.find(est => est.id === id)
    ).filter(Boolean) as SavedEstimate[];
  };

  const detectConfigurationChanges = (estimateA: SavedEstimate, estimateB: SavedEstimate): ConfigurationChange[] => {
    const changes: ConfigurationChange[] = [];
    const inputsA = estimateA.inputs;
    const inputsB = estimateB.inputs;
    const resultA = estimateA.result;
    const resultB = estimateB.result;

    // Structural changes
    if (inputsA.hasBasement !== inputsB.hasBasement) {
      changes.push({
        type: inputsB.hasBasement ? 'added' : 'removed',
        category: 'Structure',
        description: inputsB.hasBasement ? 'Basement Added' : 'Basement Removed',
        impact: inputsB.hasBasement ? 'negative' : 'positive',
        costImpact: Math.abs(resultB.totalCost - resultA.totalCost) * 0.15 // Estimate basement impact
      });
    }

    if (inputsA.hasStiltParking !== inputsB.hasStiltParking) {
      changes.push({
        type: inputsB.hasStiltParking ? 'added' : 'removed',
        category: 'Structure',
        description: inputsB.hasStiltParking ? 'Stilt Parking Added' : 'Stilt Parking Removed',
        impact: inputsB.hasStiltParking ? 'negative' : 'positive',
        costImpact: Math.abs(resultB.totalCost - resultA.totalCost) * 0.08 // Estimate stilt impact
      });
    }

    if (inputsA.numberOfFloors !== inputsB.numberOfFloors) {
      const floorDiff = inputsB.numberOfFloors - inputsA.numberOfFloors;
      changes.push({
        type: floorDiff > 0 ? 'added' : 'removed',
        category: 'Structure',
        description: `${Math.abs(floorDiff)} Floor${Math.abs(floorDiff) > 1 ? 's' : ''} ${floorDiff > 0 ? 'Added' : 'Removed'}`,
        impact: floorDiff > 0 ? 'negative' : 'positive',
        costImpact: Math.abs(floorDiff) * (resultA.totalCost / inputsA.numberOfFloors)
      });
    }

    if (inputsA.plotSize !== inputsB.plotSize) {
      const sizeDiff = inputsB.plotSize - inputsA.plotSize;
      changes.push({
        type: sizeDiff > 0 ? 'added' : 'removed',
        category: 'Plot',
        description: `Plot Size ${sizeDiff > 0 ? 'Increased' : 'Decreased'} by ${Math.abs(sizeDiff)} sq ft`,
        impact: sizeDiff > 0 ? 'negative' : 'positive',
        costImpact: Math.abs(sizeDiff) * resultA.ratePerSqft
      });
    }

    if (inputsA.location !== inputsB.location) {
      changes.push({
        type: 'changed',
        category: 'Location',
        description: `Location Changed from ${inputsA.location} to ${inputsB.location}`,
        impact: 'neutral',
        costImpact: Math.abs(resultB.totalCost - resultA.totalCost) * 0.05 // Estimate location impact
      });
    }

    // Quality tier changes
    if (resultA.qualityTier !== resultB.qualityTier) {
      const qualityOrder = { good: 1, better: 2, best: 3 };
      const isUpgrade = qualityOrder[resultB.qualityTier] > qualityOrder[resultA.qualityTier];
      
      changes.push({
        type: 'changed',
        category: 'Quality',
        description: `Quality Tier ${isUpgrade ? 'Upgraded' : 'Downgraded'} from ${resultA.qualityTier} to ${resultB.qualityTier}`,
        impact: isUpgrade ? 'negative' : 'positive',
        costImpact: Math.abs(resultB.totalCost - resultA.totalCost) * 0.2 // Estimate quality impact
      });
    }

    // Material changes (simplified - checking major sections)
    const sectionsA = resultA.sections.reduce((acc, section) => {
      acc[section.id] = section.subtotal;
      return acc;
    }, {} as Record<string, number>);

    const sectionsB = resultB.sections.reduce((acc, section) => {
      acc[section.id] = section.subtotal;
      return acc;
    }, {} as Record<string, number>);

    Object.keys(sectionsA).forEach(sectionId => {
      const costA = sectionsA[sectionId];
      const costB = sectionsB[sectionId] || 0;
      const diff = Math.abs(costB - costA);
      
      if (diff > costA * 0.1) { // More than 10% change
        const sectionName = resultA.sections.find(s => s.id === sectionId)?.title || sectionId;
        changes.push({
          type: 'changed',
          category: 'Materials',
          description: `${sectionName} Materials Modified`,
          impact: costB > costA ? 'negative' : 'positive',
          costImpact: diff
        });
      }
    });

    // Room customization changes
    if (resultA.roomMaterialSelections.length !== resultB.roomMaterialSelections.length) {
      const hasCustomA = resultA.roomMaterialSelections.length > 0;
      const hasCustomB = resultB.roomMaterialSelections.length > 0;
      
      if (hasCustomA !== hasCustomB) {
        changes.push({
          type: hasCustomB ? 'added' : 'removed',
          category: 'Customization',
          description: hasCustomB ? 'Room-wise Customization Added' : 'Room-wise Customization Removed',
          impact: 'neutral',
          costImpact: 0
        });
      }
    }

    return changes;
  };

  const calculateDifference = (value1: number, value2: number) => {
    const diff = value1 - value2;
    const percentage = ((diff / value2) * 100);
    return { diff, percentage };
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getDifferenceIcon = (diff: number) => {
    if (diff > 0) return <TrendingUp className="w-4 h-4 text-red-500" />;
    if (diff < 0) return <TrendingDown className="w-4 h-4 text-green-500" />;
    return <Minus className="w-4 h-4 text-gray-500" />;
  };

  const getChangeIcon = (type: string) => {
    switch (type) {
      case 'added':
        return <Plus className="w-4 h-4 text-green-600" />;
      case 'removed':
        return <Minus className="w-4 h-4 text-red-600" />;
      case 'changed':
        return <GitCompare className="w-4 h-4 text-blue-600" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-600" />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'positive':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'negative':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  const selectedData = getSelectedEstimatesData();
  const configurationChanges = selectedData.length === 2 ? detectConfigurationChanges(selectedData[0], selectedData[1]) : [];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-7xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Estimate Comparison</h2>
            <p className="text-gray-600 mt-1">Save and compare different project configurations</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Save Current Estimate */}
          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-blue-800 mb-3">Save Current Estimate</h3>
            <div className="flex gap-3">
              <input
                type="text"
                value={estimateName}
                onChange={(e) => setEstimateName(e.target.value)}
                placeholder="Enter estimate name (e.g., 'With Basement', 'Premium Finishes')"
                className="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={saveCurrentEstimate}
                disabled={!estimateName.trim()}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
              >
                Save Estimate
              </button>
            </div>
          </div>

          {/* Saved Estimates List */}
          <div className="mb-6">
            <h3 className="font-semibold text-gray-800 mb-3">Saved Estimates</h3>
            {savedEstimates.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No saved estimates yet. Save your current configuration to start comparing.
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {savedEstimates.map((estimate) => (
                  <div
                    key={estimate.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      selectedEstimates.includes(estimate.id)
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => toggleEstimateSelection(estimate.id)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-gray-800">{estimate.name}</h4>
                      <input
                        type="checkbox"
                        checked={selectedEstimates.includes(estimate.id)}
                        onChange={() => toggleEstimateSelection(estimate.id)}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                    </div>
                    <div className="text-sm text-gray-600 mb-2">
                      {estimate.timestamp.toLocaleDateString('en-IN')}
                    </div>
                    <div className="text-lg font-bold text-blue-600">
                      {formatCurrency(estimate.result.totalCost)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {estimate.inputs.plotSize} sq ft • {estimate.inputs.numberOfFloors} floors
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Configuration Changes Detection */}
          {selectedData.length === 2 && configurationChanges.length > 0 && (
            <div className="bg-yellow-50 rounded-lg p-6 mb-6 border border-yellow-200">
              <h3 className="font-semibold text-yellow-800 mb-4 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Configuration Changes Detected
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {configurationChanges.map((change, index) => (
                  <div
                    key={index}
                    className={`border rounded-lg p-3 ${getImpactColor(change.impact)}`}
                  >
                    <div className="flex items-start gap-3">
                      {getChangeIcon(change.type)}
                      <div className="flex-1">
                        <div className="font-medium text-sm">{change.description}</div>
                        <div className="text-xs mt-1 opacity-75">
                          Category: {change.category}
                          {change.costImpact > 0 && (
                            <span className="ml-2">
                              Impact: {formatCurrency(change.costImpact)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Comparison View */}
          {selectedData.length === 2 && (
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <GitCompare className="w-5 h-5" />
                Detailed Comparison
              </h3>

              {/* Summary Comparison */}
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="text-center">
                  <div className="font-medium text-gray-600">Estimate A</div>
                  <div className="text-lg font-bold text-gray-800">{selectedData[0].name}</div>
                  <div className="text-2xl font-bold text-blue-600">
                    {formatCurrency(selectedData[0].result.totalCost)}
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="font-medium text-gray-600">Difference</div>
                  <div className="flex items-center justify-center gap-2">
                    {getDifferenceIcon(selectedData[1].result.totalCost - selectedData[0].result.totalCost)}
                    <span className={`text-lg font-bold ${
                      selectedData[1].result.totalCost > selectedData[0].result.totalCost 
                        ? 'text-red-600' 
                        : selectedData[1].result.totalCost < selectedData[0].result.totalCost 
                        ? 'text-green-600' 
                        : 'text-gray-600'
                    }`}>
                      {formatCurrency(Math.abs(selectedData[1].result.totalCost - selectedData[0].result.totalCost))}
                    </span>
                  </div>
                  <div className="text-sm text-gray-500">
                    {Math.abs(((selectedData[1].result.totalCost - selectedData[0].result.totalCost) / selectedData[0].result.totalCost) * 100).toFixed(1)}%
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="font-medium text-gray-600">Estimate B</div>
                  <div className="text-lg font-bold text-gray-800">{selectedData[1].name}</div>
                  <div className="text-2xl font-bold text-blue-600">
                    {formatCurrency(selectedData[1].result.totalCost)}
                  </div>
                </div>
              </div>

              {/* Section-wise Comparison */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-800">Section-wise Breakdown</h4>
                {selectedData[0].result.sections.map((section, index) => {
                  const sectionB = selectedData[1].result.sections[index];
                  const diff = calculateDifference(sectionB.subtotal, section.subtotal);
                  
                  return (
                    <div key={section.id} className="bg-white rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h5 className="font-medium text-gray-800">{section.title}</h5>
                        <div className="flex items-center gap-2">
                          {getDifferenceIcon(diff.diff)}
                          <span className={`font-medium ${
                            diff.diff > 0 ? 'text-red-600' : diff.diff < 0 ? 'text-green-600' : 'text-gray-600'
                          }`}>
                            {diff.diff !== 0 && (diff.diff > 0 ? '+' : '')}{formatCurrency(diff.diff)}
                          </span>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div className="text-gray-600">
                          {formatCurrency(section.subtotal)}
                        </div>
                        <div className="text-center text-gray-500">
                          {diff.diff !== 0 && `${diff.percentage > 0 ? '+' : ''}${diff.percentage.toFixed(1)}%`}
                        </div>
                        <div className="text-gray-600 text-right">
                          {formatCurrency(sectionB.subtotal)}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {selectedData.length === 1 && (
            <div className="text-center py-8 text-gray-500">
              Select one more estimate to compare
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            Select up to 2 estimates to compare side-by-side
          </div>
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}