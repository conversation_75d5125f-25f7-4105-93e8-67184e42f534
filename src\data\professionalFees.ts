export interface ProfessionalFee {
  id: string;
  name: string;
  category: string;
  feeModel: 'percentage' | 'per_sqft' | 'lump_sum';
  rates: {
    good: number;
    better: number;
    best: number;
  };
  unit: string;
  description: string;
}

// Based on Delhi NCR professional fee research
export const professionalFees: ProfessionalFee[] = [
  {
    id: 'architect_fees',
    name: 'Architect Fees',
    category: 'design',
    feeModel: 'percentage',
    rates: { good: 4, better: 6, best: 10 },
    unit: '% of construction cost',
    description: 'Architectural design, drawings, and supervision'
  },
  {
    id: 'structural_engineer',
    name: 'Structural Engineer Fees',
    category: 'design',
    feeModel: 'per_sqft',
    rates: { good: 5, better: 7, best: 10 },
    unit: 'per sqft built-up',
    description: 'Structural design and drawings'
  },
  {
    id: 'mep_consultant',
    name: 'MEP Consultant Fees',
    category: 'design',
    feeModel: 'percentage',
    rates: { good: 1, better: 1.5, best: 2 },
    unit: '% of MEP cost',
    description: 'Electrical and plumbing design'
  },
  {
    id: 'project_management',
    name: 'Project Management',
    category: 'supervision',
    feeModel: 'percentage',
    rates: { good: 2, better: 3, best: 5 },
    unit: '% of construction cost',
    description: 'Site supervision and project coordination'
  }
];

export interface RegulatoryFee {
  id: string;
  name: string;
  authority: string;
  feeType: 'per_sqm' | 'per_sqft' | 'lump_sum' | 'percentage';
  rate: number;
  unit: string;
  applicableArea: string;
  description: string;
}

// Based on comprehensive regulatory research for Delhi NCR
export const regulatoryFees: RegulatoryFee[] = [
  // MCD Delhi Fees
  {
    id: 'mcd_plan_approval',
    name: 'Building Plan Approval (MCD)',
    authority: 'MCD Delhi',
    feeType: 'per_sqm',
    rate: 387,
    unit: 'per sqm',
    applicableArea: 'Plot ≤ 250 sqm, Category A',
    description: 'Compensatory regulatory charge for building plan approval'
  },
  {
    id: 'mcd_plan_approval_large',
    name: 'Building Plan Approval (MCD Large)',
    authority: 'MCD Delhi',
    feeType: 'per_sqm',
    rate: 774,
    unit: 'per sqm',
    applicableArea: 'Plot > 250 sqm, Category A',
    description: 'Compensatory regulatory charge for larger plots'
  },
  
  // Noida Authority Fees
  {
    id: 'noida_building_permit',
    name: 'Building Permit (Noida)',
    authority: 'Noida Authority',
    feeType: 'per_sqm',
    rate: 15,
    unit: 'per sqm covered area',
    applicableArea: 'All building types',
    description: 'Building permit fee for Noida'
  },
  {
    id: 'noida_completion_fee',
    name: 'Completion Certificate (Noida)',
    authority: 'Noida Authority',
    feeType: 'per_sqm',
    rate: 10,
    unit: 'per sqm covered area',
    applicableArea: 'All building types',
    description: 'Completion certificate fee'
  },
  
  // Utility Connection Fees
  {
    id: 'electricity_connection',
    name: 'Electricity Connection (BSES)',
    authority: 'BSES Delhi',
    feeType: 'lump_sum',
    rate: 4500,
    unit: 'per connection',
    applicableArea: 'Up to 5 kW load',
    description: 'New electricity connection charges'
  },
  {
    id: 'water_connection',
    name: 'Water Connection (DJB)',
    authority: 'Delhi Jal Board',
    feeType: 'lump_sum',
    rate: 3500,
    unit: 'per connection',
    applicableArea: 'Domestic connection',
    description: 'New water and sewer connection'
  }
];