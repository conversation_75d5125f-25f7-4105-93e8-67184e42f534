import React from 'react';
import { Info } from 'lucide-react';

interface ItemBreakdownProps {
  title: string;
  breakdown: string[];
  className?: string;
}

export function ItemBreakdown({ title, breakdown, className = '' }: ItemBreakdownProps) {
  return (
    <div className={`group relative ${className}`}>
      <Info className="w-4 h-4 text-gray-400 hover:text-gray-600 cursor-help transition-colors" />
      
      {/* Tooltip */}
      <div className="absolute bottom-full left-0 mb-2 w-72 bg-gray-800 text-white rounded-lg shadow-lg p-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
        <div className="text-sm font-semibold mb-2">{title}</div>
        <ul className="text-xs space-y-1">
          {breakdown.map((item, index) => (
            <li key={index} className="flex items-start gap-2">
              <span className="text-gray-300">•</span>
              <span>{item}</span>
            </li>
          ))}
        </ul>
        
        {/* Arrow */}
        <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-800"></div>
      </div>
    </div>
  );
}