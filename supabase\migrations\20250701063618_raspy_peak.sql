/*
  # Fix Calculation Engine and Cost Issues

  1. Core Structural Components
    - Add essential structural components with correct pricing
    - Add proper concrete and steel components
    - Fix foundation and structure costs

  2. Task Requirements
    - Create proper task requirements for structural components
    - Set realistic quantities and labor requirements

  3. System Configuration
    - Update system configuration for realistic pricing
*/

-- Add core structural components with realistic pricing
INSERT INTO components (name, category, sub_category, brand, cost_model, unit_price, unit, specifications) VALUES
-- Structural components
('RCC M25 Complete', 'Materials', 'Concrete', 'UltraTech', 'per_unit', 8500, 'm³', 
 '{"grade": "M25", "includes": "Concrete, formwork, labor", "standard": "IS 456:2000"}'),
('TMT Steel Fe 500D Complete', 'Materials', 'Steel', 'TATA Tiscon', 'per_unit', 85, 'kg', 
 '{"grade": "Fe 500D", "includes": "Cutting, bending, fixing", "standard": "IS 1786:2008"}'),
('Brick Masonry Complete', 'Masonry', 'Brickwork', 'Standard', 'per_sqm', 850, 'sqm', 
 '{"type": "Red Clay Brick", "thickness": "230mm", "mortar": "1:6 CM", "includes": "Labor"}'),
('AAC Block Masonry Complete', 'Masonry', 'AAC Blocks', 'Magicrete', 'per_sqm', 950, 'sqm', 
 '{"type": "AAC Block", "thickness": "200mm", "mortar": "Thin-bed adhesive", "includes": "Labor"}');

-- Create foundation task
INSERT INTO tasks (name, description, category, complexity_level, estimated_duration_hours) VALUES
('Complete Foundation Construction', 'Full foundation construction including excavation, PCC, RCC and backfilling', 'foundation', 'advanced', 120),
('Complete Superstructure Construction', 'Full superstructure including columns, beams and slabs', 'foundation', 'advanced', 180),
('Complete Masonry Work', 'Full masonry work including brickwork and blockwork', 'masonry', 'intermediate', 120);

-- Link components to tasks
UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Complete Foundation Construction' LIMIT 1) 
WHERE name = 'RCC M25 Complete';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Complete Superstructure Construction' LIMIT 1) 
WHERE name = 'TMT Steel Fe 500D Complete';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Complete Masonry Work' LIMIT 1) 
WHERE name = 'Brick Masonry Complete';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Complete Masonry Work' LIMIT 1) 
WHERE name = 'AAC Block Masonry Complete';

-- Add labor rates for structural work
INSERT INTO labor_rates (name, category, skill_level, unit, rates, productivity, location)
SELECT 'Foundation Labor', 'foundation', 'skilled', 'm³', '{"good": 1200, "better": 1500, "best": 1800}', '{"output_per_day": 1.5, "unit": "m³"}', 'gurgaon'
WHERE NOT EXISTS (SELECT 1 FROM labor_rates WHERE name = 'Foundation Labor');

INSERT INTO labor_rates (name, category, skill_level, unit, rates, productivity, location)
SELECT 'Steel Fixing Labor', 'foundation', 'skilled', 'kg', '{"good": 8, "better": 10, "best": 12}', '{"output_per_day": 120, "unit": "kg"}', 'gurgaon'
WHERE NOT EXISTS (SELECT 1 FROM labor_rates WHERE name = 'Steel Fixing Labor');

INSERT INTO labor_rates (name, category, skill_level, unit, rates, productivity, location)
SELECT 'Masonry Labor', 'masonry', 'skilled', 'sqm', '{"good": 300, "better": 350, "best": 400}', '{"output_per_day": 8, "unit": "sqm"}', 'gurgaon'
WHERE NOT EXISTS (SELECT 1 FROM labor_rates WHERE name = 'Masonry Labor');

-- Create task requirements for foundation
INSERT INTO task_requirements (task_id, component_id, labor_id, quantity_per_sqm, requirement_type) VALUES
-- Foundation construction requirements
((SELECT id FROM tasks WHERE name = 'Complete Foundation Construction' LIMIT 1), 
 (SELECT id FROM components WHERE name = 'RCC M25 Complete' LIMIT 1), 
 NULL, 0.15, 'material'), -- 0.15 m³ per sqm of built-up area
((SELECT id FROM tasks WHERE name = 'Complete Foundation Construction' LIMIT 1), 
 (SELECT id FROM components WHERE name = 'TMT Steel Fe 500D Complete' LIMIT 1), 
 NULL, 15, 'material'), -- 15 kg per sqm of built-up area
((SELECT id FROM tasks WHERE name = 'Complete Foundation Construction' LIMIT 1), 
 NULL, 
 (SELECT id FROM labor_rates WHERE name = 'Foundation Labor' LIMIT 1), 0.15, 'labor');

-- Create task requirements for superstructure
INSERT INTO task_requirements (task_id, component_id, labor_id, quantity_per_sqm, requirement_type) VALUES
-- Superstructure construction requirements
((SELECT id FROM tasks WHERE name = 'Complete Superstructure Construction' LIMIT 1), 
 (SELECT id FROM components WHERE name = 'RCC M25 Complete' LIMIT 1), 
 NULL, 0.2, 'material'), -- 0.2 m³ per sqm of built-up area
((SELECT id FROM tasks WHERE name = 'Complete Superstructure Construction' LIMIT 1), 
 (SELECT id FROM components WHERE name = 'TMT Steel Fe 500D Complete' LIMIT 1), 
 NULL, 20, 'material'), -- 20 kg per sqm of built-up area
((SELECT id FROM tasks WHERE name = 'Complete Superstructure Construction' LIMIT 1), 
 NULL, 
 (SELECT id FROM labor_rates WHERE name = 'Steel Fixing Labor' LIMIT 1), 20, 'labor');

-- Create task requirements for masonry
INSERT INTO task_requirements (task_id, component_id, labor_id, quantity_per_sqm, requirement_type) VALUES
-- Masonry construction requirements
((SELECT id FROM tasks WHERE name = 'Complete Masonry Work' LIMIT 1), 
 (SELECT id FROM components WHERE name = 'AAC Block Masonry Complete' LIMIT 1), 
 NULL, 0.8, 'material'), -- 0.8 sqm of wall per sqm of built-up area
((SELECT id FROM tasks WHERE name = 'Complete Masonry Work' LIMIT 1), 
 NULL, 
 (SELECT id FROM labor_rates WHERE name = 'Masonry Labor' LIMIT 1), 0.8, 'labor');

-- Update system configuration for realistic pricing
UPDATE system_config 
SET default_contingency = 7.5,
    default_profit_margin = 12.5
WHERE id = (SELECT id FROM system_config LIMIT 1);