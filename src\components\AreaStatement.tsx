import React from 'react';
import { CalculatedQuantities } from '../types/calculator';

interface AreaStatementProps {
  quantities: CalculatedQuantities;
  plotSize: number;
}

export function AreaStatement({ quantities, plotSize }: AreaStatementProps) {
  const formatArea = (area: number) => {
    return new Intl.NumberFormat('en-IN').format(Math.round(area));
  };

  const areaItems = [
    { label: 'Plot Size', value: plotSize, unit: 'sq ft', color: 'bg-gray-50' },
    { label: 'Ground Coverage', value: quantities.groundCoverageArea, unit: 'sq ft', color: 'bg-blue-50' },
    { label: 'Total Built-Up Area', value: quantities.totalBuiltUpArea, unit: 'sq ft', color: 'bg-green-50' },
    ...(quantities.basementArea > 0 ? [{ label: 'Basement Area', value: quantities.basementArea, unit: 'sq ft', color: 'bg-orange-50' }] : []),
    ...(quantities.stiltArea > 0 ? [{ label: 'Stilt Area', value: quantities.stiltArea, unit: 'sq ft', color: 'bg-purple-50' }] : []),
    { label: 'Total Construction', value: quantities.totalConstructionArea, unit: 'sq ft', color: 'bg-indigo-50' }
  ];

  return (
    <section className="bg-white rounded-xl shadow-lg p-4 sm:p-6 mb-4 sm:mb-6" aria-labelledby="area-statement-title">
      <h3 id="area-statement-title" className="text-lg sm:text-xl font-semibold text-gray-800 mb-4 flex items-center">
        <span className="w-2 h-2 bg-blue-600 rounded-full mr-3" aria-hidden="true"></span>
        Project Area Statement
      </h3>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 sm:gap-4">
        {areaItems.map((item, index) => (
          <div key={index} className={`text-center p-3 sm:p-4 ${item.color} rounded-lg`}>
            <div className="text-lg sm:text-2xl font-bold text-gray-800" aria-label={`${item.label}: ${formatArea(item.value)} ${item.unit}`}>
              {formatArea(item.value)}
            </div>
            <div className="text-xs sm:text-sm text-gray-600 mt-1">{item.label}</div>
            <div className="text-xs text-gray-500">{item.unit}</div>
          </div>
        ))}
      </div>

      {/* Technical Details */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <h4 className="sr-only">Technical Specifications</h4>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Concrete Grade:</span>
            <span className="font-medium text-gray-800">{quantities.concreteGrade}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Steel Ratio (Columns):</span>
            <span className="font-medium text-gray-800">{quantities.steelRatioColumns} kg/m³</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Foundation Factor:</span>
            <span className="font-medium text-gray-800">{quantities.foundationMultiplier}x</span>
          </div>
        </div>
      </div>
    </section>
  );
}