import React from 'react';
import { X, Download, Share2, <PERSON><PERSON><PERSON>, Home, MapPin, Award, Palette, Clock, Users, Calendar } from 'lucide-react';
import { ProjectSummary as ProjectSummaryType, QualityTier, UserInputs, CalculationResult } from '../types/calculator';
import { calculateProjectTimeline, ProjectTimeline } from '../utils/timelineCalculator';

interface ProjectSummaryProps {
  isOpen: boolean;
  onClose: () => void;
  summary: ProjectSummaryType;
  inputs: UserInputs;
  result: CalculationResult;
}

export function ProjectSummary({ isOpen, onClose, summary, inputs, result }: ProjectSummaryProps) {
  if (!isOpen) return null;

  // Calculate project timeline
  const timeline: ProjectTimeline = calculateProjectTimeline(inputs, result.geometricQuantities, result.qualityTier);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const qualityTierLabels = {
    good: 'Good (Budget)',
    better: 'Better (Premium)',
    best: 'Best (Luxury)'
  };

  const costBreakdownData = [
    { label: 'Structure', value: summary.costBreakdown.structure, color: 'bg-blue-500' },
    { label: 'Finishes', value: summary.costBreakdown.finishes, color: 'bg-green-500' },
    { label: 'MEP', value: summary.costBreakdown.mep, color: 'bg-yellow-500' },
    { label: 'Fees', value: summary.costBreakdown.fees, color: 'bg-purple-500' }
  ];

  const totalBreakdown = Object.values(summary.costBreakdown).reduce((sum, value) => sum + value, 0);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-green-50 to-blue-50">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Project Summary</h2>
            <p className="text-gray-600 mt-1">Complete overview of your construction project</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Project Overview */}
          <div className="mb-8">
            <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <Home className="w-5 h-5 text-blue-600" />
              Project Overview
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-blue-800">
                  {summary.projectOverview.plotSize.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600">Plot Size (sq ft)</div>
              </div>
              <div className="bg-green-50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-green-800">
                  {summary.projectOverview.totalBuiltUpArea.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600">Built-Up Area (sq ft)</div>
              </div>
              <div className="bg-purple-50 rounded-lg p-4 text-center">
                <div className="text-lg font-bold text-purple-800 flex items-center justify-center gap-1">
                  <MapPin className="w-4 h-4" />
                  {summary.projectOverview.location}
                </div>
                <div className="text-sm text-gray-600">Location</div>
              </div>
              <div className="bg-orange-50 rounded-lg p-4 text-center">
                <div className="text-lg font-bold text-orange-800">
                  {summary.projectOverview.structuralOptions.length}
                </div>
                <div className="text-sm text-gray-600">Special Features</div>
              </div>
            </div>
            
            {summary.projectOverview.structuralOptions.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium text-gray-700 mb-2">Structural Features:</h4>
                <div className="flex flex-wrap gap-2">
                  {summary.projectOverview.structuralOptions.map((option, index) => (
                    <span key={index} className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                      {option}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Project Timeline */}
          <div className="mb-8">
            <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <Clock className="w-5 h-5 text-green-600" />
              Project Timeline Estimate
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-green-50 rounded-lg p-4 text-center border-2 border-green-200">
                <div className="text-3xl font-bold text-green-800">
                  {Math.ceil(timeline.totalDuration / 30)}
                </div>
                <div className="text-sm text-green-600">Months</div>
                <div className="text-xs text-gray-500 mt-1">
                  ({timeline.totalDuration} days)
                </div>
              </div>
              <div className="bg-blue-50 rounded-lg p-4 text-center border-2 border-blue-200">
                <div className="text-3xl font-bold text-blue-800">
                  {timeline.totalManDays}
                </div>
                <div className="text-sm text-blue-600">Total Man-Days</div>
                <div className="text-xs text-gray-500 mt-1">
                  Labor requirement
                </div>
              </div>
              <div className="bg-orange-50 rounded-lg p-4 text-center border-2 border-orange-200">
                <div className="text-3xl font-bold text-orange-800">
                  {timeline.phases.length}
                </div>
                <div className="text-sm text-orange-600">Construction Phases</div>
                <div className="text-xs text-gray-500 mt-1">
                  Major milestones
                </div>
              </div>
            </div>

            {/* Timeline Phases */}
            <div className="space-y-3">
              <h4 className="font-medium text-gray-700">Construction Phases:</h4>
              {timeline.phases.map((phase, index) => (
                <div key={phase.id} className={`border rounded-lg p-3 ${phase.criticalPath ? 'border-red-200 bg-red-50' : 'border-gray-200 bg-gray-50'}`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${phase.criticalPath ? 'bg-red-500' : 'bg-gray-500'}`}>
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium text-gray-800">{phase.name}</div>
                        <div className="text-sm text-gray-600">{phase.description}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-gray-800">{phase.duration} days</div>
                      <div className="text-sm text-gray-500">{phase.manDays} man-days</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Timeline Recommendations */}
            {timeline.recommendations.length > 0 && (
              <div className="mt-4 bg-yellow-50 rounded-lg p-4 border-2 border-yellow-200">
                <h4 className="font-semibold text-yellow-800 mb-2 flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  Timeline Recommendations
                </h4>
                <ul className="space-y-1">
                  {timeline.recommendations.map((recommendation, index) => (
                    <li key={index} className="text-sm text-yellow-700 flex items-start gap-2">
                      <span className="text-yellow-500 mt-1">•</span>
                      <span>{recommendation}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* Final Cost */}
          <div className="mb-8">
            <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white rounded-xl p-6 text-center">
              <h3 className="text-2xl font-semibold mb-2">Total Estimated Cost</h3>
              <div className="text-4xl font-bold mb-2">{formatCurrency(summary.finalCost)}</div>
              <div className="text-blue-100">
                Approximately {formatCurrency(summary.finalCost / summary.projectOverview.totalBuiltUpArea)} per sq ft
              </div>
            </div>
          </div>

          {/* Quality Profile */}
          <div className="mb-8">
            <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <Award className="w-5 h-5 text-yellow-600" />
              Quality Profile
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="font-medium text-gray-700 mb-1">Overall Tier</div>
                <div className="text-lg font-bold text-blue-600">
                  {qualityTierLabels[summary.qualityProfile.overallTier]}
                </div>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="font-medium text-gray-700 mb-1">Structure</div>
                <div className="text-sm text-gray-800">{summary.qualityProfile.structure}</div>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="font-medium text-gray-700 mb-1">Finishing</div>
                <div className="text-sm text-gray-800">{summary.qualityProfile.finishing}</div>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="font-medium text-gray-700 mb-1">Fittings</div>
                <div className="text-sm text-gray-800">{summary.qualityProfile.fittings}</div>
              </div>
            </div>
          </div>

          {/* Cost Breakdown */}
          <div className="mb-8">
            <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <PieChart className="w-5 h-5 text-green-600" />
              Cost Breakdown
            </h3>
            <div className="space-y-3">
              {costBreakdownData.map((item) => {
                const percentage = (item.value / totalBreakdown) * 100;
                return (
                  <div key={item.label} className="flex items-center gap-4">
                    <div className="w-24 text-sm font-medium text-gray-700">{item.label}</div>
                    <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
                      <div
                        className={`${item.color} h-6 rounded-full flex items-center justify-end pr-2`}
                        style={{ width: `${percentage}%` }}
                      >
                        <span className="text-white text-xs font-medium">
                          {percentage.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                    <div className="w-24 text-sm font-bold text-gray-800 text-right">
                      {formatCurrency(item.value)}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Custom Choices */}
          {summary.customChoices.length > 0 && (
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <Palette className="w-5 h-5 text-purple-600" />
                Your Custom Choices
              </h3>
              <div className="space-y-3">
                {summary.customChoices.map((choice, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="font-medium text-gray-800 mb-2">{choice.roomType}</div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Flooring:</span>
                        <div className="font-medium">{choice.flooringMaterial}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Paint:</span>
                        <div className="font-medium">{choice.paintMaterial}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Electrical:</span>
                        <div className="font-medium">{choice.electricalPoints} points</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Plumbing:</span>
                        <div className="font-medium">{choice.plumbingFixtures} fixtures</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            Generated on {new Date().toLocaleDateString('en-IN')} • Timeline based on productivity analysis
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => alert('PDF download feature coming soon!')}
              className="flex items-center gap-2 px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg font-medium transition-colors"
            >
              <Download className="w-4 h-4" />
              Download PDF
            </button>
            <button
              onClick={() => alert('Share feature coming soon!')}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
            >
              <Share2 className="w-4 h-4" />
              Share Summary
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}