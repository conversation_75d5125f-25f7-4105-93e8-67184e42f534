/*
  # Populate Initial High-Quality Default Data
  
  This migration populates the database with premium "Better" tier defaults
  for the Delhi/NCR market as specified in the directive.
*/

-- Insert Labor Rates
INSERT INTO labor_rates (name, category, skill_level, unit, rates, productivity, location) VALUES
('Tile Installation', 'flooring', 'skilled', 'sqm', '{"good": 100, "better": 120, "best": 140}', '{"output_per_day": 15, "unit": "sqm"}', 'gurgaon'),
('Marble Installation', 'flooring', 'specialist', 'sqm', '{"good": 150, "better": 180, "best": 220}', '{"output_per_day": 10, "unit": "sqm"}', 'gurgaon'),
('Bathroom Fitting Installation', 'plumbing', 'skilled', 'set', '{"good": 800, "better": 1000, "best": 1200}', '{"output_per_day": 2, "unit": "set"}', 'gurgaon'),
('Window Installation', 'doors_windows', 'skilled', 'sqm', '{"good": 80, "better": 95, "best": 110}', '{"output_per_day": 12, "unit": "sqm"}', 'gurgaon'),
('Facade Installation', 'exterior', 'specialist', 'sqm', '{"good": 200, "better": 250, "best": 300}', '{"output_per_day": 8, "unit": "sqm"}', 'gurgaon');

-- Insert Tasks
INSERT INTO tasks (name, description, category, complexity_level, estimated_duration_hours) VALUES
('Install Vitrified Tiles', 'Complete vitrified tile installation including adhesive and grouting', 'flooring', 'intermediate', 6),
('Install Italian Marble', 'Premium marble installation with polishing and sealing', 'flooring', 'advanced', 8),
('Install Anti-Skid Tiles', 'Bathroom anti-skid tile installation with waterproofing', 'flooring', 'intermediate', 5),
('Install uPVC Windows', 'Complete uPVC window installation with sealing', 'doors_windows', 'intermediate', 4),
('Install Aluminium Windows', 'Standard aluminium window installation', 'doors_windows', 'basic', 3),
('Install Bathroom Fittings Bundle', 'Complete bathroom fittings installation', 'plumbing', 'intermediate', 8),
('Install Stone Cladding', 'Natural stone facade cladding installation', 'exterior', 'advanced', 10),
('Install HPL Sheets', 'High-pressure laminate facade installation', 'exterior', 'intermediate', 6);

-- Insert Components
INSERT INTO components (name, category, sub_category, brand, cost_model, unit_price, unit, image_url, specifications) VALUES
-- Flooring Components
('Kajaria Vitrified Tiles 4x2', 'Flooring', 'Vitrified Tiles', 'Kajaria', 'per_sqm', 75, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"size": "4x2 ft", "thickness": "8mm", "finish": "Glossy"}'),
('Somany Ceramic Tiles 2x2', 'Flooring', 'Ceramic Tiles', 'Somany', 'per_sqm', 45, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"size": "2x2 ft", "thickness": "6mm", "finish": "Matt"}'),
('Italian Carrara Marble', 'Flooring', 'Natural Stone', 'Imported', 'per_sqm', 250, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"origin": "Italy", "thickness": "18mm", "finish": "Polished"}'),
('Anti-Skid Bathroom Tiles', 'Flooring', 'Anti-Skid Tiles', 'Standard', 'per_sqm', 50, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"size": "1x1 ft", "slip_resistance": "R11", "finish": "Textured"}'),

-- Window Components
('Standard uPVC Windows', 'Windows', 'uPVC', 'Fenesta', 'per_sqm', 700, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"profile": "60mm", "glass": "5mm clear", "hardware": "German"}'),
('Aluminium Windows', 'Windows', 'Aluminium', 'Jindal', 'per_sqm', 450, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"profile": "50mm", "glass": "4mm clear", "finish": "Powder coated"}'),

-- Bathroom Fittings
('Jaquar WC', 'Bathroom Fittings', 'Water Closet', 'Jaquar', 'per_unit', 8000, 'piece', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Wall mounted", "flush": "Dual flush", "warranty": "10 years"}'),
('Jaquar Washbasin', 'Bathroom Fittings', 'Basin', 'Jaquar', 'per_unit', 3500, 'piece', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Table top", "material": "Ceramic", "size": "600x400mm"}'),
('Jaquar Mirror', 'Bathroom Fittings', 'Mirror', 'Jaquar', 'per_unit', 2000, 'piece', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"size": "600x800mm", "type": "LED backlit", "frame": "Stainless steel"}'),
('Jaquar Diverter Set', 'Bathroom Fittings', 'Faucets', 'Jaquar', 'per_unit', 6000, 'set', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"type": "Concealed", "finish": "Chrome", "cartridge": "Ceramic"}'),

-- Facade Components
('Natural Stone Cladding', 'Facade', 'Stone Cladding', 'Rajasthan Stone', 'per_sqm', 180, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"thickness": "20mm", "finish": "Natural", "origin": "Rajasthan"}'),
('HPL Facade Sheets', 'Facade', 'HPL Sheets', 'Greenlam', 'per_sqm', 120, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"thickness": "8mm", "finish": "Wood grain", "fire_rating": "Class 1"}'),

-- Supporting Materials
('Tile Adhesive', 'Materials', 'Adhesive', 'Pidilite', 'per_sqm', 25, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"coverage": "1.5kg per sqm", "type": "Polymer modified"}'),
('Marble Mortar', 'Materials', 'Mortar', 'ACC', 'per_sqm', 35, 'sqm', 'https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg', '{"thickness": "20mm bed", "type": "High strength"}');

-- Link components to tasks by updating associated_task_id
UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Vitrified Tiles') 
WHERE name = 'Kajaria Vitrified Tiles 4x2';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Italian Marble') 
WHERE name = 'Italian Carrara Marble';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Anti-Skid Tiles') 
WHERE name = 'Anti-Skid Bathroom Tiles';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install uPVC Windows') 
WHERE name = 'Standard uPVC Windows';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Aluminium Windows') 
WHERE name = 'Aluminium Windows';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Stone Cladding') 
WHERE name = 'Natural Stone Cladding';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install HPL Sheets') 
WHERE name = 'HPL Facade Sheets';

-- Insert Task Requirements (Recipes)
INSERT INTO task_requirements (task_id, component_id, labor_id, quantity_per_sqm, requirement_type) VALUES
-- Vitrified Tiles Recipe
((SELECT id FROM tasks WHERE name = 'Install Vitrified Tiles'), 
 (SELECT id FROM components WHERE name = 'Kajaria Vitrified Tiles 4x2'), 
 NULL, 1.05, 'material'), -- 5% wastage
((SELECT id FROM tasks WHERE name = 'Install Vitrified Tiles'), 
 (SELECT id FROM components WHERE name = 'Tile Adhesive'), 
 NULL, 1.0, 'material'),
((SELECT id FROM tasks WHERE name = 'Install Vitrified Tiles'), 
 NULL, 
 (SELECT id FROM labor_rates WHERE name = 'Tile Installation'), 1.0, 'labor'),

-- Italian Marble Recipe
((SELECT id FROM tasks WHERE name = 'Install Italian Marble'), 
 (SELECT id FROM components WHERE name = 'Italian Carrara Marble'), 
 NULL, 1.08, 'material'), -- 8% wastage for premium
((SELECT id FROM tasks WHERE name = 'Install Italian Marble'), 
 (SELECT id FROM components WHERE name = 'Marble Mortar'), 
 NULL, 1.0, 'material'),
((SELECT id FROM tasks WHERE name = 'Install Italian Marble'), 
 NULL, 
 (SELECT id FROM labor_rates WHERE name = 'Marble Installation'), 1.0, 'labor'),

-- Anti-Skid Tiles Recipe
((SELECT id FROM tasks WHERE name = 'Install Anti-Skid Tiles'), 
 (SELECT id FROM components WHERE name = 'Anti-Skid Bathroom Tiles'), 
 NULL, 1.05, 'material'),
((SELECT id FROM tasks WHERE name = 'Install Anti-Skid Tiles'), 
 (SELECT id FROM components WHERE name = 'Tile Adhesive'), 
 NULL, 1.0, 'material'),
((SELECT id FROM tasks WHERE name = 'Install Anti-Skid Tiles'), 
 NULL, 
 (SELECT id FROM labor_rates WHERE name = 'Tile Installation'), 1.0, 'labor');

-- Create Bathroom Fittings Bundle
INSERT INTO components (name, category, sub_category, brand, cost_model, unit_price, unit, specifications) VALUES
('Jaquar Premium Bathroom Bundle', 'Bathroom Fittings', 'Complete Set', 'Jaquar', 'per_unit', 19500, 'set', 
 '{"includes": ["WC", "Washbasin", "Mirror", "Diverter Set"], "total_value": 19500, "bundle_discount": 0}');

-- Insert Admin User
INSERT INTO admin_users (email, full_name, role) VALUES
('<EMAIL>', 'NirmaanAI Admin', 'super_admin');

-- Insert UI Defaults Configuration
INSERT INTO ui_defaults (config_name, config_data) VALUES
('default_selections', '{
  "qualityTier": "better",
  "shell": {
    "externalWalls": "aac_blocks",
    "windows": "upvc_standard"
  },
  "rooms": {
    "Master Bedroom": {
      "flooring": "kajaria_vitrified"
    },
    "Bedroom": {
      "flooring": "kajaria_vitrified"
    },
    "Living Room": {
      "flooring": "kajaria_vitrified"
    },
    "Kitchen": {
      "flooring": "anti_skid_tiles"
    },
    "Bathroom": {
      "flooring": "anti_skid_tiles",
      "fittingsBundle": "jaquar_premium_bundle"
    },
    "Dining Room": {
      "flooring": "kajaria_vitrified"
    }
  },
  "facade": {
    "primaryFinish": "natural_stone_cladding",
    "accentFinish": "hpl_sheets"
  }
}');