import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Package, Star, TrendingUp, Filter } from 'lucide-react';
import { Component, componentAPI } from '../../lib/supabase';

interface MultiBrandManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

interface BrandGroup {
  category: string;
  brands: {
    brand: string;
    components: Component[];
    avgPrice: number;
    priceRange: { min: number; max: number };
  }[];
}

export function MultiBrandManager({ isOpen, onClose }: MultiBrandManagerProps) {
  const [brandGroups, setBrandGroups] = useState<BrandGroup[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [showAddVariant, setShowAddVariant] = useState(false);
  const [selectedComponent, setSelectedComponent] = useState<Component | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadBrandData();
    }
  }, [isOpen, selectedCategory]);

  const loadBrandData = async () => {
    setIsLoading(true);
    try {
      const components = await componentAPI.getAll();
      
      // Group components by category and brand
      const grouped = components.reduce((acc, component) => {
        if (!component.brand) return acc;
        
        const category = component.category;
        if (selectedCategory !== 'all' && category !== selectedCategory) return acc;
        
        if (!acc[category]) {
          acc[category] = {};
        }
        
        if (!acc[category][component.brand]) {
          acc[category][component.brand] = [];
        }
        
        acc[category][component.brand].push(component);
        return acc;
      }, {} as Record<string, Record<string, Component[]>>);

      // Convert to BrandGroup format
      const brandGroups: BrandGroup[] = Object.entries(grouped).map(([category, brands]) => ({
        category,
        brands: Object.entries(brands).map(([brand, components]) => {
          const prices = components.map(c => Number(c.unit_price));
          return {
            brand,
            components,
            avgPrice: prices.reduce((sum, price) => sum + price, 0) / prices.length,
            priceRange: {
              min: Math.min(...prices),
              max: Math.max(...prices)
            }
          };
        }).sort((a, b) => a.avgPrice - b.avgPrice)
      }));

      setBrandGroups(brandGroups);
    } catch (error) {
      console.error('Error loading brand data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const categories = ['all', ...new Set(brandGroups.map(g => g.category))];

  const addBrandVariant = async (baseComponent: Component, newBrand: string, newPrice: number) => {
    try {
      const newComponent = {
        ...baseComponent,
        name: `${baseComponent.name.replace(baseComponent.brand || '', newBrand).trim()}`,
        brand: newBrand,
        unit_price: newPrice,
        id: undefined,
        created_at: undefined,
        updated_at: undefined
      };

      await componentAPI.create(newComponent as any);
      await loadBrandData();
      setShowAddVariant(false);
      setSelectedComponent(null);
    } catch (error) {
      console.error('Error adding brand variant:', error);
      alert('Error adding brand variant');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-6xl h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <Package className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-800">Multi-Brand Component Manager</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            ×
          </button>
        </div>

        {/* Controls */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-500" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm"
              >
                {categories.map(cat => (
                  <option key={cat} value={cat}>
                    {cat === 'all' ? 'All Categories' : cat}
                  </option>
                ))}
              </select>
            </div>
            <button
              onClick={() => setShowAddVariant(true)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
            >
              <Plus className="w-4 h-4" />
              Add Brand Variant
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
            </div>
          ) : (
            <div className="space-y-8">
              {brandGroups.map((group) => (
                <div key={group.category} className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                    <Package className="w-5 h-5 text-blue-600" />
                    {group.category}
                    <span className="text-sm text-gray-500">({group.brands.length} brands)</span>
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {group.brands.map((brandData) => (
                      <div key={brandData.brand} className="bg-white rounded-lg border border-gray-200 p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h4 className="font-semibold text-gray-800">{brandData.brand}</h4>
                            <p className="text-sm text-gray-600">{brandData.components.length} products</p>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-semibold text-green-600">
                              Avg: ₹{brandData.avgPrice.toLocaleString()}
                            </div>
                            <div className="text-xs text-gray-500">
                              ₹{brandData.priceRange.min.toLocaleString()} - ₹{brandData.priceRange.max.toLocaleString()}
                            </div>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          {brandData.components.slice(0, 3).map((component) => (
                            <div key={component.id} className="flex items-center justify-between text-sm">
                              <span className="text-gray-700 truncate flex-1">{component.name}</span>
                              <span className="font-medium text-gray-900 ml-2">
                                ₹{Number(component.unit_price).toLocaleString()}
                              </span>
                            </div>
                          ))}
                          {brandData.components.length > 3 && (
                            <div className="text-xs text-gray-500 text-center pt-1">
                              +{brandData.components.length - 3} more
                            </div>
                          )}
                        </div>
                        
                        <div className="mt-3 pt-3 border-t border-gray-100">
                          <button
                            onClick={() => {
                              setSelectedComponent(brandData.components[0]);
                              setShowAddVariant(true);
                            }}
                            className="w-full text-sm text-blue-600 hover:text-blue-700 font-medium"
                          >
                            Add Variant
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Add Variant Modal */}
      {showAddVariant && (
        <AddVariantModal
          baseComponent={selectedComponent}
          onSave={addBrandVariant}
          onClose={() => {
            setShowAddVariant(false);
            setSelectedComponent(null);
          }}
        />
      )}
    </div>
  );
}

interface AddVariantModalProps {
  baseComponent: Component | null;
  onSave: (baseComponent: Component, newBrand: string, newPrice: number) => void;
  onClose: () => void;
}

function AddVariantModal({ baseComponent, onSave, onClose }: AddVariantModalProps) {
  const [newBrand, setNewBrand] = useState('');
  const [newPrice, setNewPrice] = useState(0);

  if (!baseComponent) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newBrand.trim() && newPrice > 0) {
      onSave(baseComponent, newBrand.trim(), newPrice);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Add Brand Variant</h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Base Component</label>
            <input
              type="text"
              value={baseComponent.name}
              disabled
              className="w-full px-3 py-2 border border-gray-200 rounded-lg bg-gray-50"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">New Brand</label>
            <input
              type="text"
              value={newBrand}
              onChange={(e) => setNewBrand(e.target.value)}
              placeholder="Enter brand name"
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Price (₹)</label>
            <input
              type="number"
              value={newPrice}
              onChange={(e) => setNewPrice(Number(e.target.value))}
              placeholder="Enter price"
              min="0"
              step="0.01"
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              Add Variant
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
