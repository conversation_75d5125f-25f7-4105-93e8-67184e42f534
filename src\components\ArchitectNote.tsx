import React from 'react';
import { Info } from 'lucide-react';

interface ArchitectNoteProps {
  title: string;
  content: string;
  className?: string;
}

export function ArchitectNote({ title, content, className = '' }: ArchitectNoteProps) {
  return (
    <div className={`group relative ${className}`}>
      <div className="flex items-center gap-1 cursor-help">
        <Info className="w-4 h-4 text-blue-500 hover:text-blue-600 transition-colors" />
        <span className="text-xs text-blue-500 font-medium">Architect's Note</span>
      </div>
      
      {/* Tooltip */}
      <div className="absolute bottom-full left-0 mb-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg p-4 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
        <div className="text-sm font-semibold text-gray-800 mb-2">{title}</div>
        <div className="text-sm text-gray-600 leading-relaxed">{content}</div>
        
        {/* Arrow */}
        <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-200"></div>
      </div>
    </div>
  );
}