-- =====================================================
-- NIRMAANAI V2.1: CONFIGURABLE PARAMETERS DATABASE SETUP
-- =====================================================
-- Run these SQL commands in your Supabase SQL Editor
-- User: <EMAIL>
-- Project: floor-plan-analyzer-admin or central-service-db

-- =====================================================
-- 1. MATERIAL RATES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS material_rates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  category VARCHAR(50) NOT NULL,
  name VARCHAR(100) NOT NULL,
  rate DECIMAL(10,2) NOT NULL,
  unit VARCHAR(20) NOT NULL,
  quality_tier VARCHAR(20) DEFAULT 'better',
  location VARCHAR(50) DEFAULT 'delhi',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  -- Constraints
  CONSTRAINT material_rates_rate_positive CHECK (rate > 0),
  CONSTRAINT material_rates_quality_tier_check CHECK (quality_tier IN ('good', 'better', 'best'))
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_material_rates_category ON material_rates(category);
CREATE INDEX IF NOT EXISTS idx_material_rates_quality_tier ON material_rates(quality_tier);
CREATE INDEX IF NOT EXISTS idx_material_rates_location ON material_rates(location);
CREATE INDEX IF NOT EXISTS idx_material_rates_active ON material_rates(is_active);

-- Insert default material rates
INSERT INTO material_rates (category, name, rate, unit, quality_tier, location) VALUES
-- Excavation rates
('excavation', 'Manual Excavation', 450.00, 'per_m3', 'better', 'delhi'),
('excavation', 'Machine Excavation', 350.00, 'per_m3', 'better', 'delhi'),

-- Plaster rates
('plaster', 'Cement Plaster 12mm Internal', 85.00, 'per_sqm', 'better', 'delhi'),
('plaster', 'Cement Plaster 15mm External', 95.00, 'per_sqm', 'better', 'delhi'),
('plaster', 'Gypsum Plaster Internal', 120.00, 'per_sqm', 'best', 'delhi'),

-- Electrical rates
('electrical', 'Electrical Point Installation', 350.00, 'per_point', 'better', 'delhi'),
('electrical', 'Electrical Point Premium', 450.00, 'per_point', 'best', 'delhi'),
('electrical', 'Electrical Point Basic', 280.00, 'per_point', 'good', 'delhi'),

-- Plumbing rates
('plumbing', 'Plumbing Point Installation', 500.00, 'per_point', 'better', 'delhi'),
('plumbing', 'Plumbing Point Premium', 650.00, 'per_point', 'best', 'delhi'),
('plumbing', 'Plumbing Point Basic', 400.00, 'per_point', 'good', 'delhi'),

-- Waterproofing rates by quality tier
('waterproofing', 'Membrane Waterproofing', 60.00, 'per_sqm', 'good', 'delhi'),
('waterproofing', 'Membrane Waterproofing', 85.00, 'per_sqm', 'better', 'delhi'),
('waterproofing', 'Membrane Waterproofing', 120.00, 'per_sqm', 'best', 'delhi'),
('waterproofing', 'Liquid Waterproofing', 45.00, 'per_sqm', 'good', 'delhi'),
('waterproofing', 'Liquid Waterproofing', 65.00, 'per_sqm', 'better', 'delhi'),
('waterproofing', 'Liquid Waterproofing', 95.00, 'per_sqm', 'best', 'delhi');

-- =====================================================
-- 2. CALCULATION FACTORS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS calculation_factors (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  factor_name VARCHAR(100) NOT NULL UNIQUE,
  factor_value DECIMAL(10,4) NOT NULL,
  description TEXT,
  category VARCHAR(50) NOT NULL,
  unit VARCHAR(50),
  min_value DECIMAL(10,4),
  max_value DECIMAL(10,4),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  -- Constraints
  CONSTRAINT calculation_factors_value_positive CHECK (factor_value > 0),
  CONSTRAINT calculation_factors_min_max_check CHECK (min_value IS NULL OR max_value IS NULL OR min_value <= max_value)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_calculation_factors_category ON calculation_factors(category);
CREATE INDEX IF NOT EXISTS idx_calculation_factors_active ON calculation_factors(is_active);

-- Insert default calculation factors
INSERT INTO calculation_factors (factor_name, factor_value, description, category, unit, min_value, max_value) VALUES
-- Plaster calculation factors
('plaster_coverage_factor', 3.0000, 'Internal plaster coverage multiplier for walls and ceiling', 'plaster', 'multiplier', 2.5000, 4.0000),
('plaster_external_factor', 1.1000, 'External plaster additional coverage for architectural features', 'plaster', 'multiplier', 1.0000, 1.3000),

-- Opening density factors
('door_density', 200.0000, 'Square meters of built-up area per door', 'openings', 'sqm_per_door', 150.0000, 300.0000),
('window_density', 150.0000, 'Square meters of built-up area per window', 'openings', 'sqm_per_window', 100.0000, 250.0000),

-- Excavation factors
('excavation_extra_factor', 1.2000, 'Extra excavation percentage for working space', 'excavation', 'multiplier', 1.1000, 1.5000),

-- Wall calculation factors
('internal_wall_factor', 0.8000, 'Internal wall area calculation factor', 'walls', 'multiplier', 0.6000, 1.0000),
('wall_height_factor', 1.0000, 'Wall height adjustment factor', 'walls', 'multiplier', 0.9000, 1.1000),

-- Opening distribution factors
('opening_external_distribution', 0.7000, 'Percentage of openings on external walls', 'openings', 'percentage', 0.6000, 0.8000),
('opening_internal_distribution', 0.3000, 'Percentage of openings on internal walls', 'openings', 'percentage', 0.2000, 0.4000),

-- MEP calculation factors
('electrical_points_per_sqm', 0.0086, 'Electrical points per square meter of floor area', 'electrical', 'points_per_sqm', 0.0060, 0.0120),
('plumbing_points_per_sqm', 0.0043, 'Plumbing points per square meter of floor area', 'plumbing', 'points_per_sqm', 0.0030, 0.0060);

-- =====================================================
-- 3. OPENING SIZES TABLE (IS 1200:2012 COMPLIANT)
-- =====================================================
CREATE TABLE IF NOT EXISTS opening_sizes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  opening_type VARCHAR(50) NOT NULL,
  dimension_type VARCHAR(20) NOT NULL,
  value DECIMAL(5,2) NOT NULL,
  unit VARCHAR(10) DEFAULT 'meters',
  standard_reference VARCHAR(50) DEFAULT 'IS 1200:2012',
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  -- Constraints
  CONSTRAINT opening_sizes_value_positive CHECK (value > 0),
  CONSTRAINT opening_sizes_dimension_type_check CHECK (dimension_type IN ('height', 'width', 'area')),
  CONSTRAINT opening_sizes_opening_type_check CHECK (opening_type IN ('door', 'window', 'ventilator', 'french_door')),
  
  -- Unique constraint for opening_type + dimension_type combination
  UNIQUE(opening_type, dimension_type)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_opening_sizes_type ON opening_sizes(opening_type);
CREATE INDEX IF NOT EXISTS idx_opening_sizes_active ON opening_sizes(is_active);

-- Insert IS 1200:2012 compliant opening sizes
INSERT INTO opening_sizes (opening_type, dimension_type, value, standard_reference, description) VALUES
-- Standard doors (IS 1200:2012)
('door', 'height', 2.10, 'IS 1200:2012', 'Standard door height as per IS 1200:2012'),
('door', 'width', 1.00, 'IS 1200:2012', 'Standard door width as per IS 1200:2012'),

-- Standard windows (IS 1200:2012)
('window', 'height', 1.50, 'IS 1200:2012', 'Standard window height as per IS 1200:2012'),
('window', 'width', 1.20, 'IS 1200:2012', 'Standard window width as per IS 1200:2012'),

-- French doors
('french_door', 'height', 2.10, 'IS 1200:2012', 'French door height as per IS 1200:2012'),
('french_door', 'width', 1.50, 'IS 1200:2012', 'French door width as per IS 1200:2012'),

-- Ventilators
('ventilator', 'height', 0.60, 'IS 1200:2012', 'Ventilator height as per IS 1200:2012'),
('ventilator', 'width', 1.20, 'IS 1200:2012', 'Ventilator width as per IS 1200:2012');

-- =====================================================
-- 4. EXTEND ENGINEERING_STANDARDS TABLE
-- =====================================================
-- Add new columns to existing engineering_standards table for structural parameters
ALTER TABLE engineering_standards 
ADD COLUMN IF NOT EXISTS min_value DECIMAL(10,4),
ADD COLUMN IF NOT EXISTS max_value DECIMAL(10,4),
ADD COLUMN IF NOT EXISTS step_value DECIMAL(10,4),
ADD COLUMN IF NOT EXISTS unit VARCHAR(20);

-- Insert new structural parameters
INSERT INTO engineering_standards (category, name, value, unit, min_value, max_value, step_value) VALUES
-- Floor height configuration
('structural', 'floor_height', 3.0, 'meters', 2.8, 3.5, 0.1),

-- Beam dimensions
('structural', 'beam_width', 300, 'mm', 250, 400, 25),
('structural', 'beam_depth', 450, 'mm', 350, 600, 25),

-- Column sizing matrix
('structural', 'column_size_low_rise', 350, 'mm', 300, 450, 25),
('structural', 'column_size_mid_rise', 400, 'mm', 350, 500, 25),
('structural', 'column_size_high_rise', 450, 'mm', 400, 600, 25)

ON CONFLICT (category, name) DO UPDATE SET
  value = EXCLUDED.value,
  unit = EXCLUDED.unit,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  step_value = EXCLUDED.step_value,
  updated_at = NOW();

-- =====================================================
-- 5. CREATE VIEWS FOR EASY ACCESS
-- =====================================================

-- View for material rates by category and quality tier
CREATE OR REPLACE VIEW v_material_rates_by_tier AS
SELECT 
  category,
  name,
  quality_tier,
  rate,
  unit,
  location,
  is_active
FROM material_rates
WHERE is_active = true
ORDER BY category, quality_tier, name;

-- View for calculation factors by category
CREATE OR REPLACE VIEW v_calculation_factors_by_category AS
SELECT 
  category,
  factor_name,
  factor_value,
  description,
  unit,
  min_value,
  max_value,
  is_active
FROM calculation_factors
WHERE is_active = true
ORDER BY category, factor_name;

-- View for opening sizes
CREATE OR REPLACE VIEW v_opening_sizes_standard AS
SELECT 
  opening_type,
  dimension_type,
  value,
  unit,
  standard_reference,
  description,
  is_active
FROM opening_sizes
WHERE is_active = true
ORDER BY opening_type, dimension_type;

-- =====================================================
-- 6. ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE material_rates ENABLE ROW LEVEL SECURITY;
ALTER TABLE calculation_factors ENABLE ROW LEVEL SECURITY;
ALTER TABLE opening_sizes ENABLE ROW LEVEL SECURITY;

-- Create policies for authenticated users
CREATE POLICY "Allow authenticated users to read material_rates" ON material_rates
  FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated users to read calculation_factors" ON calculation_factors
  FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated users to read opening_sizes" ON opening_sizes
  FOR SELECT TO authenticated USING (true);

-- Admin users can modify (you'll need to adjust based on your admin role setup)
CREATE POLICY "Allow admin users to modify material_rates" ON material_rates
  FOR ALL TO authenticated 
  USING (auth.jwt() ->> 'role' = 'admin' OR auth.jwt() ->> 'email' = '<EMAIL>');

CREATE POLICY "Allow admin users to modify calculation_factors" ON calculation_factors
  FOR ALL TO authenticated 
  USING (auth.jwt() ->> 'role' = 'admin' OR auth.jwt() ->> 'email' = '<EMAIL>');

CREATE POLICY "Allow admin users to modify opening_sizes" ON opening_sizes
  FOR ALL TO authenticated 
  USING (auth.jwt() ->> 'role' = 'admin' OR auth.jwt() ->> 'email' = '<EMAIL>');

-- =====================================================
-- 7. TRIGGER FOR UPDATED_AT TIMESTAMPS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for all tables
CREATE TRIGGER update_material_rates_updated_at 
  BEFORE UPDATE ON material_rates 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_calculation_factors_updated_at 
  BEFORE UPDATE ON calculation_factors 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_opening_sizes_updated_at 
  BEFORE UPDATE ON opening_sizes 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- SETUP COMPLETE!
-- =====================================================
-- You now have 20+ configurable parameters:
-- - 6 Structural parameters (floor height, beam dimensions, column sizes)
-- - 7 Material rates (excavation, plaster, electrical, plumbing, waterproofing)
-- - 10 Calculation factors (coverage factors, densities, multipliers)
-- - 4 Opening sizes (doors, windows - IS 1200:2012 compliant)
-- =====================================================
