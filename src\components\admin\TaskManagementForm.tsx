import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2, Save, Info } from 'lucide-react';
import { Task, Component, LaborRate, taskAPI, componentAPI, laborRateAPI } from '../../lib/supabase';

interface TaskManagementFormProps {
  task?: Task;
  onSave: (task: any) => void;
  onCancel: () => void;
}

interface TaskRequirement {
  id?: string;
  type: 'material' | 'labor' | 'tool';
  componentId?: string;
  laborId?: string;
  quantityPerSqm: number;
  isOptional: boolean;
}

export function TaskManagementForm({ task, onSave, onCancel }: TaskManagementFormProps) {
  const [formData, setFormData] = useState<Partial<Task>>({
    name: '',
    description: '',
    category: '',
    complexity_level: 'intermediate',
    estimated_duration_hours: 8,
    is_active: true
  });
  
  const [requirements, setRequirements] = useState<TaskRequirement[]>([]);
  const [components, setComponents] = useState<Component[]>([]);
  const [laborRates, setLaborRates] = useState<LaborRate[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (task) {
      setFormData({
        name: task.name,
        description: task.description,
        category: task.category,
        complexity_level: task.complexity_level,
        estimated_duration_hours: task.estimated_duration_hours,
        is_active: task.is_active
      });
      
      // Load task requirements if available
      if (task.task_requirements) {
        const reqs = task.task_requirements.map((req: any) => ({
          id: req.id,
          type: req.requirement_type,
          componentId: req.component_id,
          laborId: req.labor_id,
          quantityPerSqm: req.quantity_per_sqm,
          isOptional: req.is_optional
        }));
        setRequirements(reqs);
      }
    }
    
    loadComponentsAndLabor();
  }, [task]);

  const loadComponentsAndLabor = async () => {
    setIsLoading(true);
    try {
      const [comps, labor] = await Promise.all([
        componentAPI.getAll(),
        laborRateAPI.getAll()
      ]);
      
      setComponents(comps);
      setLaborRates(labor);
    } catch (error) {
      console.error('Error loading components and labor:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  const addRequirement = (type: 'material' | 'labor' | 'tool') => {
    setRequirements(prev => [
      ...prev,
      {
        type,
        quantityPerSqm: 1,
        isOptional: false
      }
    ]);
  };

  const updateRequirement = (index: number, updates: Partial<TaskRequirement>) => {
    setRequirements(prev => prev.map((req, i) => 
      i === index ? { ...req, ...updates } : req
    ));
  };

  const removeRequirement = (index: number) => {
    setRequirements(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    
    try {
      // First save the task
      const taskData = {
        ...formData,
        // Convert numeric fields
        estimated_duration_hours: Number(formData.estimated_duration_hours)
      };
      
      let savedTask: Task;
      
      if (task?.id) {
        // Update existing task
        savedTask = await taskAPI.update(task.id, taskData as any);
      } else {
        // Create new task
        savedTask = await taskAPI.create(taskData as any);
      }
      
      // Then save all requirements
      const taskRequirements = requirements.map(req => ({
        task_id: savedTask.id,
        component_id: req.componentId,
        labor_id: req.laborId,
        quantity_per_sqm: req.quantityPerSqm,
        is_optional: req.isOptional,
        requirement_type: req.type
      }));
      
      // Update task requirements
      await taskAPI.updateTaskRequirements(savedTask.id, taskRequirements);
      
      // Call onSave with the complete data
      onSave({
        ...savedTask,
        task_requirements: taskRequirements
      });
    } catch (error) {
      console.error('Error saving task:', error);
      alert('Error saving task. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-green-50 to-teal-50">
          <div>
            <h2 className="text-xl font-bold text-gray-800">
              {task ? 'Edit Task' : 'Create New Task'}
            </h2>
            <p className="text-gray-600 mt-1">Define installation recipe with materials and labor</p>
          </div>
          <button
            onClick={onCancel}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isSaving}
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="overflow-y-auto max-h-[calc(90vh-200px)]">
          <div className="p-6 space-y-6">
            {/* Task Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Task Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  required
                  disabled={isSaving}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  required
                  disabled={isSaving}
                >
                  <option value="">Select category...</option>
                  <option value="flooring">Flooring</option>
                  <option value="masonry">Masonry</option>
                  <option value="plumbing">Plumbing</option>
                  <option value="electrical">Electrical</option>
                  <option value="doors_windows">Doors & Windows</option>
                  <option value="exterior">Exterior & Facade</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Complexity Level
                </label>
                <select
                  name="complexity_level"
                  value={formData.complexity_level}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  disabled={isSaving}
                >
                  <option value="basic">Basic</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Estimated Duration (hours)
                </label>
                <input
                  type="number"
                  name="estimated_duration_hours"
                  value={formData.estimated_duration_hours}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  min="1"
                  step="0.5"
                  disabled={isSaving}
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                rows={3}
                disabled={isSaving}
              />
            </div>
            
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="is_active"
                name="is_active"
                checked={formData.is_active}
                onChange={handleCheckboxChange}
                className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
                disabled={isSaving}
              />
              <label htmlFor="is_active" className="text-sm text-gray-700">Active</label>
            </div>

            {/* Task Requirements Section */}
            <div className="border-t border-gray-200 pt-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-800">Task Requirements</h3>
                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={() => addRequirement('material')}
                    className="flex items-center gap-1 px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg text-sm transition-colors"
                    disabled={isSaving}
                  >
                    <Plus className="w-3 h-3" />
                    Material
                  </button>
                  <button
                    type="button"
                    onClick={() => addRequirement('labor')}
                    className="flex items-center gap-1 px-3 py-1 bg-orange-100 hover:bg-orange-200 text-orange-700 rounded-lg text-sm transition-colors"
                    disabled={isSaving}
                  >
                    <Plus className="w-3 h-3" />
                    Labor
                  </button>
                  <button
                    type="button"
                    onClick={() => addRequirement('tool')}
                    className="flex items-center gap-1 px-3 py-1 bg-purple-100 hover:bg-purple-200 text-purple-700 rounded-lg text-sm transition-colors"
                    disabled={isSaving}
                  >
                    <Plus className="w-3 h-3" />
                    Tool
                  </button>
                </div>
              </div>
              
              {/* Requirements List */}
              <div className="space-y-4">
                {requirements.length === 0 ? (
                  <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                    <p>No requirements added yet. Add materials and labor to create a complete installation recipe.</p>
                  </div>
                ) : (
                  requirements.map((req, index) => (
                    <div 
                      key={index} 
                      className={`border rounded-lg p-4 ${
                        req.type === 'material' 
                          ? 'border-blue-200 bg-blue-50' 
                          : req.type === 'labor'
                          ? 'border-orange-200 bg-orange-50'
                          : 'border-purple-200 bg-purple-50'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium text-gray-800">
                              {req.type.charAt(0).toUpperCase() + req.type.slice(1)} Requirement
                            </h4>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              req.type === 'material' 
                                ? 'bg-blue-100 text-blue-700' 
                                : req.type === 'labor'
                                ? 'bg-orange-100 text-orange-700'
                                : 'bg-purple-100 text-purple-700'
                            }`}>
                              {req.type}
                            </span>
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={() => removeRequirement(index)}
                          className="p-1 hover:bg-red-100 rounded transition-colors"
                          disabled={isSaving}
                        >
                          <Trash2 className="w-4 h-4 text-red-500" />
                        </button>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {req.type === 'material' && (
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Material
                            </label>
                            <select
                              value={req.componentId || ''}
                              onChange={(e) => updateRequirement(index, { componentId: e.target.value })}
                              className="w-full px-2 py-1 text-sm border border-gray-200 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              disabled={isSaving}
                            >
                              <option value="">Select material...</option>
                              {components.map((component) => (
                                <option key={component.id} value={component.id}>
                                  {component.name} (₹{component.unit_price}/{component.unit})
                                </option>
                              ))}
                            </select>
                          </div>
                        )}
                        
                        {req.type === 'labor' && (
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              Labor Type
                            </label>
                            <select
                              value={req.laborId || ''}
                              onChange={(e) => updateRequirement(index, { laborId: e.target.value })}
                              className="w-full px-2 py-1 text-sm border border-gray-200 rounded focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                              disabled={isSaving}
                            >
                              <option value="">Select labor...</option>
                              {laborRates.map((labor) => (
                                <option key={labor.id} value={labor.id}>
                                  {labor.name} ({labor.skill_level})
                                </option>
                              ))}
                            </select>
                          </div>
                        )}
                        
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Quantity per sqm
                          </label>
                          <input
                            type="number"
                            value={req.quantityPerSqm}
                            onChange={(e) => updateRequirement(index, { quantityPerSqm: Number(e.target.value) })}
                            className="w-full px-2 py-1 text-sm border border-gray-200 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            step="0.01"
                            min="0"
                            disabled={isSaving}
                          />
                        </div>
                        
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id={`optional-${index}`}
                            checked={req.isOptional}
                            onChange={(e) => updateRequirement(index, { isOptional: e.target.checked })}
                            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            disabled={isSaving}
                          />
                          <label htmlFor={`optional-${index}`} className="ml-2 text-sm text-gray-700">
                            Optional
                          </label>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
              
              {/* Engineering Note */}
              <div className="mt-4 bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="flex items-start gap-2">
                  <Info className="w-5 h-5 text-gray-500 mt-0.5" />
                  <div>
                    <h5 className="font-medium text-gray-700">Engineering Note</h5>
                    <p className="text-sm text-gray-600 mt-1">
                      Task requirements define the "recipe" for installation. For example, tile installation requires 
                      the tiles themselves (material), adhesive (material), and skilled labor. The quantity per sqm 
                      defines how much of each component is needed per square meter of installation.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              disabled={isSaving}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSaving}
              className="flex items-center gap-2 px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors disabled:bg-green-400"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  {task ? 'Update Task' : 'Create Task'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}