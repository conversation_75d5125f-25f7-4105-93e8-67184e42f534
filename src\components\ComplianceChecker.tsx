import React, { useState } from 'react';
import { CheckCir<PERSON>, AlertTriangle, Clock, FileText, ExternalLink } from 'lucide-react';
import { UserInputs } from '../types/calculator';

interface ComplianceItem {
  id: string;
  name: string;
  status: 'required' | 'recommended' | 'optional';
  applicable: boolean;
  timelineWeeks: number;
  cost: number;
  authority: string;
  description: string;
  documents: string[];
}

interface ComplianceCheckerProps {
  inputs: UserInputs;
  builtUpArea: number;
}

export function ComplianceChecker({ inputs, builtUpArea }: ComplianceCheckerProps) {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());

  const plotAreaSqm = inputs.plotSize / 10.764; // Convert to sq meters

  const complianceItems: ComplianceItem[] = [
    {
      id: 'building_plan',
      name: 'Building Plan Approval',
      status: 'required',
      applicable: true,
      timelineWeeks: 8,
      cost: plotAreaSqm * 450,
      authority: 'DTCP Haryana',
      description: 'Mandatory approval for all construction in Gurgaon',
      documents: ['Site plan', 'Architectural drawings', 'Structural drawings', 'NOC from society']
    },
    {
      id: 'fire_noc',
      name: 'Fire NOC',
      status: inputs.numberOfFloors >= 3 ? 'required' : 'optional',
      applicable: inputs.numberOfFloors >= 3,
      timelineWeeks: 6,
      cost: builtUpArea * 8,
      authority: 'Haryana Fire Services',
      description: 'Required for buildings G+2 and above',
      documents: ['Fire safety plan', 'Evacuation plan', 'Fire fighting system layout']
    },
    {
      id: 'environment_clearance',
      name: 'Environment Clearance',
      status: plotAreaSqm > 2000 ? 'required' : 'optional',
      applicable: plotAreaSqm > 2000,
      timelineWeeks: 12,
      cost: 50000,
      authority: 'HSPCB',
      description: 'Required for plots larger than 2000 sqm',
      documents: ['Environmental impact assessment', 'Waste management plan']
    },
    {
      id: 'rainwater_harvesting',
      name: 'Rainwater Harvesting',
      status: plotAreaSqm > 300 ? 'required' : 'recommended',
      applicable: true,
      timelineWeeks: 2,
      cost: Math.max(25000, plotAreaSqm * 50),
      authority: 'CGWA',
      description: 'Mandatory for plots above 300 sqm, recommended for all',
      documents: ['RWH system design', 'Maintenance plan']
    },
    {
      id: 'sewage_treatment',
      name: 'Sewage Treatment Plant',
      status: plotAreaSqm > 500 ? 'required' : 'recommended',
      applicable: plotAreaSqm > 300,
      timelineWeeks: 4,
      cost: builtUpArea * 45,
      authority: 'HSPCB',
      description: 'Required for larger plots, recommended for environmental compliance',
      documents: ['STP design', 'Effluent treatment plan', 'Disposal method']
    },
    {
      id: 'structural_stability',
      name: 'Structural Stability Certificate',
      status: inputs.numberOfFloors >= 4 ? 'required' : 'recommended',
      applicable: inputs.numberOfFloors >= 3,
      timelineWeeks: 3,
      cost: 15000,
      authority: 'Licensed Structural Engineer',
      description: 'Structural safety certification',
      documents: ['Structural calculations', 'Soil test report', 'Foundation design']
    }
  ];

  const toggleItem = (itemId: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    setSelectedItems(newSelected);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'required':
        return 'text-red-700 bg-red-100 border-red-300';
      case 'recommended':
        return 'text-yellow-700 bg-yellow-100 border-yellow-300';
      default:
        return 'text-gray-700 bg-gray-100 border-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'required':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'recommended':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      default:
        return <CheckCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  const totalCost = complianceItems
    .filter(item => selectedItems.has(item.id))
    .reduce((sum, item) => sum + item.cost, 0);

  const totalTimeline = Math.max(
    ...complianceItems
      .filter(item => selectedItems.has(item.id))
      .map(item => item.timelineWeeks)
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-6 border border-gray-200">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-semibold text-gray-800">Regulatory Compliance Checker</h3>
          <p className="text-gray-600 text-sm mt-1">Gurgaon-specific approvals and clearances</p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-blue-600">{formatCurrency(totalCost)}</div>
          <div className="text-sm text-gray-600">Total Compliance Cost</div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Compliance Items */}
        <div className="space-y-4">
          {complianceItems.map((item) => (
            <div
              key={item.id}
              className={`border-2 rounded-lg p-4 transition-all cursor-pointer ${
                selectedItems.has(item.id) ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
              } ${!item.applicable ? 'opacity-50' : ''}`}
              onClick={() => item.applicable && toggleItem(item.id)}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-start gap-3">
                  <input
                    type="checkbox"
                    checked={selectedItems.has(item.id)}
                    onChange={() => item.applicable && toggleItem(item.id)}
                    disabled={!item.applicable}
                    className="mt-1 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div>
                    <h4 className="font-semibold text-gray-800">{item.name}</h4>
                    <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(item.status)}
                  <span className={`text-xs px-2 py-1 rounded-full border-2 font-medium ${getStatusColor(item.status)}`}>
                    {item.status}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Cost:</span>
                  <div className="font-semibold text-gray-800">{formatCurrency(item.cost)}</div>
                </div>
                <div>
                  <span className="text-gray-600">Timeline:</span>
                  <div className="font-semibold text-gray-800">{item.timelineWeeks} weeks</div>
                </div>
                <div>
                  <span className="text-gray-600">Authority:</span>
                  <div className="font-semibold text-gray-800 text-xs">{item.authority}</div>
                </div>
              </div>

              {item.documents.length > 0 && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <span className="text-xs text-gray-600">Required Documents:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {item.documents.map((doc, index) => (
                      <span key={index} className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded border">
                        {doc}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Summary & Timeline */}
        <div className="space-y-6">
          {/* Cost Summary */}
          <div className="bg-blue-50 rounded-lg p-4 border-2 border-blue-200">
            <h4 className="font-semibold text-blue-800 mb-3">Compliance Summary</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-blue-700">Selected Items:</span>
                <span className="font-semibold text-blue-800">{selectedItems.size}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-700">Total Cost:</span>
                <span className="font-semibold text-blue-800">{formatCurrency(totalCost)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-700">Max Timeline:</span>
                <span className="font-semibold text-blue-800">{totalTimeline} weeks</span>
              </div>
            </div>
          </div>

          {/* Critical Path */}
          <div className="bg-yellow-50 rounded-lg p-4 border-2 border-yellow-200">
            <h4 className="font-semibold text-yellow-800 mb-3 flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Critical Timeline
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-gray-700">Building Plan Approval: 8 weeks (Start immediately)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span className="text-gray-700">Fire NOC: 6 weeks (Can run parallel)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-gray-700">Other clearances: 2-4 weeks each</span>
              </div>
            </div>
          </div>

          {/* Helpful Links */}
          <div className="bg-gray-50 rounded-lg p-4 border-2 border-gray-200">
            <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Useful Resources
            </h4>
            <div className="space-y-2 text-sm">
              <a href="#" className="flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors">
                <ExternalLink className="w-3 h-3" />
                DTCP Haryana Online Portal
              </a>
              <a href="#" className="flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors">
                <ExternalLink className="w-3 h-3" />
                Fire NOC Application Form
              </a>
              <a href="#" className="flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors">
                <ExternalLink className="w-3 h-3" />
                Environmental Clearance Guidelines
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}