import React, { useState, useEffect } from 'react';
import { Brain, AlertTriangle, CheckCircle, Lightbulb, Star, Info, TrendingUp, Eye, Target } from 'lucide-react';
import { CalculationResult, RoomMaterialSelection, CostItem, QualityTier } from '../types/calculator';
import { architectNotes } from '../data/architectNotes';

interface ExpertInsight {
  id: string;
  type: 'warning' | 'suggestion' | 'optimization' | 'recommendation' | 'architect_note';
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
  actionable: boolean;
  context: 'item' | 'section' | 'global';
  itemId?: string;
  sectionId?: string;
  highlightItems?: string[]; // Items to highlight when action is taken
}

interface UnifiedExpertGuidanceProps {
  result: CalculationResult;
  roomSelections: RoomMaterialSelection[];
  currentItem?: CostItem;
  context: 'inline' | 'summary';
  itemId?: string;
  sectionId?: string;
  onHighlightItems?: (itemIds: string[]) => void;
}

export function UnifiedExpertGuidance({ 
  result, 
  roomSelections, 
  currentItem, 
  context, 
  itemId, 
  sectionId,
  onHighlightItems
}: UnifiedExpertGuidanceProps) {
  const [insights, setInsights] = useState<ExpertInsight[]>([]);
  const [highlightedInsightId, setHighlightedInsightId] = useState<string | null>(null);

  useEffect(() => {
    generateUnifiedInsights();
  }, [result, roomSelections, currentItem, itemId, sectionId]);

  const generateUnifiedInsights = () => {
    const newInsights: ExpertInsight[] = [];

    if (context === 'inline' && currentItem) {
      // Generate item-specific insights
      generateItemSpecificInsights(newInsights, currentItem);
    } else if (context === 'summary') {
      // Generate global insights
      generateGlobalInsights(newInsights);
      generateQualityConsistencyInsights(newInsights);
      generateRoomMaterialInsights(newInsights);
      generateCostOptimizationInsights(newInsights);
    }

    setInsights(newInsights);
  };

  const generateItemSpecificInsights = (insights: ExpertInsight[], item: CostItem) => {
    // Material-specific architect notes
    if (item.selectedMaterial && architectNotes[item.id]) {
      const note = architectNotes[item.id];
      insights.push({
        id: `architect_note_${item.id}`,
        type: 'architect_note',
        title: note.title,
        message: note.content,
        severity: 'medium',
        actionable: true,
        context: 'item',
        itemId: item.id
      });
    }

    // Material selection recommendations
    if (item.materialOptions && item.selectedMaterial) {
      if (item.selectedMaterial.includes('AAC') && result.qualityTier === 'good') {
        insights.push({
          id: `material_upgrade_${item.id}`,
          type: 'suggestion',
          title: 'Consider Material Upgrade',
          message: 'AAC blocks provide excellent thermal insulation and can reduce your AC costs by 20-30%. The higher initial cost pays for itself through energy savings.',
          severity: 'medium',
          actionable: true,
          context: 'item',
          itemId: item.id
        });
      }

      if (item.selectedMaterial.includes('UPVC') && result.location === 'gurgaon') {
        insights.push({
          id: `upvc_recommendation_${item.id}`,
          type: 'recommendation',
          title: 'Excellent Choice for Gurgaon Climate',
          message: 'UPVC windows are ideal for Gurgaon\'s extreme temperatures. They provide superior insulation and can reduce energy costs by 15-25%.',
          severity: 'low',
          actionable: false,
          context: 'item',
          itemId: item.id
        });
      }
    }

    // Quality tier mismatches
    if (item.isRecommended && result.qualityTier !== 'better') {
      insights.push({
        id: `quality_mismatch_${item.id}`,
        type: 'suggestion',
        title: 'Recommended Item for Better Value',
        message: item.recommendationNote || 'This item is recommended by our architects for optimal value and performance.',
        severity: 'medium',
        actionable: true,
        context: 'item',
        itemId: item.id
      });
    }
  };

  const generateGlobalInsights = (insights: ExpertInsight[]) => {
    const totalCost = result.totalCost;
    const structureCost = result.sections.find(s => s.id === 'foundation')?.subtotal || 0;
    const finishesCost = result.sections.find(s => s.id === 'masonry')?.subtotal || 0;

    // Cost distribution analysis
    const structurePercentage = (structureCost / totalCost) * 100;
    const finishesPercentage = (finishesCost / totalCost) * 100;

    if (structurePercentage < 35) {
      insights.push({
        id: 'low_structure_cost',
        type: 'warning',
        title: 'Low Structural Investment Detected',
        message: `Your structural costs are only ${structurePercentage.toFixed(1)}% of total budget. Consider investing more in foundation and structure for long-term durability.`,
        severity: 'high',
        actionable: true,
        context: 'global',
        highlightItems: ['steel_work', 'concrete_work', 'excavation']
      });
    }

    if (finishesPercentage > 45) {
      insights.push({
        id: 'high_finishes_cost',
        type: 'optimization',
        title: 'High Finishing Costs Detected',
        message: `Your finishing costs are ${finishesPercentage.toFixed(1)}% of total budget. Consider optimizing material selections in non-critical areas to balance your budget better.`,
        severity: 'medium',
        actionable: true,
        context: 'global',
        highlightItems: ['flooring', 'interior_painting', 'customized_flooring', 'customized_painting']
      });
    }
  };

  const generateQualityConsistencyInsights = (insights: ExpertInsight[]) => {
    const qualityTier = result.qualityTier;
    
    // Check for inconsistent material selections across sections
    const inconsistentItems = result.sections.flatMap(section => 
      section.items.filter(item => {
        if (item.selectedMaterial) {
          const isHighEnd = item.selectedMaterial.includes('Premium') || 
                           item.selectedMaterial.includes('Luxury') || 
                           item.selectedMaterial.includes('Jaquar') ||
                           item.selectedMaterial.includes('Schneider');
          const isBasic = item.selectedMaterial.includes('Basic') || 
                         item.selectedMaterial.includes('Local') ||
                         item.selectedMaterial.includes('Economy');
          
          if (qualityTier === 'good' && isHighEnd) return true;
          if (qualityTier === 'best' && isBasic) return true;
        }
        return false;
      })
    );

    if (inconsistentItems.length > 0) {
      insights.push({
        id: 'quality_consistency',
        type: 'warning',
        title: 'Quality Tier Inconsistency Detected',
        message: `You've selected ${qualityTier} quality tier but chosen ${inconsistentItems.length} items that don't match this standard. Consider aligning all selections for a cohesive project quality.`,
        severity: 'medium',
        actionable: true,
        context: 'global',
        highlightItems: inconsistentItems.map(item => item.id)
      });
    }
  };

  const generateRoomMaterialInsights = (insights: ExpertInsight[]) => {
    if (roomSelections.length === 0) return;

    // Group rooms by type for analysis
    const bathroomSelections = roomSelections.filter(room => room.roomType === 'Bathroom');
    const bedroomSelections = roomSelections.filter(room => room.roomType === 'Bedroom');

    // Check for bathroom material mismatches
    bathroomSelections.forEach(bathroom => {
      const isExpensiveFlooring = bathroom.flooringMaterial.includes('Italian Marble') || 
                                 bathroom.flooringMaterial.includes('Granite');
      const isCheapPaint = bathroom.paintMaterial.includes('Economy');

      if (isExpensiveFlooring && isCheapPaint) {
        insights.push({
          id: `bathroom_mismatch_${bathroom.roomId}`,
          type: 'suggestion',
          title: 'Bathroom Finish Mismatch',
          message: `In your bathroom, you've chosen premium ${bathroom.flooringMaterial} flooring but ${bathroom.paintMaterial} paint. Consider upgrading to moisture-resistant premium paint for better durability and aesthetics.`,
          severity: 'medium',
          actionable: true,
          context: 'global',
          highlightItems: ['customized_flooring', 'customized_painting']
        });
      }
    });

    // Check for bedroom consistency
    if (bedroomSelections.length > 1) {
      const flooringTypes = [...new Set(bedroomSelections.map(room => room.flooringMaterial))];
      if (flooringTypes.length > 2) {
        insights.push({
          id: 'bedroom_flooring_variety',
          type: 'suggestion',
          title: 'Too Many Flooring Types in Bedrooms',
          message: `You've selected ${flooringTypes.length} different flooring materials for bedrooms. For design consistency and cost efficiency, consider using 1-2 flooring types across all bedrooms.`,
          severity: 'low',
          actionable: true,
          context: 'global',
          highlightItems: ['customized_flooring']
        });
      }
    }
  };

  const generateCostOptimizationInsights = (insights: ExpertInsight[]) => {
    // Check for potential savings in electrical points
    const electricalItem = result.sections
      .flatMap(s => s.items)
      .find(item => item.id === 'electrical');
    
    if (electricalItem && electricalItem.quantity > result.quantities.totalBuiltUpArea * 0.1) {
      insights.push({
        id: 'excessive_electrical_points',
        type: 'optimization',
        title: 'Electrical Points Optimization',
        message: `You have ${electricalItem.quantity} electrical points for ${result.quantities.totalBuiltUpArea} sq ft. Consider if all points are necessary - reducing by 10-15% could save ₹${((electricalItem.quantity * 0.15 * electricalItem.rate)).toLocaleString()}.`,
        severity: 'low',
        actionable: true,
        context: 'global',
        highlightItems: ['electrical']
      });
    }
  };

  const handleActionableClick = (insight: ExpertInsight) => {
    if (insight.actionable && insight.highlightItems && onHighlightItems) {
      setHighlightedInsightId(insight.id);
      onHighlightItems(insight.highlightItems);
      
      // Remove highlight after 5 seconds
      setTimeout(() => {
        setHighlightedInsightId(null);
        onHighlightItems([]);
      }, 5000);
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-orange-500" />;
      case 'suggestion':
        return <Lightbulb className="w-4 h-4 text-blue-500" />;
      case 'optimization':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'recommendation':
        return <Star className="w-4 h-4 text-purple-500" />;
      case 'architect_note':
        return <Info className="w-4 h-4 text-indigo-500" />;
      default:
        return <Brain className="w-4 h-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'border-red-200 bg-red-50';
      case 'medium':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-blue-200 bg-blue-50';
    }
  };

  // Filter insights based on context
  const contextualInsights = insights.filter(insight => {
    if (context === 'inline') {
      return insight.context === 'item' && insight.itemId === itemId;
    }
    return insight.context === 'global';
  });

  if (contextualInsights.length === 0) {
    if (context === 'summary') {
      return (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-3">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <div>
              <h3 className="font-semibold text-green-800">Excellent Configuration!</h3>
              <p className="text-green-600 text-sm">Your material selections and project configuration are well-balanced and optimized.</p>
            </div>
          </div>
        </div>
      );
    }
    return null;
  }

  if (context === 'inline') {
    // Inline display for single item
    return (
      <div className="mt-2 space-y-2">
        {contextualInsights.map((insight) => (
          <div
            key={insight.id}
            className={`border rounded-lg p-3 ${getSeverityColor(insight.severity)}`}
          >
            <div className="flex items-start gap-2">
              {getInsightIcon(insight.type)}
              <div className="flex-1">
                <h5 className="font-medium text-gray-800 text-sm">{insight.title}</h5>
                <p className="text-gray-700 text-xs leading-relaxed mt-1">{insight.message}</p>
                {insight.actionable && (
                  <button 
                    onClick={() => handleActionableClick(insight)}
                    className="mt-2 flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 font-medium transition-colors"
                  >
                    <Target className="w-3 h-3" />
                    View Recommendations
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Summary display for global insights
  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
      <div className="flex items-center gap-3 mb-4">
        <Brain className="w-6 h-6 text-purple-600" />
        <div>
          <h3 className="text-xl font-semibold text-gray-800">AI Architect's Analysis</h3>
          <p className="text-gray-600 text-sm">Smart insights to optimize your project</p>
        </div>
      </div>

      <div className="space-y-4">
        {contextualInsights.map((insight) => (
          <div
            key={insight.id}
            className={`border rounded-lg p-4 transition-all duration-300 ${getSeverityColor(insight.severity)} ${
              highlightedInsightId === insight.id ? 'ring-2 ring-blue-400 shadow-lg' : ''
            }`}
          >
            <div className="flex items-start gap-3">
              {getInsightIcon(insight.type)}
              <div className="flex-1">
                <h4 className="font-semibold text-gray-800 mb-1">{insight.title}</h4>
                <p className="text-gray-700 text-sm leading-relaxed">{insight.message}</p>
                {insight.actionable && (
                  <button 
                    onClick={() => handleActionableClick(insight)}
                    className={`mt-2 flex items-center gap-1 text-xs font-medium transition-all duration-200 ${
                      highlightedInsightId === insight.id 
                        ? 'text-blue-800 bg-blue-100 px-2 py-1 rounded' 
                        : 'text-blue-600 hover:text-blue-800'
                    }`}
                  >
                    <Eye className="w-3 h-3" />
                    {highlightedInsightId === insight.id ? 'Highlighting Problem Areas...' : 'Highlight Problem Areas →'}
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}