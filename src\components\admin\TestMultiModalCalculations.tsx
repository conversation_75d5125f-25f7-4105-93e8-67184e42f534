import React, { useState } from 'react';
import { Play, CheckCircle, AlertCircle, Calculator, TrendingUp } from 'lucide-react';
import { 
  calculateLaborCost, 
  calculateProjectLaborCosts,
  selectOptimalChargingModel,
  EnhancedLaborRate,
  ProjectContext 
} from '../../lib/multiModalLaborCalculations';

export function TestMultiModalCalculations() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const sampleEnhancedRate: EnhancedLaborRate = {
    id: 'test-1',
    name: 'Test Electrical Wiring',
    category: 'electrical',
    skill_level: 'skilled',
    unit: 'point',
    rates: { good: 150, better: 180, best: 220 },
    productivity: { output_per_day: 20, unit: 'points' },
    location: 'gurgaon',
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    charging_models: {
      primary_model: 'per_unit',
      lump_sum_rates: {
        small_project: { good: 75000, better: 90000, best: 110000 },
        medium_project: { good: 140000, better: 170000, best: 210000 },
        large_project: { good: 280000, better: 340000, best: 420000 }
      },
      daily_rates: { good: 800, better: 1000, best: 1200 },
      per_sqft_rates: { good: 18, better: 22, best: 28 },
      recommended_for: {
        project_sizes: ['small', 'medium', 'large'],
        project_types: ['residential', 'commercial'],
        notes: 'Test electrical work with multiple pricing models'
      }
    }
  };

  const testScenarios = [
    {
      name: 'Small Residential Project',
      context: {
        total_area: 800,
        project_type: 'residential' as const,
        duration_months: 2,
        quality_tier: 'better' as const,
        budget_preference: 'cost_effective' as const
      },
      quantity: 15
    },
    {
      name: 'Medium Residential Project',
      context: {
        total_area: 1500,
        project_type: 'residential' as const,
        duration_months: 4,
        quality_tier: 'better' as const,
        budget_preference: 'balanced' as const
      },
      quantity: 25
    },
    {
      name: 'Large Commercial Project',
      context: {
        total_area: 3000,
        project_type: 'commercial' as const,
        duration_months: 8,
        quality_tier: 'best' as const,
        budget_preference: 'premium' as const
      },
      quantity: 50
    },
    {
      name: 'Cost-Effective Large Project',
      context: {
        total_area: 2500,
        project_type: 'residential' as const,
        duration_months: 6,
        quality_tier: 'good' as const,
        budget_preference: 'cost_effective' as const
      },
      quantity: 40
    }
  ];

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    const results = [];

    for (const scenario of testScenarios) {
      try {
        // Test optimal model selection
        const optimalModel = selectOptimalChargingModel(sampleEnhancedRate, scenario.context);
        
        // Test cost calculation
        const costResult = calculateLaborCost(sampleEnhancedRate, scenario.quantity, scenario.context);
        
        // Test project-level calculation
        const projectResult = calculateProjectLaborCosts(
          [{ labor_rate: sampleEnhancedRate, quantity: scenario.quantity }],
          scenario.context
        );

        results.push({
          scenario: scenario.name,
          success: true,
          optimalModel,
          costResult,
          projectResult,
          insights: {
            primaryCost: costResult.primary_cost,
            modelUsed: costResult.model_used,
            alternativesCount: costResult.alternatives.length,
            potentialSavings: costResult.alternatives.find(alt => alt.savings > 0)?.savings || 0,
            efficiencyRating: costResult.efficiency_rating,
            timeline: costResult.breakdown.timeline_days
          }
        });

      } catch (error) {
        results.push({
          scenario: scenario.name,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          insights: null
        });
      }
    }

    setTestResults(results);
    setIsRunning(false);
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <AlertCircle className="w-5 h-5 text-red-500" />
    );
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`;
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Multi-Modal Labor Calculation Tests</h2>
              <p className="text-sm text-gray-600 mt-1">
                Comprehensive testing of enhanced labor rate calculations across different scenarios
              </p>
            </div>
            <button
              onClick={runTests}
              disabled={isRunning}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg flex items-center space-x-2"
            >
              <Play className="w-4 h-4" />
              <span>{isRunning ? 'Running Tests...' : 'Run Tests'}</span>
            </button>
          </div>
        </div>

        {/* Test Configuration */}
        <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
          <h3 className="font-medium text-gray-900 mb-3">Test Configuration</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Labor Type:</span> {sampleEnhancedRate.name}
            </div>
            <div>
              <span className="font-medium">Category:</span> {sampleEnhancedRate.category}
            </div>
            <div>
              <span className="font-medium">Available Models:</span> Per Unit, Lump Sum, Daily Rate, Per Sqft
            </div>
            <div>
              <span className="font-medium">Test Scenarios:</span> {testScenarios.length} scenarios
            </div>
          </div>
        </div>

        {/* Test Results */}
        <div className="px-6 py-4">
          {testResults.length === 0 && !isRunning && (
            <div className="text-center py-8 text-gray-500">
              Click "Run Tests" to start comprehensive testing of multi-modal calculations
            </div>
          )}

          {isRunning && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Running calculation tests...</p>
            </div>
          )}

          {testResults.length > 0 && (
            <div className="space-y-6">
              {testResults.map((result, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(result.success)}
                      <h4 className="font-medium text-gray-900">{result.scenario}</h4>
                    </div>
                    {result.success && result.insights && (
                      <div className="text-right">
                        <div className="text-lg font-semibold text-gray-900">
                          {formatCurrency(result.insights.primaryCost)}
                        </div>
                        <div className="text-sm text-gray-600">
                          {result.insights.modelUsed.replace('_', ' ')}
                        </div>
                      </div>
                    )}
                  </div>

                  {result.success && result.insights ? (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="bg-blue-50 p-3 rounded">
                        <div className="text-sm text-blue-600 font-medium">Model Selected</div>
                        <div className="text-blue-800 capitalize">
                          {result.optimalModel.primary.replace('_', ' ')}
                        </div>
                        <div className="text-xs text-blue-600 mt-1">
                          {result.optimalModel.reasoning}
                        </div>
                      </div>

                      <div className="bg-green-50 p-3 rounded">
                        <div className="text-sm text-green-600 font-medium">Efficiency</div>
                        <div className="text-green-800 capitalize">
                          {result.insights.efficiencyRating}
                        </div>
                        <div className="text-xs text-green-600 mt-1">
                          {result.insights.alternativesCount} alternatives
                        </div>
                      </div>

                      <div className="bg-yellow-50 p-3 rounded">
                        <div className="text-sm text-yellow-600 font-medium">Timeline</div>
                        <div className="text-yellow-800">
                          {Math.ceil(result.insights.timeline)} days
                        </div>
                        <div className="text-xs text-yellow-600 mt-1">
                          Estimated duration
                        </div>
                      </div>

                      <div className="bg-purple-50 p-3 rounded">
                        <div className="text-sm text-purple-600 font-medium">Potential Savings</div>
                        <div className="text-purple-800">
                          {result.insights.potentialSavings > 0 
                            ? formatCurrency(result.insights.potentialSavings)
                            : 'Optimal'
                          }
                        </div>
                        <div className="text-xs text-purple-600 mt-1">
                          {result.insights.potentialSavings > 0 ? 'Available' : 'Current best'}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-red-50 p-3 rounded">
                      <div className="text-sm text-red-600 font-medium">Error</div>
                      <div className="text-red-800">{result.error}</div>
                    </div>
                  )}

                  {result.success && result.costResult && result.costResult.alternatives.length > 0 && (
                    <div className="mt-4">
                      <h5 className="font-medium text-gray-900 mb-2">Alternative Pricing Models</h5>
                      <div className="space-y-2">
                        {result.costResult.alternatives.slice(0, 3).map((alt: any, altIndex: number) => (
                          <div key={altIndex} className="flex justify-between items-center text-sm bg-gray-50 p-2 rounded">
                            <span className="capitalize">{alt.model.replace('_', ' ')}</span>
                            <div className="flex items-center space-x-2">
                              <span>{formatCurrency(alt.cost)}</span>
                              <div className={`flex items-center ${
                                alt.savings > 0 ? 'text-green-600' : 'text-red-600'
                              }`}>
                                <TrendingUp className={`w-3 h-3 mr-1 ${
                                  alt.savings > 0 ? 'rotate-180' : ''
                                }`} />
                                <span>{Math.abs(alt.savings_percentage).toFixed(1)}%</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {result.success && result.costResult && result.costResult.recommendations.length > 0 && (
                    <div className="mt-4">
                      <h5 className="font-medium text-gray-900 mb-2">Recommendations</h5>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {result.costResult.recommendations.map((rec: string, recIndex: number) => (
                          <li key={recIndex} className="flex items-start">
                            <span className="text-blue-500 mr-2">•</span>
                            {rec}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}

              {/* Summary */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-3">Test Summary</h4>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Total Tests:</span> {testResults.length}
                  </div>
                  <div>
                    <span className="font-medium">Passed:</span> {testResults.filter(r => r.success).length}
                  </div>
                  <div>
                    <span className="font-medium">Failed:</span> {testResults.filter(r => !r.success).length}
                  </div>
                </div>
                
                {testResults.every(r => r.success) && (
                  <div className="mt-3 flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    <span className="text-sm font-medium">All tests passed! Multi-modal calculations are working correctly.</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
