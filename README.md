# NirmaanAI Construction Cost Calculator

NirmaanAI is a professional construction cost calculator that provides accurate, transparent estimates for building projects in the Delhi/NCR region. The application uses a pluggable component architecture with Supabase as the backend database, enabling a dynamic, user-centric experience with room-by-room customization and detailed cost breakdowns.

## Features

- **Interactive Floor Plan**: Visualize and customize individual rooms
- **Visual Room Configurator**: Select materials and finishes for each room
- **Facade Design Module**: Customize exterior finishes
- **Item Override System**: Transparent price adjustments with reason tracking
- **Admin Dashboard**: Manage components, tasks, and default configurations
- **Professional Quote Generation**: Create client-ready PDF quotes
- **Compliance Checker**: Verify regulatory requirements for your project
- **Material Price Tracker**: Real-time material price updates
- **Project Sharing**: Collaborate with clients and contractors
- **Engineering Standards**: Technical parameters based on IS codes
- **Regional Data**: Location-specific cost multipliers

## Technology Stack

- **Frontend**: React, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage
- **Deployment**: Netlify/Vercel

## Getting Started

### Prerequisites

- Node.js 16+
- npm or yarn
- Supabase account

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/nirmaanai.git
   cd nirmaanai
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Set up environment variables:
   Create a `.env` file in the root directory with the following variables:
   ```
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. Start the development server:
   ```
   npm run dev
   ```

## Project Structure

```
/src
  /components           # UI components
    /admin              # Admin panel components
    /room               # Room customization components
    /facade             # Facade design components
  /data                 # Static data and constants
  /lib                  # Library code and API integrations
  /types                # TypeScript type definitions
  /utils                # Utility functions and calculation engines
  App.tsx               # Main application component
  main.tsx              # Application entry point
/supabase
  /migrations           # Database migration files
```

## Key Components

### Calculation Engine

The V2 calculation engine (`v2CalculationEngine.ts`) is the core of the application. It:
- Fetches components, tasks, labor rates, and engineering standards from Supabase
- Performs engineering-grade geometric analysis
- Calculates costs based on user inputs and customizations
- Applies user overrides and adjustments
- Generates detailed breakdowns for transparency

### Admin Panel

The admin panel (`AdminDashboard.tsx`) allows non-technical users to:
- Manage construction components and materials
- Create and edit installation tasks and recipes
- Configure default UI selections
- Set up labor rates for different skill levels
- Manage engineering standards and regional data
- Configure system-wide settings

### Room Customization

The Visual Room Configurator (`VisualRoomConfigurator.tsx`) enables users to:
- Select flooring and wall finishes for each room
- Configure electrical and plumbing points
- Choose bathroom fixture bundles
- View real-time cost updates
- See detailed specifications for each material

### Facade Design

The Facade Design Module (`FacadeDesignModule.tsx`) allows users to:
- Apply different materials to building exterior
- Customize different sections (ground floor, upper floors, accent)
- View real-time visualization and cost updates

## Database Schema

The application uses a comprehensive Supabase database schema with the following key tables:

- `components`: Materials and products with pricing
- `tasks`: Installation recipes
- `task_requirements`: Junction table defining recipes
- `labor_rates`: Tiered pricing for different skill levels
- `admin_users`: Access control for admin panel
- `ui_defaults`: JSON configuration for default selections
- `user_projects`: Store user project configurations
- `user_overrides`: Track manual overrides with transparency
- `engineering_standards`: Technical parameters and assumptions
- `concrete_mixes`: Concrete mix designs for different grades
- `steel_reinforcement`: Steel reinforcement ratios
- `material_consumption`: Material consumption ratios
- `wastage_factors`: Material wastage percentages
- `location_multipliers`: Regional cost multipliers
- `professional_fees`: Professional service fee structures
- `regulatory_fees`: Government and regulatory fees
- `system_config`: Global system configuration parameters

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Supabase](https://supabase.io/) for the backend infrastructure
- [React](https://reactjs.org/) for the frontend framework
- [Tailwind CSS](https://tailwindcss.com/) for styling
- [Lucide React](https://lucide.dev/) for icons