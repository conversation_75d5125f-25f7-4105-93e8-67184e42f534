import React, { useState } from 'react';
import { X, Calculator, Info, TrendingUp, BarChart3, FileText } from 'lucide-react';
import { CalculationBreakdown as CalculationBreakdownType } from '../types/calculator';

interface CalculationBreakdownProps {
  isOpen: boolean;
  onClose: () => void;
  breakdown: CalculationBreakdownType;
  itemName: string;
}

export function CalculationBreakdown({ isOpen, onClose, breakdown, itemName }: CalculationBreakdownProps) {
  if (!isOpen) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number, decimals: number = 2) => {
    return new Intl.NumberFormat('en-IN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(num);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex items-center gap-3">
            <Calculator className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-bold text-gray-800">Engineering Calculation Breakdown</h2>
              <p className="text-gray-600 mt-1">{itemName}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            aria-label="Close calculation breakdown"
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Formula */}
          <div className="bg-blue-50 rounded-lg p-4 mb-6 border-2 border-blue-200">
            <h3 className="font-semibold text-blue-800 mb-2 flex items-center gap-2">
              <Info className="w-5 h-5" />
              Engineering Formula & Methodology
            </h3>
            <p className="text-blue-700 font-mono text-sm leading-relaxed bg-white p-3 rounded border">
              {breakdown.formula}
            </p>
          </div>

          {/* Enhanced Components Breakdown with Granular Columns */}
          <div className="mb-6">
            <h3 className="font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-green-600" />
              Detailed Component Analysis
            </h3>
            <div className="overflow-x-auto">
              <table className="calculation-breakdown-table w-full">
                <thead>
                  <tr>
                    <th className="text-left">Component</th>
                    <th className="text-center">Base Quantity</th>
                    <th className="text-center">Consumption Ratio</th>
                    <th className="text-center">Final Quantity</th>
                    <th className="text-center">Unit Price</th>
                    <th className="text-right">Total Cost</th>
                    <th className="text-left">Engineering Source</th>
                  </tr>
                </thead>
                <tbody>
                  {breakdown.components.map((component, index) => {
                    // Parse the component data to extract base quantity and consumption ratio
                    const baseQuantity = component.quantity;
                    const consumptionRatio = component.rate;
                    const finalQuantity = baseQuantity * consumptionRatio;
                    const unitPrice = component.total / finalQuantity;
                    
                    return (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="font-medium text-gray-800">
                          {component.name}
                        </td>
                        <td className="text-center text-gray-700">
                          {formatNumber(baseQuantity)} {component.unit === 'kg' ? 'm³' : component.unit}
                        </td>
                        <td className="text-center text-gray-700 font-mono">
                          {component.unit === 'kg' ? 
                            `${formatNumber(consumptionRatio)} kg/m³` : 
                            `${formatNumber(consumptionRatio)} ${component.unit}/sqm`
                          }
                        </td>
                        <td className="text-center font-semibold text-blue-700">
                          {formatNumber(finalQuantity)} {component.unit}
                        </td>
                        <td className="text-center text-gray-700">
                          {component.unit === 'kg' || component.unit === 'points' ? 
                            formatCurrency(unitPrice) : 
                            `₹${formatNumber(unitPrice)}`
                          }
                        </td>
                        <td className="text-right font-semibold text-gray-800">
                          {formatCurrency(component.total)}
                        </td>
                        <td className="text-sm text-gray-600">
                          {component.source}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* Cost Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-green-50 rounded-lg p-4 border-2 border-green-200">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="w-5 h-5 text-green-600" />
                <h4 className="font-semibold text-green-800">Material Cost</h4>
              </div>
              <div className="text-2xl font-bold text-green-700">
                {formatCurrency(breakdown.materialCost)}
              </div>
              <div className="text-sm text-green-600 mt-1">
                {breakdown.finalCost > 0 ? ((breakdown.materialCost / breakdown.finalCost) * 100).toFixed(1) : 0}% of total
              </div>
            </div>

            <div className="bg-orange-50 rounded-lg p-4 border-2 border-orange-200">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="w-5 h-5 text-orange-600" />
                <h4 className="font-semibold text-orange-800">Labor Cost</h4>
              </div>
              <div className="text-2xl font-bold text-orange-700">
                {formatCurrency(breakdown.laborCost)}
              </div>
              <div className="text-sm text-orange-600 mt-1">
                {breakdown.finalCost > 0 ? ((breakdown.laborCost / breakdown.finalCost) * 100).toFixed(1) : 0}% of total
              </div>
            </div>

            <div className="bg-blue-50 rounded-lg p-4 border-2 border-blue-200">
              <div className="flex items-center gap-2 mb-2">
                <Calculator className="w-5 h-5 text-blue-600" />
                <h4 className="font-semibold text-blue-800">Final Cost</h4>
              </div>
              <div className="text-2xl font-bold text-blue-700">
                {formatCurrency(breakdown.finalCost)}
              </div>
              <div className="text-sm text-blue-600 mt-1">
                Total calculated cost
              </div>
            </div>
          </div>

          {/* Engineering Notes */}
          {breakdown.notes && breakdown.notes.length > 0 && (
            <div className="bg-yellow-50 rounded-lg p-4 border-2 border-yellow-200 mb-6">
              <h4 className="font-semibold text-yellow-800 mb-3 flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Engineering Notes & Standards
              </h4>
              <ul className="space-y-2">
                {breakdown.notes.map((note, index) => (
                  <li key={index} className="flex items-start gap-2 text-yellow-700">
                    <span className="text-yellow-500 mt-1 font-bold">•</span>
                    <span className="text-sm leading-relaxed">{note}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Transparency Statement */}
          <div className="bg-gray-50 rounded-lg p-4 border-2 border-gray-200">
            <h4 className="font-semibold text-gray-800 mb-2">NirmaanAI Transparency Commitment</h4>
            <p className="text-sm text-gray-600 leading-relaxed">
              This calculation is based on engineering first principles, IS codes, and current market rates. 
              All quantities are derived from geometric analysis of your building design using SI units internally, 
              ensuring accuracy and transparency in every cost component. No estimation shortcuts or top-down 
              multipliers are used - every value is traceable to its engineering source.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            Calculation based on IS codes, engineering standards, and live market data
          </div>
          <button
            onClick={onClose}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}