import React, { useState } from 'react';
import { Play, CheckCircle, AlertCircle, Calculator, Database, Zap } from 'lucide-react';
import { laborRateAPI } from '../../lib/supabase';
import { seedEnhancedLaborRates } from '../../lib/enhancedLaborRateSeeder';
import { calculateLaborCost, EnhancedLaborRate, ProjectContext } from '../../lib/multiModalLaborCalculations';

interface TestResult {
  test: string;
  success: boolean;
  message: string;
  data?: any;
}

export function MultiModalIntegrationTest() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runComprehensiveTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    const results: TestResult[] = [];

    try {
      // Test 1: Database Connection
      results.push(await testDatabaseConnection());
      
      // Test 2: Enhanced Labor Rate Seeding
      results.push(await testEnhancedRateSeeding());
      
      // Test 3: Multi-Modal Calculations
      results.push(await testMultiModalCalculations());
      
      // Test 4: Cost Optimization
      results.push(await testCostOptimization());
      
      // Test 5: V2 Integration
      results.push(await testV2Integration());

    } catch (error) {
      results.push({
        test: 'Overall Test Suite',
        success: false,
        message: `Test suite failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }

    setTestResults(results);
    setIsRunning(false);
  };

  const testDatabaseConnection = async (): Promise<TestResult> => {
    try {
      const rates = await laborRateAPI.getAll();
      return {
        test: 'Database Connection',
        success: true,
        message: `Successfully connected. Found ${rates.length} labor rates.`,
        data: { count: rates.length }
      };
    } catch (error) {
      return {
        test: 'Database Connection',
        success: false,
        message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  };

  const testEnhancedRateSeeding = async (): Promise<TestResult> => {
    try {
      // Check if enhanced rates already exist
      const existingRates = await laborRateAPI.getAll();
      const enhancedRates = existingRates.filter(rate => 
        (rate as any).charging_models && Object.keys((rate as any).charging_models).length > 0
      );

      if (enhancedRates.length === 0) {
        // Seed a few test rates
        await seedEnhancedLaborRates();
        const newRates = await laborRateAPI.getAll();
        const newEnhancedRates = newRates.filter(rate => 
          (rate as any).charging_models && Object.keys((rate as any).charging_models).length > 0
        );

        return {
          test: 'Enhanced Rate Seeding',
          success: newEnhancedRates.length > 0,
          message: `Seeded ${newEnhancedRates.length} enhanced labor rates successfully.`,
          data: { seeded: newEnhancedRates.length }
        };
      } else {
        return {
          test: 'Enhanced Rate Seeding',
          success: true,
          message: `Found ${enhancedRates.length} existing enhanced rates. Skipping seeding.`,
          data: { existing: enhancedRates.length }
        };
      }
    } catch (error) {
      return {
        test: 'Enhanced Rate Seeding',
        success: false,
        message: `Seeding failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  };

  const testMultiModalCalculations = async (): Promise<TestResult> => {
    try {
      // Get an enhanced labor rate
      const rates = await laborRateAPI.getAll();
      const enhancedRate = rates.find(rate => 
        (rate as any).charging_models && Object.keys((rate as any).charging_models).length > 0
      ) as EnhancedLaborRate;

      if (!enhancedRate) {
        return {
          test: 'Multi-Modal Calculations',
          success: false,
          message: 'No enhanced labor rates found for testing'
        };
      }

      const projectContext: ProjectContext = {
        total_area: 1500,
        project_type: 'residential',
        duration_months: 4,
        quality_tier: 'better',
        budget_preference: 'balanced'
      };

      const result = calculateLaborCost(enhancedRate, 25, projectContext);

      const isValid = result.primary_cost > 0 && 
                     result.model_used && 
                     result.breakdown && 
                     result.efficiency_rating;

      return {
        test: 'Multi-Modal Calculations',
        success: isValid,
        message: isValid 
          ? `Calculation successful. Cost: ₹${result.primary_cost.toLocaleString()}, Model: ${result.model_used}, Efficiency: ${result.efficiency_rating}`
          : 'Calculation returned invalid results',
        data: result
      };
    } catch (error) {
      return {
        test: 'Multi-Modal Calculations',
        success: false,
        message: `Calculation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  };

  const testCostOptimization = async (): Promise<TestResult> => {
    try {
      const rates = await laborRateAPI.getAll();
      const enhancedRates = rates.filter(rate => 
        (rate as any).charging_models && Object.keys((rate as any).charging_models).length > 0
      ) as EnhancedLaborRate[];

      if (enhancedRates.length === 0) {
        return {
          test: 'Cost Optimization',
          success: false,
          message: 'No enhanced rates available for optimization testing'
        };
      }

      const projectContext: ProjectContext = {
        total_area: 2500,
        project_type: 'commercial',
        duration_months: 6,
        quality_tier: 'better',
        budget_preference: 'cost_effective'
      };

      let totalSavings = 0;
      let optimizationCount = 0;

      for (const rate of enhancedRates.slice(0, 3)) { // Test first 3 rates
        const result = calculateLaborCost(rate, 30, projectContext);
        const bestSavings = result.alternatives.find(alt => alt.savings > 0);
        if (bestSavings) {
          totalSavings += bestSavings.savings;
          optimizationCount++;
        }
      }

      return {
        test: 'Cost Optimization',
        success: true,
        message: `Found ${optimizationCount} optimization opportunities with total potential savings of ₹${totalSavings.toLocaleString()}`,
        data: { optimizations: optimizationCount, savings: totalSavings }
      };
    } catch (error) {
      return {
        test: 'Cost Optimization',
        success: false,
        message: `Optimization test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  };

  const testV2Integration = async (): Promise<TestResult> => {
    try {
      // This is a simplified test - in reality, we'd need to test the full V2 calculation
      // For now, we'll just verify that the integration functions exist and are callable
      
      const { createProjectContext } = await import('../../utils/v2CalculationEngine');
      
      // Mock context for testing
      const mockContext = {
        inputs: { builtup_area: 1500, location: 'gurgaon', project_type: 'residential' },
        qualityTier: 'better' as const,
        components: [],
        tasks: [],
        laborRates: [],
        defaults: {},
        userOverrides: {},
        standards: {}
      };

      const mockGeometric = { totalBuiltUpArea: 1500 };
      
      // This should not throw an error if integration is correct
      const projectContext = createProjectContext(mockContext as any, mockGeometric as any);
      
      const isValid = projectContext.total_area === 1500 &&
                     projectContext.project_type === 'residential' &&
                     projectContext.quality_tier === 'better';

      return {
        test: 'V2 Integration',
        success: isValid,
        message: isValid 
          ? 'V2 calculation engine integration successful'
          : 'V2 integration returned invalid context',
        data: projectContext
      };
    } catch (error) {
      return {
        test: 'V2 Integration',
        success: false,
        message: `V2 integration test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <AlertCircle className="w-5 h-5 text-red-500" />
    );
  };

  const getOverallStatus = () => {
    if (testResults.length === 0) return null;
    const passed = testResults.filter(r => r.success).length;
    const total = testResults.length;
    return { passed, total, percentage: Math.round((passed / total) * 100) };
  };

  const overallStatus = getOverallStatus();

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Multi-Modal Integration Test Suite</h2>
              <p className="text-sm text-gray-600 mt-1">
                Comprehensive testing of enhanced labor rate system integration
              </p>
            </div>
            <button
              onClick={runComprehensiveTests}
              disabled={isRunning}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg flex items-center space-x-2"
            >
              <Play className="w-4 h-4" />
              <span>{isRunning ? 'Running Tests...' : 'Run All Tests'}</span>
            </button>
          </div>
        </div>

        {/* Overall Status */}
        {overallStatus && (
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getStatusIcon(overallStatus.percentage === 100)}
                <div>
                  <div className="font-medium text-gray-900">
                    Overall Status: {overallStatus.passed}/{overallStatus.total} Tests Passed
                  </div>
                  <div className="text-sm text-gray-600">
                    {overallStatus.percentage}% Success Rate
                  </div>
                </div>
              </div>
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                overallStatus.percentage === 100 
                  ? 'bg-green-100 text-green-800' 
                  : overallStatus.percentage >= 80 
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {overallStatus.percentage === 100 ? 'All Systems Operational' :
                 overallStatus.percentage >= 80 ? 'Minor Issues Detected' :
                 'Critical Issues Found'}
              </div>
            </div>
          </div>
        )}

        {/* Test Results */}
        <div className="px-6 py-4">
          {testResults.length === 0 && !isRunning && (
            <div className="text-center py-8 text-gray-500">
              Click "Run All Tests" to start comprehensive integration testing
            </div>
          )}

          {isRunning && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Running comprehensive tests...</p>
            </div>
          )}

          {testResults.length > 0 && (
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <div key={index} className={`border rounded-lg p-4 ${
                  result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                }`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(result.success)}
                      <h4 className="font-medium text-gray-900">{result.test}</h4>
                    </div>
                    <div className={`px-2 py-1 rounded text-xs font-medium ${
                      result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {result.success ? 'PASSED' : 'FAILED'}
                    </div>
                  </div>
                  
                  <p className={`text-sm ${
                    result.success ? 'text-green-700' : 'text-red-700'
                  }`}>
                    {result.message}
                  </p>

                  {result.data && (
                    <details className="mt-2">
                      <summary className="text-xs text-gray-600 cursor-pointer hover:text-gray-800">
                        View Test Data
                      </summary>
                      <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Quick Actions */}
        {testResults.length > 0 && (
          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <h4 className="font-medium text-gray-900 mb-3">Quick Actions</h4>
            <div className="flex space-x-3">
              <button
                onClick={() => window.location.reload()}
                className="px-3 py-2 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
              >
                Refresh Page
              </button>
              <button
                onClick={() => console.log('Test Results:', testResults)}
                className="px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                Log Results to Console
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
