import React, { useState, useEffect, useMemo } from 'react';
import { Calculator, TrendingUp, TrendingDown, AlertCircle, CheckCircle, Info, BarChart3, Database } from 'lucide-react';
import { LaborRate } from '../../lib/supabase';
import {
  EnhancedLaborRate,
  calculateProjectLaborCosts,
  ProjectContext,
  calculateLaborCost
} from '../../lib/multiModalLaborCalculations';
import { seedEnhancedLaborRates } from '../../lib/enhancedLaborRateSeeder';

interface LaborCostComparisonPanelProps {
  laborRates: LaborRate[];
  onClose: () => void;
  onRefreshRates?: () => void;
}

export function LaborCostComparisonPanel({ laborRates, onClose, onRefreshRates }: LaborCostComparisonPanelProps) {
  const [projectContext, setProjectContext] = useState<ProjectContext>({
    total_area: 1500,
    project_type: 'residential',
    duration_months: 4,
    quality_tier: 'better',
    budget_preference: 'balanced'
  });

  const [selectedRates, setSelectedRates] = useState<string[]>([]);

  // Calculate quantities based on project area (more realistic)
  const getQuantityForRate = (rate: LaborRate): number => {
    const area = projectContext.total_area;
    const baseQuantities: Record<string, number> = {
      'foundation': Math.ceil(area * 0.03), // 3% of area in m³
      'masonry': Math.ceil(area * 0.8),     // 80% of area in sqm
      'finishes': Math.ceil(area * 1.2),    // 120% of area in sqm (walls + ceiling)
      'flooring': Math.ceil(area * 0.9),    // 90% of area in sqm
      'electrical': Math.ceil(area * 0.02), // 2 points per 100 sqft
      'plumbing': Math.ceil(area * 0.01),   // 1 point per 100 sqft
      'specialized': Math.ceil(area * 0.005) // 0.5% of area
    };

    const quantity = baseQuantities[rate.category] || Math.ceil(area * 0.01);
    console.log(`📏 Quantity for ${rate.name} (${rate.category}): ${quantity} ${rate.unit}`);
    return quantity;
  };

  const calculateIndividualCosts = () => {
    return laborRates.map(rate => {
      try {
        const enhancedRate = rate as EnhancedLaborRate;
        const quantity = getQuantityForRate(rate);

        // Debug: Log the rate structure
        console.log(`Analyzing rate: ${enhancedRate.name}`, {
          hasRates: !!enhancedRate.rates,
          hasProductivity: !!enhancedRate.productivity,
          hasChargingModels: !!(enhancedRate as any).charging_models,
          rateStructure: enhancedRate.rates,
          productivityStructure: enhancedRate.productivity,
          chargingModels: (enhancedRate as any).charging_models,
          fullRate: enhancedRate
        });

        // Ensure the rate has basic structure for calculation
        if (!enhancedRate.rates || !enhancedRate.productivity) {
          console.warn(`Labor rate ${enhancedRate.name} missing required data, creating fallback`);

          // Create fallback calculation using basic rate if available
          const fallbackRate = (enhancedRate as any).unit_price || 100; // Default fallback
          const fallbackCost = quantity * fallbackRate;

          return {
            rate,
            quantity,
            result: {
              primary_cost: fallbackCost,
              model_used: 'fallback',
              alternatives: [],
              breakdown: {
                base_rate: fallbackRate,
                quantity,
                total: fallbackCost,
                unit: rate.unit || 'unit',
                timeline_days: Math.ceil(quantity / 10) // Assume 10 units per day
              },
              recommendations: ['Rate data incomplete - using fallback calculation'],
              efficiency_rating: 'poor' as const
            }
          };
        }

        // Additional debug: Test the calculation directly
        console.log(`Testing calculation for ${enhancedRate.name}:`, {
          quantity,
          projectContext,
          ratesGood: enhancedRate.rates.good,
          ratesBetter: enhancedRate.rates.better,
          ratesBest: enhancedRate.rates.best,
          productivity: enhancedRate.productivity
        });

        let result;
        try {
          result = calculateLaborCost(enhancedRate, quantity, projectContext);
          console.log(`✅ Calculation successful for ${enhancedRate.name}:`, result);
        } catch (calcError) {
          console.error(`❌ Calculation failed for ${enhancedRate.name}:`, calcError);
          // Create a fallback result
          const fallbackCost = quantity * enhancedRate.rates.good;
          result = {
            primary_cost: fallbackCost,
            model_used: 'fallback_per_unit',
            alternatives: [],
            breakdown: {
              base_rate: enhancedRate.rates.good,
              quantity,
              total: fallbackCost,
              unit: rate.unit || 'unit',
              timeline_days: Math.ceil(quantity / (enhancedRate.productivity?.output_per_day || 10))
            },
            recommendations: [`Calculation error: ${calcError instanceof Error ? calcError.message : 'Unknown error'}`],
            efficiency_rating: 'fair' as const
          };
        }

        return {
          rate,
          quantity,
          result
        };
      } catch (error) {
        console.error(`Error calculating cost for ${rate.name}:`, error);
        return {
          rate,
          quantity: getQuantityForRate(rate),
          result: {
            primary_cost: 0,
            model_used: 'per_unit',
            alternatives: [],
            breakdown: {
              base_rate: 0,
              quantity: getQuantityForRate(rate),
              total: 0,
              unit: rate.unit || 'unit',
              timeline_days: 0
            },
            recommendations: ['Calculation error'],
            efficiency_rating: 'poor' as const
          }
        };
      }
    });
  };

  // Use useMemo to recalculate when projectContext or laborRates change
  const individualCosts = useMemo(() => {
    console.log('🔄 Recalculating costs with context:', projectContext);
    return calculateIndividualCosts();
  }, [laborRates, projectContext]);

  const totalProjectCost = useMemo(() => {
    const total = individualCosts.reduce((sum, item) => sum + item.result.primary_cost, 0);
    console.log('💰 Total project cost updated:', total.toLocaleString());
    return total;
  }, [individualCosts]);

  const totalPotentialSavings = useMemo(() =>
    individualCosts.reduce((sum, item) => {
      const bestSavings = item.result.alternatives.find(alt => alt.savings > 0);
      return sum + (bestSavings?.savings || 0);
    }, 0),
    [individualCosts]
  );

  const optimizationOpportunities = useMemo(() =>
    individualCosts.filter(item =>
      item.result.alternatives.some(alt => alt.savings > 0 && alt.savings_percentage > 10)
    ),
    [individualCosts]
  );

  const efficiencyDistribution = useMemo(() =>
    individualCosts.reduce((acc, item) => {
      acc[item.result.efficiency_rating] = (acc[item.result.efficiency_rating] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    [individualCosts]
  );

  const [isSeeding, setIsSeeding] = useState(false);

  const handleSeedEnhancedRates = async () => {
    try {
      setIsSeeding(true);
      await seedEnhancedLaborRates();
      if (onRefreshRates) {
        onRefreshRates();
      }
      alert('Enhanced labor rates seeded successfully! Please refresh the analysis.');
    } catch (error) {
      console.error('Error seeding enhanced rates:', error);
      alert('Error seeding enhanced rates. Check console for details.');
    } finally {
      setIsSeeding(false);
    }
  };

  // Check if we have any valid costs
  const hasValidCosts = individualCosts.some(item => item.result.primary_cost > 0);
  const enhancedRatesCount = laborRates.filter(rate =>
    (rate as any).charging_models && Object.keys((rate as any).charging_models).length > 0
  ).length;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Labor Cost Analysis & Optimization</h3>
              <p className="text-sm text-gray-600 mt-1">
                Comprehensive cost comparison across different charging models
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold p-2"
              title="Close"
            >
              ×
            </button>
          </div>
        </div>

        {/* Warning Banner for Missing Enhanced Rates */}
        {!hasValidCosts && (
          <div className="px-6 py-4 bg-yellow-50 border-b border-yellow-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-yellow-600 mr-2" />
                <div>
                  <div className="text-sm font-medium text-yellow-800">
                    No Enhanced Labor Rates Found
                  </div>
                  <div className="text-xs text-yellow-600">
                    Found {enhancedRatesCount} enhanced rates out of {laborRates.length} total rates.
                    Seed enhanced rates to enable cost optimization analysis.
                  </div>
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={handleSeedEnhancedRates}
                  disabled={isSeeding}
                  className="px-3 py-2 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700 disabled:bg-gray-400 flex items-center space-x-2"
                >
                  <Database className="w-4 h-4" />
                  <span>{isSeeding ? 'Seeding...' : 'Seed Enhanced Rates'}</span>
                </button>
                <button
                  onClick={() => {
                    console.log('🔍 === DEBUG INFO ===');
                    console.log('📊 Current project context:', projectContext);
                    console.log('📋 Labor rates count:', laborRates.length);
                    console.log('⚡ Enhanced rates count:', enhancedRatesCount);
                    console.log('💰 Total project cost:', totalProjectCost.toLocaleString());
                    console.log('💾 Individual costs:', individualCosts.map(c => ({
                      name: c.rate.name,
                      quantity: c.quantity,
                      cost: c.result.primary_cost,
                      model: c.result.model_used
                    })));
                    console.log('🔍 === END DEBUG ===');
                  }}
                  className="px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 flex items-center space-x-2"
                >
                  <Info className="w-4 h-4" />
                  <span>Debug Info</span>
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="flex h-[calc(90vh-100px)]">
          {/* Left Panel - Project Configuration */}
          <div className="w-1/3 p-6 border-r border-gray-200 overflow-y-auto">
            <h4 className="font-medium text-gray-900 mb-4">Project Configuration</h4>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Total Area (sqft)</label>
                <input
                  type="number"
                  value={projectContext.total_area}
                  onChange={(e) => {
                    const newArea = Number(e.target.value);
                    console.log('🏠 Area changed to:', newArea);
                    setProjectContext(prev => ({ ...prev, total_area: newArea }));
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Project Type</label>
                <select
                  value={projectContext.project_type}
                  onChange={(e) => setProjectContext(prev => ({ ...prev, project_type: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="residential">Residential</option>
                  <option value="commercial">Commercial</option>
                  <option value="industrial">Industrial</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Duration (months)</label>
                <input
                  type="number"
                  value={projectContext.duration_months}
                  onChange={(e) => setProjectContext(prev => ({ ...prev, duration_months: Number(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Quality Tier</label>
                <select
                  value={projectContext.quality_tier}
                  onChange={(e) => {
                    const newTier = e.target.value as any;
                    console.log('⭐ Quality tier changed to:', newTier);
                    setProjectContext(prev => ({ ...prev, quality_tier: newTier }));
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="good">Good</option>
                  <option value="better">Better</option>
                  <option value="best">Best</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Budget Preference</label>
                <select
                  value={projectContext.budget_preference}
                  onChange={(e) => setProjectContext(prev => ({ ...prev, budget_preference: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="cost_effective">Cost Effective</option>
                  <option value="balanced">Balanced</option>
                  <option value="premium">Premium</option>
                </select>
              </div>
            </div>

            {/* Summary Cards */}
            <div className="mt-6 space-y-3">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center">
                  <Calculator className="w-5 h-5 text-blue-600 mr-2" />
                  <div>
                    <div className="text-lg font-semibold text-blue-800">
                      ₹{totalProjectCost.toLocaleString()}
                    </div>
                    <div className="text-sm text-blue-600">Total Project Cost</div>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center">
                  <TrendingDown className="w-5 h-5 text-green-600 mr-2" />
                  <div>
                    <div className="text-lg font-semibold text-green-800">
                      ₹{totalPotentialSavings.toLocaleString()}
                    </div>
                    <div className="text-sm text-green-600">Potential Savings</div>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center">
                  <AlertCircle className="w-5 h-5 text-yellow-600 mr-2" />
                  <div>
                    <div className="text-lg font-semibold text-yellow-800">
                      {optimizationOpportunities.length}
                    </div>
                    <div className="text-sm text-yellow-600">Optimization Opportunities</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Efficiency Distribution */}
            <div className="mt-6">
              <h5 className="font-medium text-gray-900 mb-3">Efficiency Distribution</h5>
              <div className="space-y-2">
                {Object.entries(efficiencyDistribution).map(([rating, count]) => (
                  <div key={rating} className="flex justify-between items-center">
                    <span className="text-sm capitalize">{rating}</span>
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full mr-2 ${
                        rating === 'excellent' ? 'bg-green-500' :
                        rating === 'good' ? 'bg-blue-500' :
                        rating === 'fair' ? 'bg-yellow-500' : 'bg-red-500'
                      }`}></div>
                      <span className="text-sm font-medium">{count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Panel - Detailed Analysis */}
          <div className="w-2/3 p-6 overflow-y-auto">
            <h4 className="font-medium text-gray-900 mb-4">Detailed Cost Analysis</h4>
            
            <div className="space-y-4">
              {individualCosts.map((item, index) => (
                <div key={item.rate.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h5 className="font-medium text-gray-900">{item.rate.name}</h5>
                      <p className="text-sm text-gray-600">
                        {item.quantity} {item.rate.unit} • {item.rate.category}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-gray-900">
                        ₹{item.result.primary_cost.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-600">
                        {item.result.model_used.replace('_', ' ')}
                      </div>
                    </div>
                  </div>

                  {/* Efficiency Rating */}
                  <div className="flex items-center mb-3">
                    <span className="text-sm text-gray-600 mr-2">Efficiency:</span>
                    <div className={`px-2 py-1 rounded text-xs font-medium ${
                      item.result.efficiency_rating === 'excellent' ? 'bg-green-100 text-green-800' :
                      item.result.efficiency_rating === 'good' ? 'bg-blue-100 text-blue-800' :
                      item.result.efficiency_rating === 'fair' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {item.result.efficiency_rating}
                    </div>
                  </div>

                  {/* Alternatives */}
                  {item.result.alternatives.length > 0 && (
                    <div>
                      <h6 className="text-sm font-medium text-gray-700 mb-2">Alternative Pricing Models:</h6>
                      <div className="space-y-2">
                        {item.result.alternatives.slice(0, 3).map((alt, altIndex) => (
                          <div key={altIndex} className="flex justify-between items-center text-sm">
                            <span className="capitalize">{alt.model.replace('_', ' ')}</span>
                            <div className="flex items-center">
                              <span className="mr-2">₹{alt.cost.toLocaleString()}</span>
                              <div className={`flex items-center ${
                                alt.savings > 0 ? 'text-green-600' : 'text-red-600'
                              }`}>
                                {alt.savings > 0 ? (
                                  <TrendingDown className="w-3 h-3 mr-1" />
                                ) : (
                                  <TrendingUp className="w-3 h-3 mr-1" />
                                )}
                                <span>{Math.abs(alt.savings_percentage).toFixed(1)}%</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Recommendations */}
                  {item.result.recommendations.length > 0 && (
                    <div className="mt-3 p-3 bg-blue-50 rounded">
                      <div className="flex items-start">
                        <Info className="w-4 h-4 text-blue-600 mr-2 mt-0.5" />
                        <div>
                          <h6 className="text-sm font-medium text-blue-800 mb-1">Recommendations:</h6>
                          <ul className="text-sm text-blue-700 space-y-1">
                            {item.result.recommendations.map((rec, recIndex) => (
                              <li key={recIndex}>• {rec}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Optimization Summary */}
            {optimizationOpportunities.length > 0 && (
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <h5 className="font-medium text-green-800 mb-3">Top Optimization Opportunities</h5>
                <div className="space-y-2">
                  {optimizationOpportunities.slice(0, 5).map((item, index) => {
                    const bestSavings = item.result.alternatives.find(alt => alt.savings > 0);
                    return (
                      <div key={index} className="flex justify-between items-center text-sm">
                        <span>{item.rate.name}</span>
                        <div className="text-green-700 font-medium">
                          Save ₹{bestSavings?.savings.toLocaleString()} ({bestSavings?.savings_percentage.toFixed(1)}%)
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
