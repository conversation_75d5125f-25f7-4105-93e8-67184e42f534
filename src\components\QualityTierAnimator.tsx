import React, { useEffect, useState } from 'react';
import { QualityTier } from '../types/calculator';

interface QualityTierAnimatorProps {
  currentTier: QualityTier;
  onTierChange: (tier: QualityTier) => void;
  children: React.ReactNode;
}

export function QualityTierAnimator({ currentTier, onTierChange, children }: QualityTierAnimatorProps) {
  const [animatingItems, setAnimatingItems] = useState<Set<string>>(new Set());
  const [previousTier, setPreviousTier] = useState<QualityTier>(currentTier);

  useEffect(() => {
    if (previousTier !== currentTier) {
      // Trigger animation for affected items
      const affectedItems = new Set([
        'concrete_foundation',
        'concrete_structure', 
        'steel_work',
        'brickwork',
        'flooring',
        'interior_painting',
        'main_door',
        'windows',
        'electrical',
        'bathroom_fittings'
      ]);
      
      setAnimatingItems(affectedItems);
      
      // Remove animation after 2 seconds
      setTimeout(() => {
        setAnimatingItems(new Set());
      }, 2000);
      
      setPreviousTier(currentTier);
    }
  }, [currentTier, previousTier]);

  const handleTierChange = (tier: QualityTier) => {
    onTierChange(tier);
  };

  // Clone children and add animation props
  const enhancedChildren = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, {
        animatingItems,
        onTierChange: handleTierChange
      } as any);
    }
    return child;
  });

  return <>{enhancedChildren}</>;
}