import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Minus, RefreshCw, AlertCircle } from 'lucide-react';

interface MaterialPrice {
  id: string;
  name: string;
  currentPrice: number;
  previousPrice: number;
  trend: 'up' | 'down' | 'stable';
  lastUpdated: string;
  volatility: 'high' | 'medium' | 'low';
  seasonalImpact: number;
}

interface MaterialPriceTrackerProps {
  onPriceUpdate: (materialId: string, newPrice: number) => void;
}

export function MaterialPriceTracker({ onPriceUpdate }: MaterialPriceTrackerProps) {
  const [materials, setMaterials] = useState<MaterialPrice[]>([
    {
      id: 'cement',
      name: 'Cement (OPC 53 Grade)',
      currentPrice: 385,
      previousPrice: 375,
      trend: 'up',
      lastUpdated: '2024-01-15',
      volatility: 'medium',
      seasonalImpact: 1.1
    },
    {
      id: 'steel',
      name: 'TMT Steel (Fe 500D)',
      currentPrice: 52,
      previousPrice: 54,
      trend: 'down',
      lastUpdated: '2024-01-15',
      volatility: 'high',
      seasonalImpact: 1.0
    },
    {
      id: 'sand',
      name: 'River Sand',
      currentPrice: 50,
      previousPrice: 48,
      trend: 'up',
      lastUpdated: '2024-01-15',
      volatility: 'high',
      seasonalImpact: 1.3
    },
    {
      id: 'aggregate',
      name: 'Coarse Aggregate (20mm)',
      currentPrice: 60,
      previousPrice: 60,
      trend: 'stable',
      lastUpdated: '2024-01-15',
      volatility: 'low',
      seasonalImpact: 1.1
    }
  ]);

  const [isUpdating, setIsUpdating] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-red-500" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-green-500" />;
      default:
        return <Minus className="w-4 h-4 text-gray-500" />;
    }
  };

  const getVolatilityColor = (volatility: string) => {
    switch (volatility) {
      case 'high':
        return 'text-red-600 bg-red-50';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50';
      default:
        return 'text-green-600 bg-green-50';
    }
  };

  const updatePrices = async () => {
    setIsUpdating(true);
    // Simulate API call
    setTimeout(() => {
      setMaterials(prev => prev.map(material => {
        const variation = (Math.random() - 0.5) * 0.1; // ±5% variation
        const newPrice = Math.round(material.currentPrice * (1 + variation));
        const trend = newPrice > material.currentPrice ? 'up' : 
                     newPrice < material.currentPrice ? 'down' : 'stable';
        
        onPriceUpdate(material.id, newPrice);
        
        return {
          ...material,
          previousPrice: material.currentPrice,
          currentPrice: newPrice,
          trend,
          lastUpdated: new Date().toISOString().split('T')[0]
        };
      }));
      setIsUpdating(false);
    }, 2000);
  };

  const calculatePriceChange = (current: number, previous: number) => {
    const change = ((current - previous) / previous) * 100;
    return change.toFixed(1);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-semibold text-gray-800">Live Material Prices - Gurgaon</h3>
          <p className="text-gray-600 text-sm mt-1">Real-time market rates with trend analysis</p>
        </div>
        <button
          onClick={updatePrices}
          disabled={isUpdating}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
        >
          <RefreshCw className={`w-4 h-4 ${isUpdating ? 'animate-spin' : ''}`} />
          {isUpdating ? 'Updating...' : 'Refresh Prices'}
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {materials.map((material) => (
          <div key={material.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between mb-3">
              <div>
                <h4 className="font-semibold text-gray-800 text-sm">{material.name}</h4>
                <div className="flex items-center gap-1 mt-1">
                  {getTrendIcon(material.trend)}
                  <span className={`text-xs ${
                    material.trend === 'up' ? 'text-red-600' : 
                    material.trend === 'down' ? 'text-green-600' : 'text-gray-600'
                  }`}>
                    {material.trend === 'stable' ? '0.0' : calculatePriceChange(material.currentPrice, material.previousPrice)}%
                  </span>
                </div>
              </div>
              <span className={`text-xs px-2 py-1 rounded-full ${getVolatilityColor(material.volatility)}`}>
                {material.volatility}
              </span>
            </div>

            <div className="mb-3">
              <div className="text-2xl font-bold text-gray-800">
                {formatCurrency(material.currentPrice)}
              </div>
              <div className="text-xs text-gray-500">
                Previous: {formatCurrency(material.previousPrice)}
              </div>
            </div>

            <div className="space-y-2 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-600">Last Updated:</span>
                <span className="text-gray-800">{material.lastUpdated}</span>
              </div>
              {material.seasonalImpact > 1 && (
                <div className="flex items-center gap-1 text-orange-600">
                  <AlertCircle className="w-3 h-3" />
                  <span>Monsoon impact: +{((material.seasonalImpact - 1) * 100).toFixed(0)}%</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-start gap-2">
          <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
          <div>
            <h4 className="font-semibold text-yellow-800">Price Volatility Alert</h4>
            <p className="text-yellow-700 text-sm mt-1">
              Material prices in Gurgaon are subject to daily fluctuations. Consider adding a 5-8% escalation clause 
              for projects spanning more than 3 months. Sand prices typically increase by 30% during monsoon season.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}