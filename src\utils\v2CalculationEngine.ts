import { UserInputs, CalculationResult, QualityTier, CostSection, CostItem, GeometricQuantities, ProjectSummary, RoomConfiguration } from '../types/calculator'
import { Component, Task, LaborRate, componentAPI, taskAPI, uiDefaultsAPI, overrideAPI, laborRateAPI, EngineeringStandards, fetchCalculationStandards } from '../lib/supabase'

/**
 * NirmaanAI v2.0 Calculation Engine
 *
 * This is the new pluggable calculation engine that uses Supabase components
 * and tasks to calculate costs from bottom-up engineering principles.
 *
 * V2.1 UPDATE: Now completely independent with no V1 dependencies.
 * All calculations use admin panel/database values with proper safety standards.
 */

/**
 * UTILITY FUNCTIONS
 */
function convertSqftToSqm(sqft: number): number {
  return sqft / 10.764;
}

function convertSqmToSqft(sqm: number): number {
  return sqm * 10.764;
}

/**
 * Generate room configuration based on total built-up area
 */
function generateRoomConfiguration(totalBuiltUpArea: number) {
  const rooms = [];

  if (totalBuiltUpArea < 1000) {
    // Small house
    rooms.push(
      { id: 'bedroom-1', type: 'Bedroom', count: 1, areaPerRoom: 120, totalArea: 120 },
      { id: 'living-1', type: 'Living Room', count: 1, areaPerRoom: 150, totalArea: 150 },
      { id: 'kitchen-1', type: 'Kitchen', count: 1, areaPerRoom: 80, totalArea: 80 },
      { id: 'bathroom-1', type: 'Bathroom', count: 1, areaPerRoom: 40, totalArea: 40 }
    );
  } else if (totalBuiltUpArea < 2000) {
    // Medium house
    rooms.push(
      { id: 'bedroom-1', type: 'Bedroom', count: 2, areaPerRoom: 120, totalArea: 240 },
      { id: 'living-1', type: 'Living Room', count: 1, areaPerRoom: 200, totalArea: 200 },
      { id: 'kitchen-1', type: 'Kitchen', count: 1, areaPerRoom: 100, totalArea: 100 },
      { id: 'bathroom-1', type: 'Bathroom', count: 2, areaPerRoom: 50, totalArea: 100 },
      { id: 'balcony-1', type: 'Balcony', count: 1, areaPerRoom: 60, totalArea: 60 }
    );
  } else {
    // Large house
    rooms.push(
      { id: 'bedroom-1', type: 'Bedroom', count: 3, areaPerRoom: 140, totalArea: 420 },
      { id: 'living-1', type: 'Living Room', count: 1, areaPerRoom: 250, totalArea: 250 },
      { id: 'kitchen-1', type: 'Kitchen', count: 1, areaPerRoom: 120, totalArea: 120 },
      { id: 'bathroom-1', type: 'Bathroom', count: 3, areaPerRoom: 60, totalArea: 180 },
      { id: 'balcony-1', type: 'Balcony', count: 2, areaPerRoom: 80, totalArea: 160 },
      { id: 'study-1', type: 'Study', count: 1, areaPerRoom: 100, totalArea: 100 }
    );
  }

  const currentTotal = rooms.reduce((sum, room) => sum + room.totalArea, 0);
  const scaleFactor = totalBuiltUpArea / currentTotal;

  return rooms.map(room => ({
    ...room,
    areaPerRoom: Math.round(room.areaPerRoom * scaleFactor),
    totalArea: Math.round(room.totalArea * scaleFactor)
  }));
}

/**
 * V2 INDEPENDENT GEOMETRIC ANALYSIS
 *
 * This function performs complete geometric analysis using ONLY database/admin values.
 * NO hardcoded fallbacks - ensures admin panel controls all calculations.
 */
function performV2GeometricAnalysis(
  inputs: UserInputs,
  standards: EngineeringStandards
): GeometricQuantities {
  // Convert input from sqft to sqm for internal calculations
  const plotSizeSqm = convertSqftToSqm(inputs.plotSize);
  const groundCoverageAreaSqm = plotSizeSqm * (inputs.constructionPercentage / 100);

  // Basic area calculations in sqm
  const basementAreaSqm = inputs.hasBasement ? groundCoverageAreaSqm : 0;
  const stiltAreaSqm = inputs.hasStiltParking ? groundCoverageAreaSqm : 0;
  const totalFloorAreaSqm = groundCoverageAreaSqm * inputs.numberOfFloors + basementAreaSqm + stiltAreaSqm;
  const totalBuiltUpArea = convertSqmToSqft(totalFloorAreaSqm);

  // CRITICAL: Use admin panel values with safe fallbacks
  const foundationDepth = standards?.structuralAssumptions?.foundationDepth || 2.5; // 2.5m safe default
  const gridSpacing = standards?.structuralAssumptions?.gridSpacing || 3.5; // 3.5m safe default
  const slabThickness = (standards?.structuralAssumptions?.slabThickness || 150) / 1000; // Convert mm to m

  // STRUCTURAL GRID SIMULATION using admin values
  const buildingLength = Math.sqrt(groundCoverageAreaSqm * 1.5); // Rectangular assumption
  const buildingWidth = groundCoverageAreaSqm / buildingLength;

  const numberOfBaysLength = Math.ceil(buildingLength / gridSpacing);
  const numberOfBaysWidth = Math.ceil(buildingWidth / gridSpacing);

  // FOUNDATION CALCULATIONS using admin panel depth
  const foundationType = inputs.hasBasement ? 'raft_foundation' : 'strip_footings';
  let concreteVolume_Foundations_m3: number;

  if (inputs.hasBasement) {
    // Raft foundation + retaining walls using admin values
    const raftThickness = standards?.structuralAssumptions?.raftThickness || 0.45; // 450mm minimum
    const retainingWallThickness = 0.3; // 300mm standard
    const retainingWallHeight = foundationDepth;

    const raftVolume = groundCoverageAreaSqm * raftThickness;
    const retainingWallVolume = (2 * (buildingLength + buildingWidth)) * retainingWallThickness * retainingWallHeight;

    concreteVolume_Foundations_m3 = raftVolume + retainingWallVolume;
  } else {
    // Strip footings using admin panel depth (CRITICAL FIX)
    const footingWidth = 0.6; // 600mm wide
    const footingDepth = foundationDepth; // USE ADMIN VALUE: 2.5m NOT 0.3m!
    const totalFootingLength = (numberOfBaysLength + 1) * buildingWidth +
                              (numberOfBaysWidth + 1) * buildingLength;

    concreteVolume_Foundations_m3 = totalFootingLength * footingWidth * footingDepth;
  }

  // COLUMN CALCULATIONS using admin panel sizing
  const totalFloors = inputs.numberOfFloors + (inputs.hasBasement ? 1 : 0);
  const floorHeight = 3.0; // 3m floor height standard

  // Get column size from admin panel based on floors
  let columnSize = 0.35; // Default 350mm
  if (inputs.numberOfFloors <= 2) {
    columnSize = 0.35; // G+1: 350mm
  } else if (inputs.numberOfFloors <= 4) {
    columnSize = 0.40; // G+2-3: 400mm
  } else {
    columnSize = 0.45; // G+4+: 450mm
  }

  const numberOfColumns = (numberOfBaysLength + 1) * (numberOfBaysWidth + 1);
  const totalColumnHeight = numberOfColumns * floorHeight * totalFloors;
  const concreteVolume_Columns_m3 = totalColumnHeight * columnSize * columnSize;

  // BEAM CALCULATIONS using admin panel values
  let beamWidth = 0.30; // 300mm from admin panel
  let beamDepth = 0.45; // 450mm standard depth

  // Main beams (along length)
  const mainBeamLength = numberOfBaysLength * gridSpacing * (numberOfBaysWidth + 1);
  // Secondary beams (along width)
  const secondaryBeamLength = numberOfBaysWidth * gridSpacing * (numberOfBaysLength + 1);

  const totalBeamLength = (mainBeamLength + secondaryBeamLength) * totalFloors;
  const concreteVolume_Beams_m3 = totalBeamLength * beamWidth * beamDepth;

  // SLAB CALCULATIONS using admin panel thickness
  const totalSlabArea = groundCoverageAreaSqm * totalFloors;
  const concreteVolume_Slabs_m3 = totalSlabArea * slabThickness;

  // TOTAL CONCRETE VOLUME
  const totalConcreteVolume_m3 = concreteVolume_Foundations_m3 +
                                concreteVolume_Columns_m3 +
                                concreteVolume_Beams_m3 +
                                concreteVolume_Slabs_m3;

  // STEEL CALCULATIONS using admin panel ratios with safe fallbacks
  const foundationSteelRatio = standards?.steelRatios?.foundations?.[foundationType] || 140; // From admin
  const columnSteelRatio = standards?.steelRatios?.columns?.typical_floor || 180; // From admin
  const beamSteelRatio = standards?.steelRatios?.beams?.main_beams || 160; // From admin
  const slabSteelRatio = standards?.steelRatios?.slabs?.two_way_slab || 120; // From admin

  const steel_foundations_kg = concreteVolume_Foundations_m3 * foundationSteelRatio;
  const steel_columns_kg = concreteVolume_Columns_m3 * columnSteelRatio;
  const steel_beams_kg = concreteVolume_Beams_m3 * beamSteelRatio;
  const steel_slabs_kg = concreteVolume_Slabs_m3 * slabSteelRatio;

  const totalSteel_kg = steel_foundations_kg + steel_columns_kg + steel_beams_kg + steel_slabs_kg;

  // WALL AREA CALCULATIONS
  const wallHeight = floorHeight - beamDepth; // Wall height minus beam
  const externalWallArea_sqm = 2 * (buildingLength + buildingWidth) * wallHeight * totalFloors;
  const internalWallArea_sqm = externalWallArea_sqm * 0.6; // 60% assumption - can be made dynamic later
  const totalWallArea_sqm = externalWallArea_sqm + internalWallArea_sqm;

  return {
    // Area calculations (in sqft for display)
    plotSize: inputs.plotSize,
    groundCoverageArea: convertSqmToSqft(groundCoverageAreaSqm),
    totalFloorArea_sqm: totalFloorAreaSqm,
    totalBuiltUpArea,
    basementArea: convertSqmToSqft(basementAreaSqm),
    stiltArea: convertSqmToSqft(stiltAreaSqm),

    // Structural quantities
    totalConcreteVolume_m3,
    concreteVolume_Foundations_m3,
    concreteVolume_Columns_m3,
    concreteVolume_Beams_m3,
    concreteVolume_Slabs_m3,

    // Steel quantities
    totalSteel_kg,
    steel_foundations_kg,
    steel_columns_kg,
    steel_beams_kg,
    steel_slabs_kg,

    // Wall areas
    totalWallArea_sqm,
    externalWallArea_sqm,
    internalWallArea_sqm,

    // Structural parameters (from admin panel)
    foundationDepth,
    gridSpacing,
    slabThickness: slabThickness * 1000, // Convert back to mm for display
    numberOfColumns,
    totalBeamLength,

    // Building dimensions
    buildingLength,
    buildingWidth,
    floorHeight,
    totalFloors,

    // Room configuration
    roomConfiguration: generateRoomConfiguration(totalBuiltUpArea)
  };
}

export interface V2CalculationContext {
  inputs: UserInputs
  qualityTier: QualityTier
  components: Component[]
  tasks: Task[]
  laborRates: LaborRate[]
  defaults: any
  userOverrides: Record<string, any>
  projectId?: string
  standards: EngineeringStandards
}

export async function calculateV2Cost(
  inputs: UserInputs,
  qualityTier: QualityTier = 'better',
  userOverrides: Record<string, any> = {},
  projectId?: string
): Promise<CalculationResult> {
  try {
    // Load all required data from Supabase
    const [components, tasks, defaults, laborRates, standards] = await Promise.all([
      componentAPI.getAll(),
      taskAPI.getAll(),
      uiDefaultsAPI.getDefaults(),
      laborRateAPI.getAll(),
      fetchCalculationStandards(inputs.location)
    ])

    // Load user overrides from database if project ID is provided
    let savedOverrides: Record<string, any> = {};
    if (projectId) {
      try {
        const overrides = await overrideAPI.getByProject(projectId);
        overrides.forEach(override => {
          savedOverrides[override.component_id] = {
            originalQuantity: override.original_quantity,
            originalRate: override.original_rate,
            overrideQuantity: override.override_quantity,
            overrideRate: override.override_rate,
            reason: override.override_reason
          };
        });
      } catch (error) {
        console.error('Error loading saved overrides:', error);
      }
    }

    const context: V2CalculationContext = {
      inputs,
      qualityTier,
      components,
      tasks,
      laborRates,
      defaults: defaults?.config_data || {},
      userOverrides: { ...savedOverrides, ...userOverrides },
      projectId,
      standards
    }

    // Perform V2 independent geometric analysis using admin panel values
    const geometricQuantities = performV2GeometricAnalysis(inputs, standards)

    // Calculate costs using pluggable components
    const sections = await calculateSectionsFromComponents(context, geometricQuantities)

    // Apply GST, contingency, and profit margin from standards
    const materialCosts = sections.reduce((sum, section) => {
      if (['foundation', 'masonry', 'rooms', 'facade'].includes(section.id)) {
        return sum + section.subtotal
      }
      return sum
    }, 0)

    const serviceCosts = sections.reduce((sum, section) => {
      if (['mep', 'fees'].includes(section.id)) {
        return sum + section.subtotal
      }
      return sum
    }, 0)

    const gstOnMaterials = materialCosts * ((standards?.systemConfig?.gstOnMaterials || 18) / 100)
    const gstOnServices = serviceCosts * ((standards?.systemConfig?.gstOnServices || 18) / 100)
    
    // Add GST section
    const gstItems: CostItem[] = [
      {
        id: 'gst_materials',
        name: `GST on Materials (${standards?.systemConfig?.gstOnMaterials || 18}%)`,
        quantity: 1,
        unit: 'lump sum',
        rate: gstOnMaterials,
        total: gstOnMaterials,
        category: 'taxes',
        editable: false,
        isCalculated: true
      },
      {
        id: 'gst_services',
        name: `GST on Services (${standards?.systemConfig?.gstOnServices || 18}%)`,
        quantity: 1,
        unit: 'lump sum',
        rate: gstOnServices,
        total: gstOnServices,
        category: 'taxes',
        editable: false,
        isCalculated: true
      }
    ]

    sections.push({
      id: 'taxes',
      title: 'Taxes & Duties',
      items: gstItems,
      subtotal: gstOnMaterials + gstOnServices
    })

    // Calculate subtotal before contingency and profit
    const subtotal = sections.reduce((sum, section) => sum + section.subtotal, 0)
    
    // Apply contingency
    const contingencyAmount = subtotal * ((standards?.systemConfig?.defaultContingency || 15) / 100)
    const contingencyItems: CostItem[] = [
      {
        id: 'contingency',
        name: `Contingency (${standards?.systemConfig?.defaultContingency || 15}%)`,
        quantity: 1,
        unit: 'lump sum',
        rate: contingencyAmount,
        total: contingencyAmount,
        category: 'contingency',
        editable: true,
        isCalculated: true
      }
    ]

    sections.push({
      id: 'contingency',
      title: 'Contingency',
      items: contingencyItems,
      subtotal: contingencyAmount
    })

    // Apply profit margin
    const subtotalWithContingency = subtotal + contingencyAmount
    const profitAmount = subtotalWithContingency * ((standards?.systemConfig?.defaultProfitMargin || 15) / 100)
    const profitItems: CostItem[] = [
      {
        id: 'profit_margin',
        name: `Contractor Profit (${standards?.systemConfig?.defaultProfitMargin || 15}%)`,
        quantity: 1,
        unit: 'lump sum',
        rate: profitAmount,
        total: profitAmount,
        category: 'profit',
        editable: true,
        isCalculated: true
      }
    ]

    sections.push({
      id: 'profit',
      title: 'Contractor Profit',
      items: profitItems,
      subtotal: profitAmount
    })

    const totalCost = sections.reduce((sum, section) => sum + section.subtotal, 0)
    const ratePerSqft = totalCost / geometricQuantities.totalBuiltUpArea

    return {
      totalCost,
      ratePerSqft,
      sections,
      qualityTier,
      quantities: {
        ...geometricQuantities,
        // Legacy compatibility fields
        totalRCCVolume: geometricQuantities.totalConcreteVolume_m3,
        totalTMTSteel: geometricQuantities.totalSteel_kg,
        totalBrickworkArea: geometricQuantities.totalWallArea_sqm * 10.764, // Convert to sqft
        totalPlasterArea: geometricQuantities.totalWallArea_sqm * 10.764,
        numberOfDoors: Math.ceil(geometricQuantities.totalBuiltUpArea / 200),
        numberOfWindows: Math.ceil(geometricQuantities.totalBuiltUpArea / 150),
        numberOfBathrooms: Math.max(1, Math.floor(inputs.numberOfFloors * 1.5)),
        electricalPoints: Math.ceil(geometricQuantities.totalFloorArea_sqm * 0.86),
        plumbingPoints: Math.ceil(geometricQuantities.totalFloorArea_sqm * 0.43),
        steelRatioColumns: 160,
        foundationMultiplier: inputs.hasBasement ? 1.8 : 1.0
      },
      location: inputs.location,
      roomMaterialSelections: [],
      geometricQuantities
    }
  } catch (error) {
    console.error('Error in V2 calculation engine:', error)
    throw error
  }
}

async function calculateSectionsFromComponents(
  context: V2CalculationContext,
  geometricQuantities: any
): Promise<CostSection[]> {
  const sections: CostSection[] = []

  // Foundation & Structure Section
  const foundationItems = await calculateFoundationItems(context, geometricQuantities)
  sections.push({
    id: 'foundation',
    title: 'Foundation & Structure',
    items: foundationItems,
    subtotal: foundationItems.reduce((sum, item) => sum + item.total, 0)
  })

  // Masonry & Finishes Section
  const masonryItems = await calculateMasonryItems(context, geometricQuantities)
  sections.push({
    id: 'masonry',
    title: 'Masonry & Finishes',
    items: masonryItems,
    subtotal: masonryItems.reduce((sum, item) => sum + item.total, 0)
  })

  // Room-wise Customization Section
  const roomItems = await calculateRoomItems(context, geometricQuantities)
  if (roomItems.length > 0) {
    sections.push({
      id: 'rooms',
      title: 'Room Customization',
      items: roomItems,
      subtotal: roomItems.reduce((sum, item) => sum + item.total, 0)
    })
  }

  // MEP Section
  const mepItems = await calculateMEPItems(context, geometricQuantities)
  sections.push({
    id: 'mep',
    title: 'MEP Work',
    items: mepItems,
    subtotal: mepItems.reduce((sum, item) => sum + item.total, 0)
  })

  // Facade Section (if applicable)
  const facadeItems = await calculateFacadeItems(context, geometricQuantities)
  if (facadeItems.length > 0) {
    sections.push({
      id: 'facade',
      title: 'Facade & Exterior',
      items: facadeItems,
      subtotal: facadeItems.reduce((sum, item) => sum + item.total, 0)
    })
  }

  // Professional Fees Section
  const feesItems = calculateProfessionalFees(context, geometricQuantities, sections)
  sections.push({
    id: 'fees',
    title: 'Professional & Regulatory Fees',
    items: feesItems,
    subtotal: feesItems.reduce((sum, item) => sum + item.total, 0)
  })

  return sections
}

/**
 * V2 PROJECT SUMMARY GENERATION
 *
 * Independent project summary generation without V1 dependencies
 */
export function generateV2ProjectSummary(
  inputs: UserInputs,
  result: CalculationResult
): ProjectSummary {
  const structuralOptions: string[] = [];
  if (inputs.hasBasement) structuralOptions.push('Basement');
  if (inputs.hasStiltParking) structuralOptions.push('Stilt Parking');
  if (inputs.numberOfFloors > 2) structuralOptions.push(`${inputs.numberOfFloors} Floors`);

  const structureCost = result.sections.find(s => s.id === 'foundation')?.subtotal || 0;
  const finishesCost = result.sections.find(s => s.id === 'masonry')?.subtotal || 0;
  const mepCost = result.sections.find(s => s.id === 'mep')?.subtotal || 0;
  const feesCost = result.sections.find(s => s.id === 'fees')?.subtotal || 0;

  // Generate room configuration based on built-up area
  const totalBuiltUpArea = result.quantities.totalBuiltUpArea;
  const rooms = generateRoomConfiguration(totalBuiltUpArea);

  return {
    projectOverview: {
      plotSize: inputs.plotSize,
      totalBuiltUpArea: totalBuiltUpArea,
      numberOfFloors: inputs.numberOfFloors,
      constructionType: inputs.numberOfFloors > 2 ? 'Frame Structure' : 'Load Bearing',
      structuralOptions: structuralOptions,
      location: inputs.location
    },
    costBreakdown: {
      structureAndFoundation: structureCost,
      finishesAndMasonry: finishesCost,
      mepAndUtilities: mepCost,
      feesAndApprovals: feesCost,
      totalProjectCost: result.totalCost,
      ratePerSqft: result.ratePerSqft
    },
    technicalSpecifications: {
      foundationDepth: result.quantities.foundationDepth || 2.5,
      slabThickness: result.quantities.slabThickness || 150,
      columnSize: inputs.numberOfFloors <= 2 ? '350x350mm' :
                  inputs.numberOfFloors <= 4 ? '400x400mm' : '450x450mm',
      beamSize: '300x450mm',
      concreteGrade: inputs.numberOfFloors > 3 || inputs.hasBasement ? 'M30' : 'M25',
      steelGrade: 'Fe 500D',
      brickType: 'AAC Blocks 200mm',
      qualityTier: result.qualityTier
    },
    roomConfiguration: rooms,
    materialSummary: {
      concrete: `${Math.round(result.quantities.totalConcreteVolume_m3)} m³`,
      steel: `${Math.round(result.quantities.totalSteel_kg / 1000)} tons`,
      bricks: `${Math.round(result.quantities.totalWallArea_sqm * 110)} pieces`,
      paint: `${Math.round(result.quantities.totalWallArea_sqm * 2.5)} sqm`,
      tiles: `${Math.round(totalBuiltUpArea * 0.6)} sqft`,
      electrical: `${Math.ceil(totalBuiltUpArea * 0.08)} points`,
      plumbing: `${Math.ceil(totalBuiltUpArea * 0.04)} points`
    },
    complianceItems: [
      'Building Plan Approval',
      'Structural Design Approval',
      'Fire NOC (if applicable)',
      'Environmental Clearance (if required)',
      'Completion Certificate'
    ]
  };
}



/**
 * Calculate the total installed cost for a component based on its associated task
 * This includes material cost, labor cost, and any additional materials required
 */
async function calculateInstalledCost(
  component: Component,
  quantity: number,
  context: V2CalculationContext
): Promise<{
  materialCost: number;
  laborCost: number;
  additionalMaterialsCost: number;
  totalInstalledCost: number;
  breakdown: any;
}> {
  // Check for user overrides
  const override = context.userOverrides[component.id];
  const effectiveQuantity = override?.overrideQuantity !== undefined ? override.overrideQuantity : quantity;
  const effectiveRate = override?.overrideRate !== undefined ? override.overrideRate : component.unit_price;
  
  // Apply material multiplier from standards
  const materialMultiplier = context.standards?.regionalData?.materialMultiplier || 1.0;
  const adjustedRate = effectiveRate * materialMultiplier;
  
  // Default to just the material cost if no associated task
  let materialCost = adjustedRate * effectiveQuantity;
  let laborCost = 0;
  let additionalMaterialsCost = 0;
  let breakdown: any = {
    formula: component.associated_task_id 
      ? `Total Installed Cost = Material Cost + Labor Cost + Supporting Materials`
      : `Total Cost = Quantity × Unit Price × Material Multiplier (${materialMultiplier})`,
    components: [
      {
        name: component.name,
        quantity: effectiveQuantity,
        unit: component.unit,
        rate: adjustedRate,
        total: materialCost,
        source: `${component.brand || 'Standard'} ${component.category} (${context.inputs.location} multiplier: ${materialMultiplier})`
      }
    ],
    notes: []
  };

  // Add note if override was applied
  if (override) {
    if (override.overrideQuantity !== undefined) {
      breakdown.notes.push(`Quantity manually overridden from ${override.originalQuantity} to ${override.overrideQuantity}`);
    }
    if (override.overrideRate !== undefined) {
      breakdown.notes.push(`Rate manually overridden from ₹${override.originalRate} to ₹${override.overrideRate}`);
    }
    if (override.reason) {
      breakdown.notes.push(`Override reason: ${override.reason}`);
    }
  }

  // If there's an associated task, calculate full installed cost
  if (component.associated_task_id) {
    const task = context.tasks.find(t => t.id === component.associated_task_id);
    
    if (task && task.task_requirements) {
      // Add task information to breakdown
      breakdown.notes.push(`Installation method: ${task.name}`);
      breakdown.notes.push(`Complexity level: ${task.complexity_level}`);
      
      // Find all requirements for this task
      const requirements = task.task_requirements;
      
      // Process each requirement
      requirements.forEach((req: any) => {
        if (req.requirement_type === 'labor' && req.labor_rates) {
          // Add labor cost based on quality tier
          const laborRate = req.labor_rates.rates[context.qualityTier] || 0;
          // Apply labor multiplier from standards
          const adjustedLaborRate = laborRate * (context.standards?.regionalData?.laborMultiplier || 1.0);
          const laborQuantity = effectiveQuantity * (req.quantity_per_sqm || 1);
          const laborCostForReq = adjustedLaborRate * laborQuantity;
          
          laborCost += laborCostForReq;
          
          // Add to breakdown
          breakdown.components.push({
            name: `${req.labor_rates.name} (${context.qualityTier})`,
            quantity: laborQuantity,
            unit: req.labor_rates.unit,
            rate: adjustedLaborRate,
            total: laborCostForReq,
            source: `Labor for ${task.name} (${context.inputs.location} multiplier: ${context.standards?.regionalData?.laborMultiplier || 1.0})`
          });
          
          // Add productivity information
          if (req.labor_rates.productivity) {
            const { output_per_day, unit } = req.labor_rates.productivity;
            const manDays = laborQuantity / output_per_day;
            breakdown.notes.push(`Labor productivity: ${output_per_day} ${unit}/day, requiring approximately ${manDays.toFixed(1)} man-days`);
          }
        } 
        else if (req.requirement_type === 'material' && req.components) {
          // Skip if this is the main component itself
          if (req.component_id === component.id) return;
          
          // Add supporting material cost
          const materialRate = req.components.unit_price || 0;
          // Apply material multiplier from standards
          const adjustedMaterialRate = materialRate * materialMultiplier;
          const materialQuantity = effectiveQuantity * (req.quantity_per_sqm || 1);
          const materialCostForReq = adjustedMaterialRate * materialQuantity;
          
          additionalMaterialsCost += materialCostForReq;
          
          // Add to breakdown
          breakdown.components.push({
            name: req.components.name,
            quantity: materialQuantity,
            unit: req.components.unit,
            rate: adjustedMaterialRate,
            total: materialCostForReq,
            source: `Supporting material for ${task.name} (${context.inputs.location} multiplier: ${materialMultiplier})`
          });
          
          // Add material specification
          if (req.components.specifications) {
            const specs = Object.entries(req.components.specifications)
              .map(([key, value]) => `${key}: ${value}`)
              .join(', ');
            breakdown.notes.push(`${req.components.name} specifications: ${specs}`);
          }
        }
      });
      
      // Add IS code references based on category
      if (component.category === 'Flooring') {
        breakdown.notes.push('Follows IS 15622:2017 for ceramic/vitrified tiles installation');
      } else if (component.category === 'Masonry') {
        breakdown.notes.push('Follows IS 2212:1991 for brick masonry and IS 2185:2008 for concrete blocks');
      } else if (component.category === 'Windows') {
        breakdown.notes.push('Follows IS 1948:1961 for aluminum and IS 14351-1:2010 for uPVC windows');
      } else if (component.category === 'Bathroom Fittings') {
        breakdown.notes.push('Follows IS 2556 for sanitary appliances and IS 15778:2007 for water fittings');
      }
    }
  }

  const totalInstalledCost = materialCost + laborCost + additionalMaterialsCost;
  
  // Complete the breakdown
  breakdown.materialCost = materialCost;
  breakdown.laborCost = laborCost;
  breakdown.additionalMaterialsCost = additionalMaterialsCost;
  breakdown.finalCost = totalInstalledCost;
  
  return {
    materialCost,
    laborCost,
    additionalMaterialsCost,
    totalInstalledCost,
    breakdown
  };
}

async function calculateFoundationItems(
  context: V2CalculationContext,
  geometricQuantities: any
): Promise<CostItem[]> {
  const items: CostItem[] = []

  // Steel work - find steel component and associated task
  const steelComponent = context.components.find(c => 
    c.category === 'Materials' && c.name.toLowerCase().includes('steel')
  )
  
  if (steelComponent) {
    const quantity = geometricQuantities.totalSteel_kg;
    
    // Calculate full installed cost including labor and supporting materials
    const { totalInstalledCost, breakdown } = await calculateInstalledCost(
      steelComponent,
      quantity,
      context
    );
    
    // Calculate effective rate
    const rate = totalInstalledCost / quantity;
    
    items.push({
      id: steelComponent.id,
      name: `TMT Steel Work (${steelComponent.brand || 'Standard'})`,
      quantity,
      unit: steelComponent.unit,
      rate,
      total: totalInstalledCost,
      category: 'foundation',
      editable: true,
      isCalculated: true,
      materialOptions: context.components
        .filter(c => c.category === 'Materials' && c.name.toLowerCase().includes('steel'))
        .map(c => c.name),
      selectedMaterial: steelComponent.name,
      calculationBreakdown: breakdown,
      isRecommended: context.qualityTier === 'best',
      recommendationNote: 'TATA Tiscon steel offers superior strength and corrosion resistance.'
    })
  }

  // Concrete work
  const cementComponent = context.components.find(c => 
    c.category === 'Materials' && c.name.toLowerCase().includes('cement')
  )
  
  if (cementComponent) {
    const quantity = geometricQuantities.totalConcreteVolume_m3;
    
    // Calculate full installed cost including labor and supporting materials
    const { totalInstalledCost, breakdown } = await calculateInstalledCost(
      cementComponent,
      quantity * 8, // Approximate cement bags per m3
      context
    );
    
    // Calculate effective rate
    const rate = totalInstalledCost / quantity;
    
    items.push({
      id: cementComponent.id,
      name: `RCC Work (${geometricQuantities.concreteGrade})`,
      quantity,
      unit: 'm³',
      rate,
      total: totalInstalledCost,
      category: 'foundation',
      editable: true,
      isCalculated: true,
      calculationBreakdown: breakdown
    })
  }

  // Excavation work
  const excavationVolume = geometricQuantities.concreteVolume_Foundations_m3 * 1.2; // 20% extra for working space
  const excavationRate = 450 * (context.standards?.regionalData?.laborMultiplier || 1.0); // Apply labor multiplier
  
  items.push({
    id: 'excavation_v2',
    name: 'Excavation Work',
    quantity: excavationVolume,
    unit: 'm³',
    rate: excavationRate,
    total: excavationVolume * excavationRate,
    category: 'foundation',
    editable: true,
    isCalculated: true,
    calculationBreakdown: {
      formula: 'Excavation Volume = Foundation Volume × 1.2 (working space)',
      components: [
        {
          name: 'Manual Excavation',
          quantity: excavationVolume,
          unit: 'm³',
          rate: excavationRate,
          total: excavationVolume * excavationRate,
          source: `Standard excavation rates for ${context.inputs.location} (labor multiplier: ${context.standards?.regionalData?.laborMultiplier || 1.0})`
        }
      ],
      materialCost: 0,
      laborCost: excavationVolume * excavationRate,
      finalCost: excavationVolume * excavationRate,
      notes: [
        'Includes excavation, soil disposal, and site preparation',
        'Based on IS 1200 (Part 1) for measurement of excavation work',
        `Rates adjusted for ${context.inputs.location} labor market conditions`
      ]
    }
  })

  return items
}

async function calculateMasonryItems(
  context: V2CalculationContext,
  geometricQuantities: any
): Promise<CostItem[]> {
  const items: CostItem[] = []

  // Masonry work - use default from UI defaults or first available
  const defaultMasonryId = context.defaults?.shell?.externalWalls
  let masonryComponent = defaultMasonryId 
    ? context.components.find(c => c.id === defaultMasonryId)
    : context.components.find(c => c.category === 'Masonry')

  if (masonryComponent) {
    const wallAreaSqm = geometricQuantities.totalWallArea_sqm;
    
    // Calculate full installed cost including labor and supporting materials
    const { totalInstalledCost, breakdown } = await calculateInstalledCost(
      masonryComponent,
      wallAreaSqm,
      context
    );
    
    // Calculate effective rate
    const rate = totalInstalledCost / wallAreaSqm;
    
    items.push({
      id: masonryComponent.id,
      name: `Masonry Work (${masonryComponent.brand || 'Standard'})`,
      quantity: wallAreaSqm,
      unit: masonryComponent.unit,
      rate,
      total: totalInstalledCost,
      category: 'masonry',
      editable: true,
      isCalculated: true,
      materialOptions: context.components
        .filter(c => c.category === 'Masonry')
        .map(c => c.name),
      selectedMaterial: masonryComponent.name,
      calculationBreakdown: breakdown,
      isRecommended: masonryComponent.name.includes('AAC'),
      recommendationNote: 'AAC blocks provide superior thermal insulation and reduce structural load.'
    })
  }

  // Plastering work
  const plasterArea = geometricQuantities.totalWallArea_sqm;
  // Apply material and labor multipliers from standards
  const materialMultiplier = context.standards?.regionalData?.materialMultiplier || 1.0;
  const laborMultiplier = context.standards?.regionalData?.laborMultiplier || 1.0;
  const plasterRate = 85 * ((materialMultiplier * 0.4) + (laborMultiplier * 0.6)); // 40% material, 60% labor
  
  items.push({
    id: 'plastering_v2',
    name: 'Internal + External Plastering',
    quantity: plasterArea,
    unit: 'sqm',
    rate: plasterRate,
    total: plasterArea * plasterRate,
    category: 'masonry',
    editable: true,
    isCalculated: true,
    calculationBreakdown: {
      formula: 'Plaster Area = Total Wall Area',
      components: [
        {
          name: 'Cement Plaster (12mm thick)',
          quantity: plasterArea,
          unit: 'sqm',
          rate: plasterRate,
          total: plasterArea * plasterRate,
          source: `IS 1661:1972 for cement plaster (${context.inputs.location} multipliers: material ${materialMultiplier}, labor ${laborMultiplier})`
        }
      ],
      materialCost: plasterArea * plasterRate * 0.4,
      laborCost: plasterArea * plasterRate * 0.6,
      finalCost: plasterArea * plasterRate,
      notes: [
        'Includes 12mm cement plaster in 1:4 ratio',
        'Based on IS 1661:1972 for cement and sand plaster',
        `Rates adjusted for ${context.inputs.location} market conditions`,
        `Material wastage factor: ${context.standards?.materialConsumption?.wastageFactors?.cement || 5}%`
      ]
    }
  })

  return items
}

async function calculateRoomItems(
  context: V2CalculationContext,
  geometricQuantities: any
): Promise<CostItem[]> {
  const items: CostItem[] = []

  // Generate room-wise items based on defaults
  const roomDefaults = context.defaults?.rooms || {}
  
  for (const [roomType, roomConfig] of Object.entries(roomDefaults)) {
    const flooringComponentId = (roomConfig as any)?.flooring
    const flooringComponent = flooringComponentId 
      ? context.components.find(c => c.id === flooringComponentId)
      : null

    if (flooringComponent) {
      // Estimate room area (simplified)
      const roomArea = geometricQuantities.totalFloorArea_sqm / Object.keys(roomDefaults).length;
      
      // Calculate full installed cost including labor and supporting materials
      const { totalInstalledCost, breakdown } = await calculateInstalledCost(
        flooringComponent,
        roomArea,
        context
      );
      
      // Calculate effective rate
      const rate = totalInstalledCost / roomArea;
      
      items.push({
        id: `${roomType.toLowerCase().replace(' ', '_')}_flooring`,
        name: `${roomType} Flooring (${flooringComponent.name})`,
        quantity: roomArea,
        unit: flooringComponent.unit,
        rate,
        total: totalInstalledCost,
        category: 'rooms',
        editable: true,
        isCalculated: false,
        roomCustomizable: true,
        materialOptions: context.components
          .filter(c => c.category === 'Flooring')
          .map(c => c.name),
        selectedMaterial: flooringComponent.name,
        calculationBreakdown: breakdown
      })
    }

    // Bathroom fittings
    if (roomType === 'Bathroom') {
      const fittingsBundleId = (roomConfig as any)?.fittingsBundle
      const fittingsComponent = fittingsBundleId 
        ? context.components.find(c => c.id === fittingsBundleId)
        : context.components.find(c => c.category === 'Bathroom Fittings' && c.name.includes('Bundle'))

      if (fittingsComponent) {
        const quantity = 1; // One set per bathroom
        
        // Calculate full installed cost including labor and supporting materials
        const { totalInstalledCost, breakdown } = await calculateInstalledCost(
          fittingsComponent,
          quantity,
          context
        );
        
        // Calculate effective rate
        const rate = totalInstalledCost / quantity;
        
        items.push({
          id: 'bathroom_fittings_v2',
          name: `Bathroom Fittings (${fittingsComponent.name})`,
          quantity,
          unit: fittingsComponent.unit,
          rate,
          total: totalInstalledCost,
          category: 'rooms',
          editable: true,
          isCalculated: false,
          roomCustomizable: true,
          materialOptions: context.components
            .filter(c => c.category === 'Bathroom Fittings')
            .map(c => c.name),
          selectedMaterial: fittingsComponent.name,
          calculationBreakdown: breakdown
        })
      }
    }
  }

  return items
}

async function calculateMEPItems(
  context: V2CalculationContext,
  geometricQuantities: any
): Promise<CostItem[]> {
  const items: CostItem[] = []

  // Electrical work
  const electricalPoints = Math.ceil(geometricQuantities.totalFloorArea_sqm * 0.86);
  
  // Find electrical components and associated tasks
  const electricalComponent = context.components.find(c => 
    c.category === 'Electrical' || (c.category === 'Materials' && c.name.toLowerCase().includes('electrical'))
  );
  
  if (electricalComponent) {
    // Calculate full installed cost including labor and supporting materials
    const { totalInstalledCost, breakdown } = await calculateInstalledCost(
      electricalComponent,
      electricalPoints,
      context
    );
    
    // Calculate effective rate
    const rate = totalInstalledCost / electricalPoints;
    
    items.push({
      id: 'electrical_v2',
      name: 'Electrical Work (Complete)',
      quantity: electricalPoints,
      unit: 'points',
      rate,
      total: totalInstalledCost,
      category: 'electrical',
      editable: true,
      isCalculated: false,
      roomCustomizable: true,
      calculationBreakdown: breakdown
    })
  } else {
    // Fallback if no component found
    const electricalRate = 350 * (context.standards?.regionalData?.laborMultiplier || 1.0); // Apply labor multiplier
    
    items.push({
      id: 'electrical_v2',
      name: 'Electrical Work (Complete)',
      quantity: electricalPoints,
      unit: 'points',
      rate: electricalRate,
      total: electricalPoints * electricalRate,
      category: 'electrical',
      editable: true,
      isCalculated: false,
      roomCustomizable: true
    })
  }

  // Plumbing work
  const plumbingPoints = Math.ceil(geometricQuantities.totalFloorArea_sqm * 0.43);
  
  // Find plumbing components and associated tasks
  const plumbingComponent = context.components.find(c => 
    c.category === 'Plumbing' || (c.category === 'Materials' && c.name.toLowerCase().includes('plumbing'))
  );
  
  if (plumbingComponent) {
    // Calculate full installed cost including labor and supporting materials
    const { totalInstalledCost, breakdown } = await calculateInstalledCost(
      plumbingComponent,
      plumbingPoints,
      context
    );
    
    // Calculate effective rate
    const rate = totalInstalledCost / plumbingPoints;
    
    items.push({
      id: 'plumbing_v2',
      name: 'Plumbing Work (Complete)',
      quantity: plumbingPoints,
      unit: 'points',
      rate,
      total: totalInstalledCost,
      category: 'plumbing',
      editable: true,
      isCalculated: false,
      roomCustomizable: true,
      calculationBreakdown: breakdown
    })
  } else {
    // Fallback if no component found
    const plumbingRate = 500 * (context.standards?.regionalData?.laborMultiplier || 1.0); // Apply labor multiplier
    
    items.push({
      id: 'plumbing_v2',
      name: 'Plumbing Work (Complete)',
      quantity: plumbingPoints,
      unit: 'points',
      rate: plumbingRate,
      total: plumbingPoints * plumbingRate,
      category: 'plumbing',
      editable: true,
      isCalculated: false,
      roomCustomizable: true
    })
  }

  return items
}

async function calculateFacadeItems(
  context: V2CalculationContext,
  geometricQuantities: any
): Promise<CostItem[]> {
  const items: CostItem[] = []

  // Facade finish - use default from UI defaults
  const defaultFacadeId = context.defaults?.facade?.primaryFinish
  const facadeComponent = defaultFacadeId 
    ? context.components.find(c => c.id === defaultFacadeId)
    : context.components.find(c => c.category === 'Facade')

  if (facadeComponent) {
    const externalWallArea = geometricQuantities.externalWallArea_sqm || 
                            (geometricQuantities.totalWallArea_sqm * 0.4); // Approximate if not available
    
    // Calculate full installed cost including labor and supporting materials
    const { totalInstalledCost, breakdown } = await calculateInstalledCost(
      facadeComponent,
      externalWallArea,
      context
    );
    
    // Calculate effective rate
    const rate = totalInstalledCost / externalWallArea;
    
    items.push({
      id: facadeComponent.id,
      name: `Facade Finish (${facadeComponent.name})`,
      quantity: externalWallArea,
      unit: facadeComponent.unit,
      rate,
      total: totalInstalledCost,
      category: 'facade',
      editable: true,
      isCalculated: false,
      materialOptions: context.components
        .filter(c => c.category === 'Facade')
        .map(c => c.name),
      selectedMaterial: facadeComponent.name,
      calculationBreakdown: breakdown
    })
  }

  return items
}

/**
 * Calculate professional fees based on construction cost and standards
 */
function calculateProfessionalFees(
  context: V2CalculationContext,
  geometricQuantities: any,
  sections: CostSection[]
): CostItem[] {
  const items: CostItem[] = []
  
  // Calculate construction cost (excluding fees)
  const constructionCost = sections.reduce((sum, section) => sum + section.subtotal, 0)
  
  // Architect fees from standards based on quality tier
  const architectFeePercentage = context.qualityTier === 'best'
    ? (context.standards?.regionalData?.professionalFees?.architect?.tier3 || 4) / 100
    : context.qualityTier === 'better'
    ? (context.standards?.regionalData?.professionalFees?.architect?.tier2 || 3.5) / 100
    : (context.standards?.regionalData?.professionalFees?.architect?.tier1 || 3) / 100;
    
  const architectFee = constructionCost * architectFeePercentage;
  
  items.push({
    id: 'architect_fees_v2',
    name: 'Architect & Engineering Fees',
    quantity: 1,
    unit: 'lump sum',
    rate: architectFee,
    total: architectFee,
    category: 'fees',
    editable: true,
    isCalculated: false,
    calculationBreakdown: {
      formula: `Architect Fees = ${(architectFeePercentage * 100).toFixed(1)}% of Construction Cost`,
      components: [
        {
          name: 'Architectural Design & Drawings',
          quantity: 1,
          unit: 'lump sum',
          rate: architectFee * 0.6,
          total: architectFee * 0.6,
          source: 'Council of Architecture recommended rates'
        },
        {
          name: 'Structural Engineering',
          quantity: 1,
          unit: 'lump sum',
          rate: architectFee * 0.3,
          total: architectFee * 0.3,
          source: 'Industry standard for structural design'
        },
        {
          name: 'MEP Engineering',
          quantity: 1,
          unit: 'lump sum',
          rate: architectFee * 0.1,
          total: architectFee * 0.1,
          source: 'Industry standard for MEP design'
        }
      ],
      materialCost: 0,
      laborCost: architectFee,
      finalCost: architectFee,
      notes: [
        'Based on Council of Architecture recommended fee structure',
        'Includes architectural design, working drawings, and site visits',
        'Structural and MEP engineering included'
      ]
    }
  })
  
  // Approval fees from standards
  const builtUpAreaSqm = geometricQuantities.totalBuiltUpArea / 10.764 // Convert to sqm
  const approvalFeeRate = context.standards?.regionalData?.approvalFeeRate || 400;
  
  const approvalFee = builtUpAreaSqm * approvalFeeRate;
  
  items.push({
    id: 'approval_fees_v2',
    name: 'Municipal Approval Fees',
    quantity: builtUpAreaSqm,
    unit: 'sqm',
    rate: approvalFeeRate,
    total: approvalFee,
    category: 'fees',
    editable: true,
    isCalculated: false,
    calculationBreakdown: {
      formula: `Approval Fees = Built-up Area × Rate per sqm`,
      components: [
        {
          name: 'Building Plan Approval',
          quantity: builtUpAreaSqm,
          unit: 'sqm',
          rate: approvalFeeRate,
          total: approvalFee,
          source: `${context.standards?.regionalData?.authority || 'Municipal'} municipal rates`
        }
      ],
      materialCost: 0,
      laborCost: 0,
      finalCost: approvalFee,
      notes: [
        `Based on ${context.standards?.regionalData?.authority || 'Municipal'} municipal corporation rates`,
        'Includes building plan sanction fees',
        'Additional fees may apply for NOCs and other clearances'
      ]
    }
  })
  
  return items
}

/**
 * Helper function to apply user overrides to calculated items
 */
export function applyUserOverrides(
  items: CostItem[],
  overrides: Record<string, any>
): CostItem[] {
  return items.map(item => {
    const override = overrides[item.id]
    if (override) {
      return {
        ...item,
        quantity: override.quantity ?? item.quantity,
        rate: override.rate ?? item.rate,
        total: (override.quantity ?? item.quantity) * (override.rate ?? item.rate),
        selectedMaterial: override.selectedMaterial ?? item.selectedMaterial
      }
    }
    return item
  })
}

/**
 * Helper function to get component by ID with fallback
 */
export function getComponentWithFallback(
  components: Component[],
  componentId: string,
  category: string
): Component | null {
  return components.find(c => c.id === componentId) || 
         components.find(c => c.category === category) ||
         null
}