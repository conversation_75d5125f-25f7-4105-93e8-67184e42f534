import React, { useState, useEffect } from 'react';
import { X, AlertTriangle, Save, Loader, Info } from 'lucide-react';
import { CostItem } from '../types/calculator';

interface ItemOverrideModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: CostItem;
  originalData: {
    originalQuantity: number;
    originalRate: number;
    overrideQuantity?: number;
    overrideRate?: number;
    reason?: string;
  };
  onSave: (overrideData: {
    overrideQuantity?: number;
    overrideRate?: number;
    reason?: string;
  }) => void;
  isSaving?: boolean;
}

export function ItemOverrideModal({ 
  isOpen, 
  onClose, 
  item, 
  originalData, 
  onSave,
  isSaving = false
}: ItemOverrideModalProps) {
  const [overrideQuantity, setOverrideQuantity] = useState<string>('');
  const [overrideRate, setOverrideRate] = useState<string>('');
  const [reason, setReason] = useState('');
  const [showEngineeringInfo, setShowEngineeringInfo] = useState(false);

  useEffect(() => {
    if (isOpen && originalData) {
      setOverrideQuantity(originalData.overrideQuantity?.toString() || '');
      setOverrideRate(originalData.overrideRate?.toString() || '');
      setReason(originalData.reason || '');
    }
  }, [isOpen, originalData]);

  if (!isOpen) return null;

  const handleSave = () => {
    const overrideData: any = {};
    
    if (overrideQuantity && Number(overrideQuantity) !== originalData.originalQuantity) {
      overrideData.overrideQuantity = Number(overrideQuantity);
    }
    
    if (overrideRate && Number(overrideRate) !== originalData.originalRate) {
      overrideData.overrideRate = Number(overrideRate);
    }
    
    if (reason.trim()) {
      overrideData.reason = reason.trim();
    }

    onSave(overrideData);
  };

  const calculateNewTotal = () => {
    const qty = overrideQuantity ? Number(overrideQuantity) : originalData.originalQuantity;
    const rate = overrideRate ? Number(overrideRate) : originalData.originalRate;
    return qty * rate;
  };

  const originalTotal = originalData.originalQuantity * originalData.originalRate;
  const newTotal = calculateNewTotal();
  const costDifference = newTotal - originalTotal;
  const percentageDifference = originalTotal > 0 ? (costDifference / originalTotal) * 100 : 0;

  // Engineering information based on item category
  const getEngineeringInfo = () => {
    switch (item.category) {
      case 'foundation':
        return {
          title: 'Foundation & Structure Engineering Standards',
          content: [
            'Concrete mix design follows IS 10262:2019',
            'Steel reinforcement as per IS 456:2000',
            'Structural design based on IS 875 for loads and IS 1893 for seismic design',
            'Quality control as per IS 4926:2003 for ready-mixed concrete'
          ]
        };
      case 'masonry':
        return {
          title: 'Masonry Engineering Standards',
          content: [
            'Brick masonry follows IS 2212:1991',
            'AAC blocks follow IS 2185 (Part 3):1984',
            'Mortar specifications as per IS 2250:1981',
            'Plastering standards follow IS 1661:1972'
          ]
        };
      case 'rooms':
        return {
          title: 'Flooring & Finishing Engineering Standards',
          content: [
            'Tile installation follows IS 15622:2017',
            'Natural stone installation follows IS 14223',
            'Waterproofing for wet areas follows IS 13182:1991',
            'Material consumption ratios based on CPWD specifications'
          ]
        };
      case 'electrical':
        return {
          title: 'Electrical Engineering Standards',
          content: [
            'Wiring follows IS 732:2019 for electrical installations',
            'Conduit specifications as per IS 9537',
            'Load calculation based on connected load plus 40% future provision',
            'Point density follows National Building Code recommendations'
          ]
        };
      case 'plumbing':
        return {
          title: 'Plumbing Engineering Standards',
          content: [
            'Pipe sizing follows IS 2065:1983',
            'Fixture requirements based on IS 1172:1993',
            'Water supply design as per IS 2065:1983',
            'Sanitary design follows IS 1742:1983'
          ]
        };
      default:
        return {
          title: 'Engineering Standards',
          content: [
            'All calculations follow relevant Indian Standard (IS) codes',
            'Material quantities include standard wastage percentages',
            'Labor productivity based on CPWD norms',
            'Quality specifications follow industry best practices'
          ]
        };
    }
  };

  const engineeringInfo = getEngineeringInfo();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-yellow-50 to-orange-50">
          <div>
            <h2 className="text-xl font-bold text-gray-800">Override Item Pricing</h2>
            <p className="text-gray-600 mt-1">{item.name}</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isSaving}
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Warning */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-yellow-800">Manual Override Warning</h3>
                <p className="text-yellow-700 text-sm mt-1">
                  You are manually overriding auto-calculated values. This may affect the accuracy of your total estimate.
                  The original values are calculated from engineering first principles and market rates.
                </p>
              </div>
            </div>
          </div>

          {/* Current Values */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-semibold text-gray-800 mb-3">Current Auto-Calculated Values</h4>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Quantity:</span>
                <div className="font-semibold text-gray-800">
                  {originalData.originalQuantity.toFixed(2)} {item.unit}
                </div>
              </div>
              <div>
                <span className="text-gray-600">Rate:</span>
                <div className="font-semibold text-gray-800">
                  ₹{originalData.originalRate.toFixed(2)}/{item.unit}
                </div>
              </div>
              <div>
                <span className="text-gray-600">Total:</span>
                <div className="font-semibold text-gray-800">
                  ₹{originalTotal.toLocaleString()}
                </div>
              </div>
            </div>
          </div>

          {/* Engineering Information Toggle */}
          <div>
            <button
              onClick={() => setShowEngineeringInfo(!showEngineeringInfo)}
              className="flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
            >
              <Info className="w-4 h-4" />
              <span className="text-sm font-medium">
                {showEngineeringInfo ? 'Hide Engineering Information' : 'Show Engineering Information'}
              </span>
            </button>
            
            {showEngineeringInfo && (
              <div className="mt-3 bg-blue-50 rounded-lg p-4 border border-blue-200">
                <h5 className="font-semibold text-blue-800 mb-2">{engineeringInfo.title}</h5>
                <ul className="space-y-1">
                  {engineeringInfo.content.map((info, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm text-blue-700">
                      <span className="text-blue-500 mt-1">•</span>
                      <span>{info}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-3 text-xs text-blue-600">
                  These engineering standards ensure structural integrity, safety, and compliance with Indian building codes.
                </div>
              </div>
            )}
          </div>

          {/* Override Inputs */}
          <div className="space-y-4">
            <h4 className="font-semibold text-gray-800">Override Values</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Override Quantity
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={overrideQuantity}
                    onChange={(e) => setOverrideQuantity(e.target.value)}
                    placeholder={originalData.originalQuantity.toString()}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    step="0.01"
                    min="0"
                    disabled={isSaving}
                  />
                  <span className="absolute right-3 top-2 text-sm text-gray-500">
                    {item.unit}
                  </span>
                </div>
                {overrideQuantity && Number(overrideQuantity) !== originalData.originalQuantity && (
                  <div className="mt-1 text-xs">
                    <span className="text-gray-500 line-through">
                      {originalData.originalQuantity.toFixed(2)}
                    </span>
                    <span className="text-blue-600 font-semibold ml-2">
                      → {Number(overrideQuantity).toFixed(2)}
                    </span>
                    <span className="text-gray-500 ml-2">
                      ({Number(overrideQuantity) > originalData.originalQuantity ? '+' : ''}
                      {(((Number(overrideQuantity) - originalData.originalQuantity) / originalData.originalQuantity) * 100).toFixed(1)}%)
                    </span>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Override Rate
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={overrideRate}
                    onChange={(e) => setOverrideRate(e.target.value)}
                    placeholder={originalData.originalRate.toString()}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    step="0.01"
                    min="0"
                    disabled={isSaving}
                  />
                  <span className="absolute right-3 top-2 text-sm text-gray-500">
                    ₹/{item.unit}
                  </span>
                </div>
                {overrideRate && Number(overrideRate) !== originalData.originalRate && (
                  <div className="mt-1 text-xs">
                    <span className="text-gray-500 line-through">
                      ₹{originalData.originalRate.toFixed(2)}
                    </span>
                    <span className="text-blue-600 font-semibold ml-2">
                      → ₹{Number(overrideRate).toFixed(2)}
                    </span>
                    <span className="text-gray-500 ml-2">
                      ({Number(overrideRate) > originalData.originalRate ? '+' : ''}
                      {(((Number(overrideRate) - originalData.originalRate) / originalData.originalRate) * 100).toFixed(1)}%)
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reason for Override (Optional)
              </label>
              <textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="e.g., Got a better quote from local supplier, Different specification required, etc."
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                disabled={isSaving}
              />
            </div>
          </div>

          {/* Cost Impact */}
          {(overrideQuantity || overrideRate) && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2">Cost Impact Analysis</h4>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-blue-700">Original Total:</span>
                  <div className="font-semibold text-blue-800">
                    ₹{originalTotal.toLocaleString()}
                  </div>
                </div>
                <div>
                  <span className="text-blue-700">New Total:</span>
                  <div className="font-semibold text-blue-800">
                    ₹{newTotal.toLocaleString()}
                  </div>
                </div>
                <div>
                  <span className="text-blue-700">Difference:</span>
                  <div className={`font-semibold ${
                    costDifference > 0 ? 'text-red-600' : costDifference < 0 ? 'text-green-600' : 'text-blue-800'
                  }`}>
                    {costDifference > 0 ? '+' : ''}₹{costDifference.toLocaleString()}
                    <span className="block text-xs">
                      ({percentageDifference > 0 ? '+' : ''}{percentageDifference.toFixed(1)}%)
                    </span>
                  </div>
                </div>
              </div>
              
              {/* Impact assessment */}
              <div className="mt-3 p-2 bg-white rounded border border-blue-100">
                <p className="text-sm text-gray-700">
                  <span className="font-medium">Impact Assessment: </span>
                  {Math.abs(percentageDifference) < 5 
                    ? 'Minor impact on overall budget.' 
                    : Math.abs(percentageDifference) < 15
                    ? 'Moderate impact on overall budget. Consider reviewing other items to maintain balance.'
                    : 'Significant impact on overall budget. Ensure you have valid reasons for this override.'}
                </p>
              </div>
            </div>
          )}

          {/* Common Override Reasons */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-semibold text-gray-800 mb-2">Common Override Reasons</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {[
                'Got a better quote from local supplier',
                'Different specification required',
                'Material already purchased separately',
                'Using existing inventory',
                'Special discount from vendor',
                'Higher quality material preferred',
                'Different brand selected'
              ].map((commonReason) => (
                <button
                  key={commonReason}
                  onClick={() => setReason(commonReason)}
                  className="text-left text-sm px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors"
                  disabled={isSaving}
                >
                  {commonReason}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            {isSaving ? 'Saving override to database...' : 'Changes will be applied to your estimate immediately'}
          </div>
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              disabled={isSaving}
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={isSaving || (!overrideQuantity && !overrideRate && !reason)}
              className="flex items-center gap-2 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors disabled:bg-blue-400"
            >
              {isSaving ? (
                <>
                  <Loader className="w-4 h-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Apply Override
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}