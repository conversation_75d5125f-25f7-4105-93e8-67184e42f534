import React, { useState, useEffect } from 'react';
import { X, Plus, Minus, Home, Bath, ChefHat, Bed, AlertCircle, CheckCircle } from 'lucide-react';
import { RoomConfiguration } from '../types/calculator';

interface RoomPlannerProps {
  isOpen: boolean;
  onClose: () => void;
  totalBuiltUpArea: number;
  initialRooms: RoomConfiguration[];
  onSave: (rooms: RoomConfiguration[]) => void;
}

const roomTypeIcons = {
  'Bedroom': Bed,
  'Living Room': Home,
  'Kitchen': ChefHat,
  'Bathroom': Bath,
  'Dining Room': Home,
  'Study Room': Bed,
  'Balcony': Home,
  'Store Room': Home
};

const defaultRoomTypes = [
  { type: 'Bedroom', defaultArea: 150 },
  { type: 'Living Room', defaultArea: 250 },
  { type: 'Kitchen', defaultArea: 120 },
  { type: 'Bathroom', defaultArea: 60 },
  { type: 'Dining Room', defaultArea: 120 },
  { type: 'Study Room', defaultArea: 100 },
  { type: 'Balcony', defaultArea: 80 },
  { type: 'Store Room', defaultArea: 50 }
];

export function RoomPlanner({ isOpen, onClose, totalBuiltUpArea, initialRooms, onSave }: RoomPlannerProps) {
  const [rooms, setRooms] = useState<RoomConfiguration[]>(initialRooms);
  const [errors, setErrors] = useState<string[]>([]);

  useEffect(() => {
    setRooms(initialRooms);
  }, [initialRooms]);

  if (!isOpen) return null;

  const updateRoom = (id: string, updates: Partial<RoomConfiguration>) => {
    setRooms(prev => prev.map(room => 
      room.id === id 
        ? { ...room, ...updates, totalArea: (updates.count ?? room.count) * (updates.areaPerRoom ?? room.areaPerRoom) }
        : room
    ));
  };

  const addRoom = (roomType: string) => {
    const defaultRoom = defaultRoomTypes.find(r => r.type === roomType);
    const newRoom: RoomConfiguration = {
      id: `room-${Date.now()}`,
      type: roomType,
      count: 1,
      areaPerRoom: defaultRoom?.defaultArea || 100,
      totalArea: defaultRoom?.defaultArea || 100
    };
    setRooms(prev => [...prev, newRoom]);
  };

  const removeRoom = (id: string) => {
    setRooms(prev => prev.filter(room => room.id !== id));
  };

  const totalCalculatedArea = rooms.reduce((sum, room) => sum + room.totalArea, 0);
  const areaDiscrepancy = Math.abs(totalCalculatedArea - totalBuiltUpArea);
  const isAreaValid = areaDiscrepancy <= 50; // Allow 50 sq ft variance
  const discrepancyPercentage = (areaDiscrepancy / totalBuiltUpArea) * 100;

  const handleSave = () => {
    const newErrors: string[] = [];
    
    if (!isAreaValid) {
      newErrors.push(`Total room area (${totalCalculatedArea.toLocaleString()} sq ft) should match built-up area (${totalBuiltUpArea.toLocaleString()} sq ft)`);
    }
    
    if (rooms.some(room => room.count <= 0 || room.areaPerRoom <= 0)) {
      newErrors.push('All rooms must have positive count and area values');
    }

    setErrors(newErrors);

    if (newErrors.length === 0) {
      onSave(rooms);
      onClose();
    }
  };

  const availableRoomTypes = defaultRoomTypes.filter(
    defaultRoom => !rooms.some(room => room.type === defaultRoom.type)
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Room Configuration</h2>
            <p className="text-gray-600 mt-1">Customize your room layout and areas</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Area Summary */}
          <div className={`rounded-lg p-4 mb-6 ${isAreaValid ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {isAreaValid ? (
                  <CheckCircle className="w-6 h-6 text-green-600" />
                ) : (
                  <AlertCircle className="w-6 h-6 text-red-600" />
                )}
                <div>
                  <h3 className={`font-semibold ${isAreaValid ? 'text-green-800' : 'text-red-800'}`}>
                    Area Summary
                  </h3>
                  <p className={`text-sm ${isAreaValid ? 'text-green-600' : 'text-red-600'}`}>
                    Target Built-Up Area: {totalBuiltUpArea.toLocaleString()} sq ft
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-2xl font-bold ${isAreaValid ? 'text-green-600' : 'text-red-600'}`}>
                  {totalCalculatedArea.toLocaleString()} sq ft
                </div>
                <div className="text-sm text-gray-600">
                  Difference: {areaDiscrepancy.toLocaleString()} sq ft ({discrepancyPercentage.toFixed(1)}%)
                </div>
              </div>
            </div>
          </div>

          {/* Error Messages */}
          {errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <h4 className="font-semibold text-red-800 mb-2">Please fix the following issues:</h4>
              <ul className="list-disc list-inside text-red-600 text-sm space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Room List */}
          <div className="space-y-4 mb-6">
            {rooms.map((room) => {
              const IconComponent = roomTypeIcons[room.type as keyof typeof roomTypeIcons] || Home;
              
              return (
                <div key={room.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="grid grid-cols-1 md:grid-cols-6 gap-4 items-center">
                    {/* Room Type */}
                    <div className="md:col-span-2 flex items-center gap-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <IconComponent className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-800">{room.type}</div>
                        <div className="text-sm text-gray-500">
                          Total: {room.totalArea.toLocaleString()} sq ft
                        </div>
                      </div>
                    </div>

                    {/* Count */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Count</label>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => updateRoom(room.id, { count: Math.max(1, room.count - 1) })}
                          className="p-1 hover:bg-gray-100 rounded transition-colors"
                        >
                          <Minus className="w-4 h-4" />
                        </button>
                        <input
                          type="number"
                          value={room.count}
                          onChange={(e) => updateRoom(room.id, { count: Math.max(1, Number(e.target.value)) })}
                          className="w-16 px-2 py-1 border border-gray-200 rounded text-center text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          min="1"
                        />
                        <button
                          onClick={() => updateRoom(room.id, { count: room.count + 1 })}
                          className="p-1 hover:bg-gray-100 rounded transition-colors"
                        >
                          <Plus className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    {/* Area per Room */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Area per Room</label>
                      <div className="flex items-center gap-2">
                        <input
                          type="number"
                          value={room.areaPerRoom}
                          onChange={(e) => updateRoom(room.id, { areaPerRoom: Math.max(1, Number(e.target.value)) })}
                          className="w-20 px-2 py-1 border border-gray-200 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          min="1"
                        />
                        <span className="text-sm text-gray-500">sq ft</span>
                      </div>
                    </div>

                    {/* Total Area (Read-only) */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Total Area</label>
                      <div className="px-2 py-1 bg-gray-50 border border-gray-200 rounded text-sm text-gray-600 text-center">
                        {room.totalArea.toLocaleString()}
                      </div>
                    </div>

                    {/* Remove Button */}
                    <div className="flex justify-end">
                      <button
                        onClick={() => removeRoom(room.id)}
                        className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                        title="Remove room"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Add Room Section */}
          {availableRoomTypes.length > 0 && (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              <h4 className="font-semibold text-gray-800 mb-3">Add More Rooms</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {availableRoomTypes.map((roomType) => {
                  const IconComponent = roomTypeIcons[roomType.type as keyof typeof roomTypeIcons] || Home;
                  
                  return (
                    <button
                      key={roomType.type}
                      onClick={() => addRoom(roomType.type)}
                      className="flex items-center gap-2 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-blue-300 transition-all"
                    >
                      <IconComponent className="w-4 h-4 text-gray-600" />
                      <span className="text-sm text-gray-700">{roomType.type}</span>
                    </button>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm">
            {isAreaValid ? (
              <span className="flex items-center gap-2 text-green-600">
                <CheckCircle className="w-4 h-4" />
                Area allocation is valid
              </span>
            ) : (
              <span className="flex items-center gap-2 text-red-600">
                <AlertCircle className="w-4 h-4" />
                Please adjust room areas to match total built-up area
              </span>
            )}
          </div>
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={!isAreaValid}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
            >
              Save Configuration
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}