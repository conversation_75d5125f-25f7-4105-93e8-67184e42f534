/*
  # Fix Plumbing Components and UI Issues

  1. New Components
    - Add proper plumbing components with correct pricing
    - Add bathroom fixture components with detailed specifications
    - Add electrical components with proper pricing
    - Fix wall finish components to avoid duplication

  2. Task Requirements
    - Create proper plumbing installation tasks
    - Link components to appropriate tasks
    - Set realistic quantities and labor requirements
*/

-- Add proper plumbing components
INSERT INTO components (name, category, sub_category, brand, cost_model, unit_price, unit, image_url, specifications) VALUES
-- Plumbing pipes and fittings
('CPVC Pipe Bundle', 'Plumbing', 'Pipes', 'Astral', 'per_point', 1200, 'point', 'https://images.pexels.com/photos/2219024/pexels-photo-2219024.jpeg', 
 '{"diameter": "15mm-25mm", "length": "Average 8m per point", "pressure_rating": "SDR 11", "temperature": "93°C"}'),
('UPVC Drainage Pipes', 'Plumbing', 'Drainage', 'Supreme', 'per_point', 850, 'point', 'https://images.pexels.com/photos/2219024/pexels-photo-2219024.jpeg', 
 '{"diameter": "75mm-110mm", "length": "Average 3m per point", "type": "SWR", "standard": "IS 13592"}'),
('Bathroom Fittings Basic', 'Plumbing', 'Fixtures', 'Cera', 'per_set', 8500, 'set', 'https://images.pexels.com/photos/1457847/pexels-photo-1457847.jpeg', 
 '{"includes": "WC, basin, shower, taps", "quality": "Standard", "warranty": "5 years"}'),
('Bathroom Fittings Standard', 'Plumbing', 'Fixtures', 'Hindware', 'per_set', 12500, 'set', 'https://images.pexels.com/photos/1457847/pexels-photo-1457847.jpeg', 
 '{"includes": "WC, basin, shower, taps", "quality": "Premium", "warranty": "7 years"}'),
('Bathroom Fittings Premium', 'Plumbing', 'Fixtures', 'Jaquar', 'per_set', 18500, 'set', 'https://images.pexels.com/photos/1457847/pexels-photo-1457847.jpeg', 
 '{"includes": "WC, basin, shower, taps", "quality": "Luxury", "warranty": "10 years"}');

-- Add proper electrical components with realistic pricing
INSERT INTO components (name, category, sub_category, brand, cost_model, unit_price, unit, image_url, specifications) VALUES
('Electrical Point Basic', 'Electrical', 'Wiring', 'Anchor', 'per_point', 350, 'point', 'https://images.pexels.com/photos/3201763/pexels-photo-3201763.jpeg', 
 '{"includes": "Wiring, conduit, switch/socket", "wire_type": "FR-PVC", "brand": "Anchor Roma", "warranty": "2 years"}'),
('Electrical Point Standard', 'Electrical', 'Wiring', 'Havells', 'per_point', 450, 'point', 'https://images.pexels.com/photos/3201763/pexels-photo-3201763.jpeg', 
 '{"includes": "Wiring, conduit, switch/socket", "wire_type": "FRLS", "brand": "Havells", "warranty": "5 years"}'),
('Electrical Point Premium', 'Electrical', 'Wiring', 'Legrand', 'per_point', 650, 'point', 'https://images.pexels.com/photos/3201763/pexels-photo-3201763.jpeg', 
 '{"includes": "Wiring, conduit, switch/socket", "wire_type": "FRLSH", "brand": "Legrand", "warranty": "10 years"}');

-- Add proper wall finish components
INSERT INTO components (name, category, sub_category, brand, cost_model, unit_price, unit, image_url, specifications) VALUES
('Asian Paints Tractor', 'Wall Finishes', 'Economy Emulsion', 'Asian Paints', 'per_sqm', 25, 'sqm', 'https://images.pexels.com/photos/1669754/pexels-photo-1669754.jpeg', 
 '{"type": "Economy Emulsion", "finish": "Matte", "washability": "Low", "coverage": "120-140 sqft/liter"}'),
('Asian Paints Royale', 'Wall Finishes', 'Luxury Emulsion', 'Asian Paints', 'per_sqm', 50, 'sqm', 'https://images.pexels.com/photos/1669754/pexels-photo-1669754.jpeg', 
 '{"type": "Luxury Emulsion", "finish": "Velvet Touch", "washability": "High", "coverage": "110-130 sqft/liter"}'),
('Berger Silk Luxury', 'Wall Finishes', 'Luxury Emulsion', 'Berger', 'per_sqm', 45, 'sqm', 'https://images.pexels.com/photos/1669754/pexels-photo-1669754.jpeg', 
 '{"type": "Luxury Emulsion", "finish": "Smooth", "washability": "High", "coverage": "110-130 sqft/liter"}');

-- Create plumbing installation task
INSERT INTO tasks (name, description, category, complexity_level, estimated_duration_hours) VALUES
('Install Bathroom Fittings', 'Complete bathroom fittings installation including fixtures and plumbing', 'plumbing', 'intermediate', 16),
('Install Plumbing Lines', 'Installation of water supply and drainage lines', 'plumbing', 'intermediate', 12);

-- Link components to tasks
UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Plumbing Lines' LIMIT 1) 
WHERE name = 'CPVC Pipe Bundle';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Plumbing Lines' LIMIT 1) 
WHERE name = 'UPVC Drainage Pipes';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Bathroom Fittings' LIMIT 1) 
WHERE name = 'Bathroom Fittings Basic';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Bathroom Fittings' LIMIT 1) 
WHERE name = 'Bathroom Fittings Standard';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Bathroom Fittings' LIMIT 1) 
WHERE name = 'Bathroom Fittings Premium';

-- Create task requirements for plumbing
INSERT INTO task_requirements (task_id, component_id, labor_id, quantity_per_sqm, requirement_type) VALUES
-- Plumbing installation requirements
((SELECT id FROM tasks WHERE name = 'Install Plumbing Lines' LIMIT 1), 
 (SELECT id FROM components WHERE name = 'CPVC Pipe Bundle' LIMIT 1), 
 NULL, 1.0, 'material'),
((SELECT id FROM tasks WHERE name = 'Install Plumbing Lines' LIMIT 1), 
 (SELECT id FROM components WHERE name = 'UPVC Drainage Pipes' LIMIT 1), 
 NULL, 0.5, 'material'),
((SELECT id FROM tasks WHERE name = 'Install Plumbing Lines' LIMIT 1), 
 NULL, 
 (SELECT id FROM labor_rates WHERE name = 'Plumbing Fitting Installation' LIMIT 1), 1.0, 'labor');

-- Add labor rate for plumbing if not exists
INSERT INTO labor_rates (name, category, skill_level, unit, rates, productivity, location)
SELECT 'Plumbing Fitting Installation', 'plumbing', 'skilled', 'point', '{"good": 200, "better": 250, "best": 300}', '{"output_per_day": 6, "unit": "points"}', 'gurgaon'
WHERE NOT EXISTS (SELECT 1 FROM labor_rates WHERE name = 'Plumbing Fitting Installation');

-- Add labor rate for electrical if not exists
INSERT INTO labor_rates (name, category, skill_level, unit, rates, productivity, location)
SELECT 'Electrical Installation', 'electrical', 'skilled', 'point', '{"good": 150, "better": 180, "best": 210}', '{"output_per_day": 10, "unit": "points"}', 'gurgaon'
WHERE NOT EXISTS (SELECT 1 FROM labor_rates WHERE name = 'Electrical Installation');

-- Create electrical installation task
INSERT INTO tasks (name, description, category, complexity_level, estimated_duration_hours) VALUES
('Install Electrical Points', 'Complete electrical point installation including wiring and fixtures', 'electrical', 'intermediate', 8)
ON CONFLICT DO NOTHING;

-- Link electrical components to tasks
UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Electrical Points' LIMIT 1) 
WHERE name = 'Electrical Point Basic';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Electrical Points' LIMIT 1) 
WHERE name = 'Electrical Point Standard';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Electrical Points' LIMIT 1) 
WHERE name = 'Electrical Point Premium';

-- Create task requirements for electrical
INSERT INTO task_requirements (task_id, component_id, labor_id, quantity_per_sqm, requirement_type) VALUES
-- Electrical installation requirements
((SELECT id FROM tasks WHERE name = 'Install Electrical Points' LIMIT 1), 
 (SELECT id FROM components WHERE name = 'Electrical Point Standard' LIMIT 1), 
 NULL, 1.0, 'material'),
((SELECT id FROM tasks WHERE name = 'Install Electrical Points' LIMIT 1), 
 NULL, 
 (SELECT id FROM labor_rates WHERE name = 'Electrical Installation' LIMIT 1), 1.0, 'labor');