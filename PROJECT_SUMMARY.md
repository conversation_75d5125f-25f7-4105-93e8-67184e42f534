# NirmaanAI v2.1 Project Summary

## Overview

NirmaanAI v2.1 is a comprehensive construction cost calculator that provides accurate, transparent estimates for building projects in the Delhi/NCR region. The application has been refactored with a "Pluggable Component Architecture" using Supabase as the backend database, enabling a dynamic, user-centric experience with room-by-room customization and detailed cost breakdowns.

## Project Evolution

### Initial State (v2.0)
The project started with a hybrid architecture that included both a legacy calculation engine and a new V2 engine. The UI was well-designed but had several disconnected components and calculation issues. The main problems were:

1. Dual calculation engines causing inconsistencies
2. Disconnected UI components that didn't properly update the calculation
3. Non-functional admin panel
4. Missing database integration for user customizations
5. Unrealistic construction cost calculations
6. Incomplete room customization UI (missing plumbing, electrical issues)

### Transformation to v2.1
Through a series of directives, we've transformed the application into a production-ready system with:
1. A fully pluggable component architecture
2. Complete integration with Supabase for data management
3. A functional admin panel for non-technical users
4. Comprehensive user customization features
5. Professional output generation capabilities
6. Realistic construction cost calculations
7. Complete room customization UI with all tabs working properly

## Key Implemented Features

### Core Architecture
- ✅ Removed legacy calculation engine completely
- ✅ Implemented V2 calculation engine with Supabase integration
- ✅ Created a cohesive data flow where all user actions trigger recalculations
- ✅ Implemented database-backed override system for price adjustments
- ✅ Integrated engineering-grade geometric analysis for accurate quantity takeoffs

### Admin Control Panel
- ✅ Component Manager with full CRUD operations
- ✅ Task Recipe Manager for installation recipes
- ✅ UI Defaults Manager for setting default selections
- ✅ Labor Rate Management for different skill levels and locations
- ✅ Engineering Standards Manager for technical parameters
- ✅ Regional Data Manager for location-specific multipliers
- ✅ System Configuration for global settings

### User Experience
- ✅ Interactive Floor Plan for room selection
- ✅ Visual Room Configurator for material customization
- ✅ Facade Design Module for exterior customization
- ✅ Item Override Modal for transparent price adjustments
- ✅ AI Architect's Analysis for optimization suggestions
- ✅ Material Price Tracker with real-time updates

### Professional Features
- ✅ PDF Quote Generation
- ✅ Save & Share functionality
- ✅ Compliance Checker for regulatory requirements
- ✅ Project Summary with timeline estimates

## Technical Implementation Details

### Pluggable Component Architecture
The core of the system is the V2 calculation engine that:
1. Fetches components, tasks, and labor rates from Supabase
2. Calculates costs based on engineering principles
3. Applies user customizations and overrides
4. Generates detailed breakdowns for transparency

```typescript
// Core calculation function in v2CalculationEngine.ts
export async function calculateV2Cost(
  inputs: UserInputs,
  qualityTier: QualityTier = 'better',
  userOverrides: Record<string, any> = {},
  projectId?: string
): Promise<CalculationResult> {
  // Load all required data from Supabase
  const [components, tasks, defaults, laborRates, standards] = await Promise.all([
    componentAPI.getAll(),
    taskAPI.getAll(),
    uiDefaultsAPI.getDefaults(),
    laborRateAPI.getAll(),
    fetchCalculationStandards(inputs.location)
  ]);
  
  // Load user overrides from database if project ID is provided
  let savedOverrides: Record<string, any> = {};
  if (projectId) {
    try {
      const overrides = await overrideAPI.getByProject(projectId);
      overrides.forEach(override => {
        savedOverrides[override.component_id] = {
          originalQuantity: override.original_quantity,
          originalRate: override.original_rate,
          overrideQuantity: override.override_quantity,
          overrideRate: override.override_rate,
          reason: override.override_reason
        };
      });
    } catch (error) {
      console.error('Error loading saved overrides:', error);
    }
  }

  // Create calculation context
  const context: V2CalculationContext = {
    inputs,
    qualityTier,
    components,
    tasks,
    laborRates,
    defaults: defaults?.config_data || {},
    userOverrides: { ...savedOverrides, ...userOverrides },
    projectId,
    standards
  };

  // Perform geometric analysis with standards
  const geometricQuantities = performDigitalTakedown(inputs, standards);

  // Calculate costs using pluggable components
  const sections = await calculateSectionsFromComponents(context, geometricQuantities);

  // Apply GST, contingency, and profit margin from standards
  const materialCosts = sections.reduce((sum, section) => {
    if (['foundation', 'masonry', 'rooms', 'facade'].includes(section.id)) {
      return sum + section.subtotal;
    }
    return sum;
  }, 0);

  const serviceCosts = sections.reduce((sum, section) => {
    if (['mep', 'fees'].includes(section.id)) {
      return sum + section.subtotal;
    }
    return sum;
  }, 0);

  const gstOnMaterials = materialCosts * (standards.systemConfig.gstOnMaterials / 100);
  const gstOnServices = serviceCosts * (standards.systemConfig.gstOnServices / 100);
  
  // Add GST section
  const gstItems: CostItem[] = [
    {
      id: 'gst_materials',
      name: `GST on Materials (${standards.systemConfig.gstOnMaterials}%)`,
      quantity: 1,
      unit: 'lump sum',
      rate: gstOnMaterials,
      total: gstOnMaterials,
      category: 'taxes',
      editable: false,
      isCalculated: true
    },
    {
      id: 'gst_services',
      name: `GST on Services (${standards.systemConfig.gstOnServices}%)`,
      quantity: 1,
      unit: 'lump sum',
      rate: gstOnServices,
      total: gstOnServices,
      category: 'taxes',
      editable: false,
      isCalculated: true
    }
  ];

  sections.push({
    id: 'taxes',
    title: 'Taxes & Duties',
    items: gstItems,
    subtotal: gstOnMaterials + gstOnServices
  });

  // Calculate subtotal before contingency and profit
  const subtotal = sections.reduce((sum, section) => sum + section.subtotal, 0);
  
  // Apply contingency
  const contingencyAmount = subtotal * (standards.systemConfig.defaultContingency / 100);
  const contingencyItems: CostItem[] = [
    {
      id: 'contingency',
      name: `Contingency (${standards.systemConfig.defaultContingency}%)`,
      quantity: 1,
      unit: 'lump sum',
      rate: contingencyAmount,
      total: contingencyAmount,
      category: 'contingency',
      editable: true,
      isCalculated: true
    }
  ];

  sections.push({
    id: 'contingency',
    title: 'Contingency',
    items: contingencyItems,
    subtotal: contingencyAmount
  });

  // Apply profit margin
  const subtotalWithContingency = subtotal + contingencyAmount;
  const profitAmount = subtotalWithContingency * (standards.systemConfig.defaultProfitMargin / 100);
  const profitItems: CostItem[] = [
    {
      id: 'profit_margin',
      name: `Contractor Profit (${standards.systemConfig.defaultProfitMargin}%)`,
      quantity: 1,
      unit: 'lump sum',
      rate: profitAmount,
      total: profitAmount,
      category: 'profit',
      editable: true,
      isCalculated: true
    }
  ];

  sections.push({
    id: 'profit',
    title: 'Contractor Profit',
    items: profitItems,
    subtotal: profitAmount
  });

  const totalCost = sections.reduce((sum, section) => sum + section.subtotal, 0);
  const ratePerSqft = totalCost / geometricQuantities.totalBuiltUpArea;

  return {
    totalCost,
    ratePerSqft,
    sections,
    qualityTier,
    quantities: {
      ...geometricQuantities,
      // Legacy compatibility fields
      totalRCCVolume: geometricQuantities.totalConcreteVolume_m3,
      totalTMTSteel: geometricQuantities.totalSteel_kg,
      totalBrickworkArea: geometricQuantities.totalWallArea_sqm * 10.764,
      totalPlasterArea: geometricQuantities.totalWallArea_sqm * 10.764,
      numberOfDoors: Math.ceil(geometricQuantities.totalBuiltUpArea / 200),
      numberOfWindows: Math.ceil(geometricQuantities.totalBuiltUpArea / 150),
      numberOfBathrooms: Math.max(1, Math.floor(inputs.numberOfFloors * 1.5)),
      electricalPoints: Math.ceil(geometricQuantities.totalFloorArea_sqm * 0.86),
      plumbingPoints: Math.ceil(geometricQuantities.totalFloorArea_sqm * 0.43),
      steelRatioColumns: 160,
      foundationMultiplier: inputs.hasBasement ? 1.8 : 1.0
    },
    location: inputs.location,
    roomMaterialSelections: [],
    geometricQuantities
  };
}
```

### Task Recipe System
The system uses a recipe-based approach for calculating installed costs:

```typescript
async function calculateInstalledCost(
  component: Component,
  quantity: number,
  context: V2CalculationContext
): Promise<{
  materialCost: number;
  laborCost: number;
  additionalMaterialsCost: number;
  totalInstalledCost: number;
  breakdown: any;
}> {
  // Check for user overrides
  const override = context.userOverrides[component.id];
  const effectiveQuantity = override?.overrideQuantity !== undefined ? override.overrideQuantity : quantity;
  const effectiveRate = override?.overrideRate !== undefined ? override.overrideRate : component.unit_price;
  
  // Apply material multiplier from standards
  const materialMultiplier = context.standards.regionalData.materialMultiplier;
  const adjustedRate = effectiveRate * materialMultiplier;
  
  // Default to just the material cost if no associated task
  let materialCost = adjustedRate * effectiveQuantity;
  let laborCost = 0;
  let additionalMaterialsCost = 0;
  let breakdown: any = {
    formula: component.associated_task_id 
      ? `Total Installed Cost = Material Cost + Labor Cost + Supporting Materials`
      : `Total Cost = Quantity × Unit Price × Material Multiplier (${materialMultiplier})`,
    components: [
      {
        name: component.name,
        quantity: effectiveQuantity,
        unit: component.unit,
        rate: adjustedRate,
        total: materialCost,
        source: `${component.brand || 'Standard'} ${component.category} (${context.inputs.location} multiplier: ${materialMultiplier})`
      }
    ],
    notes: []
  };

  // Add note if override was applied
  if (override) {
    if (override.overrideQuantity !== undefined) {
      breakdown.notes.push(`Quantity manually overridden from ${override.originalQuantity} to ${override.overrideQuantity}`);
    }
    if (override.overrideRate !== undefined) {
      breakdown.notes.push(`Rate manually overridden from ₹${override.originalRate} to ₹${override.overrideRate}`);
    }
    if (override.reason) {
      breakdown.notes.push(`Override reason: ${override.reason}`);
    }
  }

  // If there's an associated task, calculate full installed cost
  if (component.associated_task_id) {
    const task = context.tasks.find(t => t.id === component.associated_task_id);
    
    if (task && task.task_requirements) {
      // Add task information to breakdown
      breakdown.notes.push(`Installation method: ${task.name}`);
      breakdown.notes.push(`Complexity level: ${task.complexity_level}`);
      
      // Find all requirements for this task
      const requirements = task.task_requirements;
      
      // Process each requirement
      requirements.forEach((req: any) => {
        if (req.requirement_type === 'labor' && req.labor_rates) {
          // Add labor cost based on quality tier
          const laborRate = req.labor_rates.rates[context.qualityTier] || 0;
          // Apply labor multiplier from standards
          const adjustedLaborRate = laborRate * context.standards.regionalData.laborMultiplier;
          const laborQuantity = effectiveQuantity * (req.quantity_per_sqm || 1);
          const laborCostForReq = adjustedLaborRate * laborQuantity;
          
          laborCost += laborCostForReq;
          
          // Add to breakdown
          breakdown.components.push({
            name: `${req.labor_rates.name} (${context.qualityTier})`,
            quantity: laborQuantity,
            unit: req.labor_rates.unit,
            rate: adjustedLaborRate,
            total: laborCostForReq,
            source: `Labor for ${task.name} (${context.inputs.location} multiplier: ${context.standards.regionalData.laborMultiplier})`
          });
          
          // Add productivity information
          if (req.labor_rates.productivity) {
            const { output_per_day, unit } = req.labor_rates.productivity;
            const manDays = laborQuantity / output_per_day;
            breakdown.notes.push(`Labor productivity: ${output_per_day} ${unit}/day, requiring approximately ${manDays.toFixed(1)} man-days`);
          }
        } 
        else if (req.requirement_type === 'material' && req.components) {
          // Skip if this is the main component itself
          if (req.component_id === component.id) return;
          
          // Add supporting material cost
          const materialRate = req.components.unit_price || 0;
          // Apply material multiplier from standards
          const adjustedMaterialRate = materialRate * materialMultiplier;
          const materialQuantity = effectiveQuantity * (req.quantity_per_sqm || 1);
          const materialCostForReq = adjustedMaterialRate * materialQuantity;
          
          additionalMaterialsCost += materialCostForReq;
          
          // Add to breakdown
          breakdown.components.push({
            name: req.components.name,
            quantity: materialQuantity,
            unit: req.components.unit,
            rate: adjustedMaterialRate,
            total: materialCostForReq,
            source: `Supporting material for ${task.name} (${context.inputs.location} multiplier: ${materialMultiplier})`
          });
          
          // Add material specification
          if (req.components.specifications) {
            const specs = Object.entries(req.components.specifications)
              .map(([key, value]) => `${key}: ${value}`)
              .join(', ');
            breakdown.notes.push(`${req.components.name} specifications: ${specs}`);
          }
        }
      });
    }
  }

  const totalInstalledCost = materialCost + laborCost + additionalMaterialsCost;
  
  // Complete the breakdown
  breakdown.materialCost = materialCost;
  breakdown.laborCost = laborCost;
  breakdown.additionalMaterialsCost = additionalMaterialsCost;
  breakdown.finalCost = totalInstalledCost;
  
  return {
    materialCost,
    laborCost,
    additionalMaterialsCost,
    totalInstalledCost,
    breakdown
  };
}
```

### User Customization Flow
The user journey follows a logical flow:
1. Enter basic project details (plot size, floors, location)
2. View the interactive floor plan
3. Customize individual rooms with materials and fixtures
4. Customize the facade design
5. Override specific items if needed
6. Generate professional output

### Room Customization UI
The Visual Room Configurator allows users to customize each room with:
- Flooring materials
- Wall finishes
- Electrical points
- Plumbing fixtures (for bathrooms)
- Bathroom fixture bundles

Each tab provides detailed information about the selected materials, including:
- Material specifications
- Cost breakdown
- Installation details
- Engineering standards

### Admin Panel Architecture
The admin panel is designed for non-technical users to manage the system's data:

1. **Component Management**:
   - Add, edit, and delete construction materials and products
   - Set pricing, specifications, and associated tasks
   - Upload images for visual reference

2. **Task Recipe Management**:
   - Define installation recipes with required materials and labor
   - Set complexity levels and estimated durations
   - Link tasks to components for automatic cost calculation

3. **UI Defaults Management**:
   - Set default materials for different room types
   - Configure default quality tiers
   - Define standard configurations for new projects

4. **Engineering Standards Management**:
   - Define structural parameters and assumptions
   - Set concrete mix designs for different grades
   - Configure steel reinforcement ratios
   - Set material consumption ratios and wastage factors

5. **Regional Data Management**:
   - Configure location-specific cost multipliers
   - Set professional fee structures
   - Define regulatory fees for different authorities

## Database Schema

The Supabase database includes the following key tables:

### components
```sql
CREATE TABLE components (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  category text NOT NULL,
  sub_category text,
  brand text,
  image_url text,
  cost_model text CHECK (cost_model IN ('per_unit', 'per_sqm', 'task_based')) DEFAULT 'per_sqm',
  unit_price numeric NOT NULL,
  unit text NOT NULL,
  associated_task_id uuid,
  specifications jsonb DEFAULT '{}',
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);
```

### tasks
```sql
CREATE TABLE tasks (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  description text,
  category text NOT NULL,
  complexity_level text CHECK (complexity_level IN ('basic', 'intermediate', 'advanced')) DEFAULT 'intermediate',
  estimated_duration_hours numeric DEFAULT 8,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);
```

### task_requirements
```sql
CREATE TABLE task_requirements (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  task_id uuid REFERENCES tasks(id) ON DELETE CASCADE,
  component_id uuid REFERENCES components(id) ON DELETE CASCADE,
  labor_id uuid REFERENCES labor_rates(id) ON DELETE CASCADE,
  quantity_per_sqm numeric DEFAULT 1,
  is_optional boolean DEFAULT false,
  requirement_type text CHECK (requirement_type IN ('material', 'labor', 'tool')) DEFAULT 'material',
  created_at timestamptz DEFAULT now()
);
```

### labor_rates
```sql
CREATE TABLE labor_rates (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  category text NOT NULL,
  skill_level text CHECK (skill_level IN ('unskilled', 'semiskilled', 'skilled', 'specialist')) DEFAULT 'skilled',
  unit text NOT NULL,
  rates jsonb DEFAULT '{"good": 0, "better": 0, "best": 0}' NOT NULL,
  productivity jsonb DEFAULT '{"output_per_day": 1, "unit": "sqm"}',
  location text DEFAULT 'gurgaon',
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);
```

### engineering_standards
```sql
CREATE TABLE engineering_standards (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  category text NOT NULL,
  name text NOT NULL,
  value text NOT NULL,
  unit text NOT NULL,
  description text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);
```

### concrete_mixes
```sql
CREATE TABLE concrete_mixes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  grade text NOT NULL,
  cement_bags_per_m3 numeric NOT NULL,
  sand_cft_per_m3 numeric NOT NULL,
  aggregate_cft_per_m3 numeric NOT NULL,
  water_cement_ratio numeric NOT NULL,
  applications text[],
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);
```

### user_projects
```sql
CREATE TABLE user_projects (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  project_name text NOT NULL,
  project_data jsonb NOT NULL,
  room_configurations jsonb DEFAULT '[]',
  selected_components jsonb DEFAULT '{}',
  total_cost numeric DEFAULT 0,
  is_shared boolean DEFAULT false,
  share_token text UNIQUE,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);
```

### user_overrides
```sql
CREATE TABLE user_overrides (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id uuid REFERENCES user_projects(id) ON DELETE CASCADE,
  component_id uuid REFERENCES components(id),
  original_quantity numeric,
  override_quantity numeric,
  original_rate numeric,
  override_rate numeric,
  override_reason text,
  created_at timestamptz DEFAULT now()
);
```

## Data Flow Architecture

The application follows a clear data flow pattern:

1. **User Input Collection**:
   - `UserInputSection` component collects basic project parameters
   - Data is stored in the `inputs` state in `App.tsx`

2. **Calculation Process**:
   - `calculateCost` function in `App.tsx` calls `calculateV2Cost` from `v2CalculationEngine.ts`
   - Engine fetches components, tasks, labor rates, and engineering standards from Supabase
   - Geometric analysis is performed using `performDigitalTakedown`
   - Costs are calculated section by section using the pluggable components

3. **Result Display**:
   - `ResultsSection` component displays the calculation results
   - Interactive components allow for customization
   - Changes trigger recalculation through the V2 engine

4. **Customization Flow**:
   - Room customization through `VisualRoomConfigurator`
   - Facade customization through `FacadeDesignModule`
   - Price overrides through `ItemOverrideModal`
   - All changes are persisted to Supabase and reflected in calculations

5. **Output Generation**:
   - `ProfessionalQuoteGenerator` for client-ready quotes
   - `ProjectSummary` for comprehensive project overview
   - `SaveShareEstimate` for collaboration

## Resolved Issues

Throughout the development process, we've addressed several critical issues:

1. **React Hooks Ordering**: Fixed issues in components like ProfessionalQuoteGenerator.tsx where hooks were conditionally rendered, causing React errors.

2. **Calculation Engine Integration**: Removed the legacy calculation engine and ensured all calculations use the V2 engine exclusively.

3. **Admin Panel Functionality**: Implemented full CRUD operations for components, tasks, and UI defaults with proper error handling and loading states.

4. **User Override System**: Created a transparent override system that persists to the database and is applied correctly in calculations.

5. **Task Requirements Management**: Implemented the ability to create and manage complex installation recipes with proper database integration.

6. **Room Configuration Logic**: Fixed the room planner to generate realistic room sizes that properly fit within the total floor area.

7. **Compliance Checker Math**: Corrected the calculation of total costs in the ComplianceChecker component.

8. **Component Form Validation**: Added proper validation and error handling to the ComponentForm to prevent invalid data entry.

9. **Labor Rate Integration**: Added proper labor rate management and integration with the calculation engine.

10. **UI Defaults Persistence**: Implemented proper saving and loading of UI defaults from the database.

11. **Plumbing UI**: Fixed the empty plumbing tab in the room customizer with proper fixture options and pricing.

12. **Electrical UI**: Improved the electrical points interface with proper +/- buttons and clear explanations.

13. **Wall Finish Options**: Removed duplicated wall finish options and added proper specifications.

14. **Construction Cost Calculation**: Fixed unrealistically low construction costs by adding proper structural components with realistic pricing.

15. **Database Migration Issues**: Fixed issues with the `jwt()` function in RLS policies by using `auth.jwt()` instead.

## Code Structure

The codebase is organized as follows:

```
/src
  /components           # UI components
    /admin              # Admin panel components
    /room               # Room customization components
    /facade             # Facade design components
  /data                 # Static data and constants
  /lib                  # Library code and API integrations
  /types                # TypeScript type definitions
  /utils                # Utility functions and calculation engines
  App.tsx               # Main application component
  main.tsx              # Application entry point
/supabase
  /migrations           # Database migration files
```

### Key Files

- `src/utils/v2CalculationEngine.ts`: The core calculation engine
- `src/lib/supabase.ts`: Supabase client and API functions
- `src/components/admin/AdminDashboard.tsx`: Main admin panel component
- `src/components/room/VisualRoomConfigurator.tsx`: Room customization component
- `src/components/facade/FacadeDesignModule.tsx`: Facade design component
- `src/components/ItemOverrideModal.tsx`: Price override component

## Next Steps

While the core functionality is now complete, there are still opportunities for enhancement:

1. **Performance Optimization**:
   - Implement caching for frequently accessed data
   - Optimize database queries for component and task loading
   - Implement pagination for large data sets

2. **User Experience Enhancements**:
   - Add guided tours for first-time users
   - Implement more detailed 3D visualizations
   - Add drag-and-drop room layout editor

3. **Advanced Features**:
   - Project comparison tool
   - Material alternatives suggestion engine
   - Construction timeline visualization
   - Cost optimization assistant

4. **Mobile Responsiveness**:
   - Enhance mobile experience for field use
   - Add offline capability for site visits

5. **Testing and Quality Assurance**:
   - Implement comprehensive unit tests
   - Add integration tests for critical flows
   - Perform usability testing with real users

## Deployment and Infrastructure

The application is designed to be deployed using:
- Frontend: Netlify or Vercel
- Backend: Supabase (serverless PostgreSQL database)
- Authentication: Supabase Auth
- Storage: Supabase Storage for images and documents

## Conclusion

NirmaanAI v2.1 has successfully evolved from a prototype to a production-ready application with a robust, pluggable architecture. The system now provides accurate, transparent cost estimates with a high degree of customization, making it a valuable tool for construction planning in the Delhi/NCR region.

The transformation from a hybrid legacy/V2 system to a fully integrated V2 architecture has resulted in a more maintainable, extensible, and user-friendly application. The admin panel provides a powerful interface for non-technical users to manage the system's data, while the user-facing components offer an intuitive and comprehensive experience for construction planning.

## Getting Started for New Developers

If you're new to the project, here's how to get started:

1. Clone the repository
2. Install dependencies with `npm install`
3. Set up environment variables:
   - Create a `.env` file with Supabase credentials
   - Ask an existing team member for access to the Supabase project
4. Start the development server with `npm run dev`
5. Explore the admin panel to understand the data structure
6. Review the code structure, starting with App.tsx and v2CalculationEngine.ts

## Contributing

When contributing to the project, please follow these guidelines:

1. Always create a new branch for your changes
2. Follow the existing code style and patterns
3. Ensure all changes to the calculation engine maintain accuracy
4. Test your changes thoroughly before submitting a pull request
5. Document any new features or significant changes

## Contact

For questions or support, please contact the project maintainers:
- Project Manager: [Name]
- Lead Developer: [Name]
- Supabase Administrator: [Name]