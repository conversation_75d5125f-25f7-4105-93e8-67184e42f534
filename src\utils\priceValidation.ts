/**
 * Price Validation System for Construction Materials
 * Based on Delhi/NCR market rates and 15+ years of civil engineering experience
 */

export interface PriceRange {
  min: number;
  max: number;
  unit: string;
  category: string;
  subCategory: string;
  marketBenchmark: number;
  lastUpdated: string;
}

export interface ValidationResult {
  isValid: boolean;
  severity: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  suggestedRange: PriceRange;
  deviationPercentage: number;
}

// Market-based price ranges for Delhi/NCR (Updated 2024)
export const PRICE_RANGES: Record<string, PriceRange> = {
  // Cement & Concrete
  'cement_opc_53': {
    min: 380,
    max: 470,
    unit: 'bag',
    category: 'Materials',
    subCategory: 'Cement',
    marketBenchmark: 425,
    lastUpdated: '2024-07-02'
  },
  'concrete_m25': {
    min: 180,
    max: 220,
    unit: 'cft',
    category: 'Materials',
    subCategory: 'Concrete',
    marketBenchmark: 200,
    lastUpdated: '2024-07-02'
  },

  // Steel & Reinforcement
  'tmt_steel_fe500d': {
    min: 70,
    max: 82,
    unit: 'kg',
    category: 'Materials',
    subCategory: 'Steel',
    marketBenchmark: 76,
    lastUpdated: '2024-07-02'
  },

  // Flooring Materials
  'vitrified_tiles': {
    min: 32,
    max: 65,
    unit: 'sqft',
    category: 'Flooring',
    subCategory: 'Vitrified Tiles',
    marketBenchmark: 45,
    lastUpdated: '2024-07-02'
  },
  'ceramic_tiles': {
    min: 18,
    max: 35,
    unit: 'sqft',
    category: 'Flooring',
    subCategory: 'Ceramic Tiles',
    marketBenchmark: 25,
    lastUpdated: '2024-07-02'
  },
  'anti_skid_tiles': {
    min: 25,
    max: 45,
    unit: 'sqft',
    category: 'Flooring',
    subCategory: 'Anti-Skid Tiles',
    marketBenchmark: 32,
    lastUpdated: '2024-07-02'
  },
  'marble_tiles': {
    min: 45,
    max: 120,
    unit: 'sqft',
    category: 'Flooring',
    subCategory: 'Marble Tiles',
    marketBenchmark: 75,
    lastUpdated: '2024-07-02'
  },
  'granite_tiles': {
    min: 35,
    max: 85,
    unit: 'sqft',
    category: 'Flooring',
    subCategory: 'Granite Tiles',
    marketBenchmark: 55,
    lastUpdated: '2024-07-02'
  },

  // Bathroom Fittings
  'washbasin': {
    min: 2200,
    max: 8500,
    unit: 'piece',
    category: 'Bathroom Fittings',
    subCategory: 'Basin',
    marketBenchmark: 4500,
    lastUpdated: '2024-07-02'
  },
  'water_closet': {
    min: 4000,
    max: 18000,
    unit: 'piece',
    category: 'Bathroom Fittings',
    subCategory: 'Water Closet',
    marketBenchmark: 8500,
    lastUpdated: '2024-07-02'
  },
  'faucet_set': {
    min: 3500,
    max: 12000,
    unit: 'set',
    category: 'Bathroom Fittings',
    subCategory: 'Faucets',
    marketBenchmark: 6500,
    lastUpdated: '2024-07-02'
  },

  // Electrical Components
  'modular_switches': {
    min: 220,
    max: 750,
    unit: 'point',
    category: 'Electrical',
    subCategory: 'Switches',
    marketBenchmark: 400,
    lastUpdated: '2024-07-02'
  },

  // Facade Materials
  'acp_sheets': {
    min: 42,
    max: 85,
    unit: 'sqft',
    category: 'Facade',
    subCategory: 'ACP Cladding',
    marketBenchmark: 60,
    lastUpdated: '2024-07-02'
  },
  'hpl_sheets': {
    min: 45,
    max: 75,
    unit: 'sqft',
    category: 'Facade',
    subCategory: 'HPL Sheets',
    marketBenchmark: 58,
    lastUpdated: '2024-07-02'
  },
  'natural_stone': {
    min: 80,
    max: 150,
    unit: 'sqft',
    category: 'Facade',
    subCategory: 'Stone Cladding',
    marketBenchmark: 110,
    lastUpdated: '2024-07-02'
  },

  // Wall Finishes
  'premium_emulsion': {
    min: 3.5,
    max: 8.0,
    unit: 'sqft',
    category: 'Wall Finishes',
    subCategory: 'Premium Emulsion',
    marketBenchmark: 5.2,
    lastUpdated: '2024-07-02'
  },
  'economy_emulsion': {
    min: 2.0,
    max: 4.5,
    unit: 'sqft',
    category: 'Wall Finishes',
    subCategory: 'Economy Emulsion',
    marketBenchmark: 3.0,
    lastUpdated: '2024-07-02'
  },

  // Masonry
  'aac_blocks': {
    min: 25,
    max: 35,
    unit: 'sqft',
    category: 'Masonry',
    subCategory: 'AAC Blocks',
    marketBenchmark: 30,
    lastUpdated: '2024-07-02'
  },

  // Windows
  'upvc_windows': {
    min: 55,
    max: 85,
    unit: 'sqft',
    category: 'Windows',
    subCategory: 'uPVC',
    marketBenchmark: 68,
    lastUpdated: '2024-07-02'
  },
  'aluminium_windows': {
    min: 35,
    max: 55,
    unit: 'sqft',
    category: 'Windows',
    subCategory: 'Aluminium',
    marketBenchmark: 42,
    lastUpdated: '2024-07-02'
  }
};

/**
 * Get price range for a component based on category and subcategory
 */
export function getPriceRange(category: string, subCategory: string): PriceRange | null {
  const key = `${category.toLowerCase()}_${subCategory.toLowerCase()}`.replace(/\s+/g, '_');
  
  // Try exact match first
  if (PRICE_RANGES[key]) {
    return PRICE_RANGES[key];
  }

  // Try partial matches
  for (const [rangeKey, range] of Object.entries(PRICE_RANGES)) {
    if (rangeKey.includes(subCategory.toLowerCase().replace(/\s+/g, '_')) ||
        subCategory.toLowerCase().includes(rangeKey.split('_')[1])) {
      return range;
    }
  }

  return null;
}

/**
 * Validate component price against market ranges
 */
export function validatePrice(
  price: number,
  category: string,
  subCategory: string,
  componentName: string
): ValidationResult {
  const priceRange = getPriceRange(category, subCategory);
  
  if (!priceRange) {
    return {
      isValid: true,
      severity: 'info',
      message: `No price validation data available for ${category} - ${subCategory}`,
      suggestedRange: {
        min: 0,
        max: 0,
        unit: '',
        category,
        subCategory,
        marketBenchmark: 0,
        lastUpdated: new Date().toISOString()
      },
      deviationPercentage: 0
    };
  }

  const deviation = ((price - priceRange.marketBenchmark) / priceRange.marketBenchmark) * 100;
  
  // Critical: Price is extremely low (>70% below market)
  if (price < priceRange.min * 0.3) {
    return {
      isValid: false,
      severity: 'critical',
      message: `🚨 CRITICAL: ${componentName} price ₹${price} is ${Math.abs(deviation).toFixed(1)}% below market benchmark (₹${priceRange.marketBenchmark}). This will cause severe cost underestimation.`,
      suggestedRange: priceRange,
      deviationPercentage: deviation
    };
  }

  // Error: Price is significantly below minimum
  if (price < priceRange.min) {
    return {
      isValid: false,
      severity: 'error',
      message: `❌ ERROR: ${componentName} price ₹${price} is below market minimum (₹${priceRange.min}). Expected range: ₹${priceRange.min} - ₹${priceRange.max}`,
      suggestedRange: priceRange,
      deviationPercentage: deviation
    };
  }

  // Warning: Price is significantly above maximum
  if (price > priceRange.max) {
    return {
      isValid: false,
      severity: 'warning',
      message: `⚠️ WARNING: ${componentName} price ₹${price} is above market maximum (₹${priceRange.max}). This may lead to cost overestimation.`,
      suggestedRange: priceRange,
      deviationPercentage: deviation
    };
  }

  // Info: Price is within acceptable range but notable deviation
  if (Math.abs(deviation) > 20) {
    return {
      isValid: true,
      severity: 'info',
      message: `ℹ️ INFO: ${componentName} price ₹${price} deviates ${Math.abs(deviation).toFixed(1)}% from market benchmark (₹${priceRange.marketBenchmark})`,
      suggestedRange: priceRange,
      deviationPercentage: deviation
    };
  }

  // Valid: Price is within acceptable range
  return {
    isValid: true,
    severity: 'info',
    message: `✅ VALID: ${componentName} price ₹${price} is within market range`,
    suggestedRange: priceRange,
    deviationPercentage: deviation
  };
}

/**
 * Batch validate multiple components
 */
export function validateComponentPrices(components: Array<{
  name: string;
  category: string;
  subCategory: string;
  unitPrice: number;
}>): ValidationResult[] {
  return components.map(component => 
    validatePrice(component.unitPrice, component.category, component.subCategory, component.name)
  );
}

/**
 * Get quality tier multipliers for pricing
 */
export function getQualityTierMultiplier(tier: 'good' | 'better' | 'best'): number {
  switch (tier) {
    case 'good': return 0.85;
    case 'better': return 1.0;
    case 'best': return 1.35;
    default: return 1.0;
  }
}

/**
 * Get regional price multiplier for different locations
 */
export function getRegionalMultiplier(location: string): number {
  const multipliers: Record<string, number> = {
    'delhi': 1.0,
    'gurgaon': 1.15,
    'noida': 1.05,
    'ghaziabad': 0.95,
    'faridabad': 1.0,
    'mumbai': 1.25,
    'bangalore': 1.1,
    'pune': 1.05,
    'hyderabad': 0.9,
    'chennai': 0.95
  };
  
  return multipliers[location.toLowerCase()] || 1.0;
}
