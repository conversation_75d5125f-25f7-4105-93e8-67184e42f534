import React, { useState, useEffect } from 'react';
import { Save, Plus, Edit, Trash2, Upload, Download, AlertTriangle, CheckCircle, Clock, Users, Calculator, Zap, TrendingUp } from 'lucide-react';
import { supabase, laborRateAPI, LaborRate } from '../../lib/supabase';
import { MultiModalLaborRateEditor } from './MultiModalLaborRateEditor';
import { LaborCostComparisonPanel } from './LaborCostComparisonPanel';
import { EnhancedLaborRate, calculateLaborCost, ProjectContext } from '../../lib/multiModalLaborCalculations';
import { seedEnhancedLaborRates } from '../../lib/enhancedLaborRateSeeder';

interface LaborRateManagerProps {
  onUpdateLaborRate?: (laborRateId: string, updates: Partial<LaborRate>) => Promise<void>;
}

interface LaborRateValidation {
  laborRateId: string;
  isValid: boolean;
  issues: string[];
  marketComparison: {
    isWithinRange: boolean;
    marketMin: number;
    marketMax: number;
    deviation: number;
  };
}

export function EnhancedLaborRateManager({ onUpdateLaborRate }: LaborRateManagerProps) {
  const [laborRates, setLaborRates] = useState<LaborRate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [editingRate, setEditingRate] = useState<LaborRate | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showMultiModalEditor, setShowMultiModalEditor] = useState(false);
  const [validationResults, setValidationResults] = useState<LaborRateValidation[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showCostComparison, setShowCostComparison] = useState(false);

  // Market rate ranges for validation (Delhi/NCR 2024)
  const marketRanges: Record<string, { min: number; max: number; unit: string }> = {
    'foundation_excavation': { min: 400, max: 650, unit: 'm³' },
    'foundation_concrete': { min: 350, max: 550, unit: 'm³' },
    'steel_fixing': { min: 7, max: 13, unit: 'kg' },
    'masonry_brick': { min: 140, max: 220, unit: 'sqm' },
    'masonry_aac': { min: 70, max: 130, unit: 'sqm' },
    'plastering_internal': { min: 40, max: 70, unit: 'sqm' },
    'plastering_external': { min: 50, max: 80, unit: 'sqm' },
    'tile_floor': { min: 90, max: 150, unit: 'sqm' },
    'tile_wall': { min: 120, max: 180, unit: 'sqm' },
    'marble_installation': { min: 140, max: 250, unit: 'sqm' },
    'painting_interior': { min: 30, max: 60, unit: 'sqm' },
    'painting_exterior': { min: 40, max: 70, unit: 'sqm' },
    'electrical_wiring': { min: 140, max: 220, unit: 'point' },
    'electrical_fitting': { min: 60, max: 110, unit: 'point' },
    'plumbing_installation': { min: 180, max: 320, unit: 'point' },
    'bathroom_fitting': { min: 700, max: 1300, unit: 'set' },
    'window_installation': { min: 70, max: 120, unit: 'sqm' },
    'door_installation': { min: 90, max: 150, unit: 'sqm' }
  };

  const categories = [
    'all', 'foundation', 'masonry', 'finishes', 'flooring', 
    'electrical', 'plumbing', 'doors_windows', 'exterior', 'specialized'
  ];

  useEffect(() => {
    loadLaborRates();
  }, []);

  useEffect(() => {
    if (laborRates.length > 0) {
      validateLaborRates();
    }
  }, [laborRates]);

  const loadLaborRates = async () => {
    setIsLoading(true);
    try {
      const data = await laborRateAPI.getAll();
      setLaborRates(data);
    } catch (error) {
      console.error('Error loading labor rates:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const validateLaborRates = () => {
    const results: LaborRateValidation[] = laborRates.map(rate => {
      const issues: string[] = [];
      let marketComparison = {
        isWithinRange: true,
        marketMin: 0,
        marketMax: 0,
        deviation: 0
      };

      // Basic validation
      if (!rate.rates.good || !rate.rates.better || !rate.rates.best) {
        issues.push('Missing rate tiers');
      }

      if (rate.rates.good >= rate.rates.better || rate.rates.better >= rate.rates.best) {
        issues.push('Rate tiers not in ascending order');
      }

      if (!rate.productivity?.output_per_day || rate.productivity.output_per_day <= 0) {
        issues.push('Invalid productivity data');
      }

      // Market rate validation
      const rateKey = `${rate.category}_${rate.name.toLowerCase().replace(/\s+/g, '_')}`;
      const marketRange = marketRanges[rateKey];
      
      if (marketRange) {
        const avgRate = (rate.rates.good + rate.rates.better + rate.rates.best) / 3;
        marketComparison = {
          isWithinRange: avgRate >= marketRange.min && avgRate <= marketRange.max,
          marketMin: marketRange.min,
          marketMax: marketRange.max,
          deviation: ((avgRate - (marketRange.min + marketRange.max) / 2) / ((marketRange.min + marketRange.max) / 2)) * 100
        };

        if (!marketComparison.isWithinRange) {
          issues.push(`Rate outside market range (₹${marketRange.min}-${marketRange.max}/${marketRange.unit})`);
        }
      }

      return {
        laborRateId: rate.id,
        isValid: issues.length === 0,
        issues,
        marketComparison
      };
    });

    setValidationResults(results);
  };

  const handleSaveLaborRate = async (laborRateData: Partial<LaborRate>) => {
    try {
      if (editingRate?.id) {
        await laborRateAPI.update(editingRate.id, laborRateData);
      } else {
        await laborRateAPI.create(laborRateData as Omit<LaborRate, 'id' | 'created_at' | 'updated_at'>);
      }
      await loadLaborRates();
      setShowEditModal(false);
      setEditingRate(null);
    } catch (error) {
      console.error('Error saving labor rate:', error);
      alert('Error saving labor rate');
    }
  };

  const handleSaveEnhancedLaborRate = async (laborRateData: Partial<EnhancedLaborRate>) => {
    try {
      if (editingRate?.id) {
        await laborRateAPI.update(editingRate.id, laborRateData);
      } else {
        await laborRateAPI.create(laborRateData as Omit<LaborRate, 'id' | 'created_at' | 'updated_at'>);
      }
      await loadLaborRates();
      setShowMultiModalEditor(false);
      setEditingRate(null);
    } catch (error) {
      console.error('Error saving enhanced labor rate:', error);
      alert('Error saving enhanced labor rate');
    }
  };

  const openMultiModalEditor = (rate?: LaborRate) => {
    setEditingRate(rate || null);
    setShowMultiModalEditor(true);
  };

  const calculateSampleCosts = (rate: LaborRate) => {
    try {
      const enhancedRate = rate as EnhancedLaborRate;
      const sampleContext: ProjectContext = {
        total_area: 1500,
        project_type: 'residential',
        duration_months: 3,
        quality_tier: 'better',
        budget_preference: 'balanced'
      };

      return calculateLaborCost(enhancedRate, 25, sampleContext);
    } catch (error) {
      console.error('Error calculating sample costs:', error);
      return {
        primary_cost: 0,
        model_used: 'per_unit',
        alternatives: [],
        breakdown: { base_rate: 0, quantity: 25, total: 0, unit: 'unit', timeline_days: 0 },
        recommendations: ['Calculation error'],
        efficiency_rating: 'poor' as const
      };
    }
  };

  const handleSeedEnhancedRates = async () => {
    try {
      setIsLoading(true);
      await seedEnhancedLaborRates();
      await loadLaborRates();
      alert('Enhanced labor rates seeded successfully!');
    } catch (error) {
      console.error('Error seeding enhanced rates:', error);
      alert('Error seeding enhanced rates. Check console for details.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteLaborRate = async (id: string) => {
    if (confirm('Are you sure you want to delete this labor rate?')) {
      try {
        await laborRateAPI.delete(id);
        await loadLaborRates();
      } catch (error) {
        console.error('Error deleting labor rate:', error);
      }
    }
  };

  const addMissingLaborRates = async () => {
    const missingRates = [
      {
        name: 'Manual Excavation',
        category: 'foundation',
        skill_level: 'unskilled' as const,
        unit: 'm³',
        rates: { good: 450, better: 525, best: 600 },
        productivity: { output_per_day: 1.0, unit: 'm³' },
        location: 'gurgaon',
        is_active: true
      },
      {
        name: 'Concrete Mixing & Pouring',
        category: 'foundation',
        skill_level: 'skilled' as const,
        unit: 'm³',
        rates: { good: 380, better: 450, best: 520 },
        productivity: { output_per_day: 8, unit: 'm³' },
        location: 'gurgaon',
        is_active: true
      },
      {
        name: 'Brickwork 230mm',
        category: 'masonry',
        skill_level: 'skilled' as const,
        unit: 'sqm',
        rates: { good: 150, better: 180, best: 210 },
        productivity: { output_per_day: 8, unit: 'sqm' },
        location: 'gurgaon',
        is_active: true
      },
      {
        name: 'Interior Plastering',
        category: 'finishes',
        skill_level: 'skilled' as const,
        unit: 'sqm',
        rates: { good: 45, better: 55, best: 65 },
        productivity: { output_per_day: 25, unit: 'sqm' },
        location: 'gurgaon',
        is_active: true
      },
      {
        name: 'Electrical Wiring',
        category: 'electrical',
        skill_level: 'skilled' as const,
        unit: 'point',
        rates: { good: 150, better: 180, best: 210 },
        productivity: { output_per_day: 20, unit: 'points' },
        location: 'gurgaon',
        is_active: true
      },
      {
        name: 'Plumbing Installation',
        category: 'plumbing',
        skill_level: 'skilled' as const,
        unit: 'point',
        rates: { good: 200, better: 250, best: 300 },
        productivity: { output_per_day: 12, unit: 'points' },
        location: 'gurgaon',
        is_active: true
      }
    ];

    try {
      for (const rate of missingRates) {
        await laborRateAPI.create(rate);
      }
      await loadLaborRates();
      alert(`Added ${missingRates.length} missing labor rates successfully!`);
    } catch (error) {
      console.error('Error adding missing rates:', error);
      alert('Error adding missing labor rates');
    }
  };

  const filteredRates = laborRates.filter(rate => {
    const matchesCategory = selectedCategory === 'all' || rate.category === selectedCategory;
    const matchesSearch = rate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rate.category.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const getValidationForRate = (rateId: string) => {
    return validationResults.find(v => v.laborRateId === rateId);
  };

  const getValidationSummary = () => {
    const total = validationResults.length;
    const valid = validationResults.filter(v => v.isValid).length;
    const invalid = total - valid;
    return { total, valid, invalid };
  };

  const summary = getValidationSummary();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Enhanced Labor Rate Management</h2>
          <p className="text-sm text-gray-600">
            Manage construction labor rates with market validation and productivity tracking
          </p>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowCostComparison(!showCostComparison)}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 flex items-center space-x-2"
          >
            <Calculator className="w-4 h-4" />
            <span>Cost Analysis</span>
          </button>
          <button
            onClick={handleSeedEnhancedRates}
            disabled={isLoading}
            className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:bg-gray-400 flex items-center space-x-2"
          >
            <TrendingUp className="w-4 h-4" />
            <span>Seed Enhanced Rates</span>
          </button>
          <button
            onClick={addMissingLaborRates}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Add Missing Rates</span>
          </button>
          <button
            onClick={() => openMultiModalEditor()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center space-x-2"
          >
            <Zap className="w-4 h-4" />
            <span>Add Enhanced Rate</span>
          </button>
          <button
            onClick={() => {
              setEditingRate(null);
              setShowEditModal(true);
            }}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Add Basic Rate</span>
          </button>
        </div>
      </div>

      {/* Validation Summary */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
            <div>
              <div className="text-lg font-semibold text-green-800">{summary.valid}</div>
              <div className="text-sm text-green-600">Valid Rates</div>
            </div>
          </div>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
            <div>
              <div className="text-lg font-semibold text-red-800">{summary.invalid}</div>
              <div className="text-sm text-red-600">Issues Found</div>
            </div>
          </div>
        </div>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <Users className="w-5 h-5 text-blue-600 mr-2" />
            <div>
              <div className="text-lg font-semibold text-blue-800">{summary.total}</div>
              <div className="text-sm text-blue-600">Total Rates</div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex space-x-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search labor rates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md"
        >
          {categories.map(category => (
            <option key={category} value={category}>
              {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
            </option>
          ))}
        </select>
      </div>

      {/* Labor Rates Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Labor Type
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rates (₹)
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Productivity
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Validation
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredRates.map((rate) => {
                const validation = getValidationForRate(rate.id);
                return (
                  <tr key={rate.id} className="hover:bg-gray-50">
                    <td className="px-4 py-3">
                      <div>
                        <div className="font-medium text-gray-900">{rate.name}</div>
                        <div className="text-sm text-gray-500">
                          {rate.category} • {rate.skill_level} • {rate.unit}
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <div className="text-sm">
                        <div>Good: ₹{rate.rates?.good || 'N/A'}</div>
                        <div>Better: ₹{rate.rates?.better || 'N/A'}</div>
                        <div>Best: ₹{rate.rates?.best || 'N/A'}</div>
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center text-sm">
                        <Clock className="w-4 h-4 text-gray-400 mr-1" />
                        {rate.productivity?.output_per_day || 'N/A'} {rate.productivity?.unit || 'units'}/day
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      {validation && (
                        <div className="flex items-center">
                          {validation.isValid ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <AlertTriangle className="w-4 h-4 text-red-500" />
                          )}
                          <span className={`ml-1 text-xs ${validation.isValid ? 'text-green-600' : 'text-red-600'}`}>
                            {validation.isValid ? 'Valid' : `${validation.issues.length} issues`}
                          </span>
                        </div>
                      )}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => openMultiModalEditor(rate)}
                          className="text-purple-600 hover:text-purple-800"
                          title="Enhanced Editor"
                        >
                          <Zap className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setEditingRate(rate);
                            setShowEditModal(true);
                          }}
                          className="text-blue-600 hover:text-blue-800"
                          title="Basic Editor"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        {showCostComparison && (
                          <button
                            onClick={() => {
                              const costs = calculateSampleCosts(rate);
                              alert(`Sample Cost Analysis for ${rate.name}:\n\nPrimary Cost: ₹${costs.primary_cost.toLocaleString()}\nModel Used: ${costs.model_used}\nEfficiency: ${costs.efficiency_rating}\n\nAlternatives: ${costs.alternatives.length} options available`);
                            }}
                            className="text-green-600 hover:text-green-800"
                            title="Cost Analysis"
                          >
                            <Calculator className="w-4 h-4" />
                          </button>
                        )}
                        <button
                          onClick={() => handleDeleteLaborRate(rate.id)}
                          className="text-red-600 hover:text-red-800"
                          title="Delete"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Multi-Modal Labor Rate Editor */}
      {showMultiModalEditor && (
        <MultiModalLaborRateEditor
          laborRate={editingRate}
          onSave={handleSaveEnhancedLaborRate}
          onCancel={() => {
            setShowMultiModalEditor(false);
            setEditingRate(null);
          }}
        />
      )}

      {/* Cost Comparison Panel */}
      {showCostComparison && (
        <LaborCostComparisonPanel
          laborRates={laborRates}
          onClose={() => setShowCostComparison(false)}
          onRefreshRates={loadLaborRates}
        />
      )}
    </div>
  );
}
