import React, { useState, useEffect } from 'react';
import { Component } from '../lib/supabase';
import { getQualityTierMultiplier, getRegionalMultiplier } from '../utils/priceValidation';

interface BrandOption {
  id: string;
  name: string;
  brand: string;
  unitPrice: number;
  qualityTier: 'good' | 'better' | 'best';
  specifications: Record<string, any>;
  marketPosition: string;
  warranty: string;
  features: string[];
}

interface BrandQualitySelectorProps {
  category: string;
  subCategory: string;
  components: Component[];
  selectedComponentId?: string;
  qualityTier: 'good' | 'better' | 'best';
  location: string;
  onSelectionChange: (componentId: string, adjustedPrice: number) => void;
  showPriceBreakdown?: boolean;
}

export function BrandQualitySelector({
  category,
  subCategory,
  components,
  selectedComponentId,
  qualityTier,
  location,
  onSelectionChange,
  showPriceBreakdown = true
}: BrandQualitySelectorProps) {
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [priceBreakdown, setPriceBreakdown] = useState<{
    basePrice: number;
    qualityMultiplier: number;
    regionalMultiplier: number;
    finalPrice: number;
  } | null>(null);

  // Filter components by category and subcategory
  const relevantComponents = components.filter(
    c => c.category === category && (c.sub_category === subCategory || !subCategory)
  );

  // Group components by brand and quality tier
  const brandOptions: BrandOption[] = relevantComponents.map(component => ({
    id: component.id,
    name: component.name,
    brand: component.brand || 'Generic',
    unitPrice: component.unit_price,
    qualityTier: getQualityTierFromSpecs(component.specifications),
    specifications: component.specifications,
    marketPosition: getMarketPosition(component.unit_price, relevantComponents),
    warranty: component.specifications?.warranty || 'Standard',
    features: getFeaturesList(component.specifications)
  }));

  // Sort by quality tier and price
  const sortedOptions = brandOptions.sort((a, b) => {
    const tierOrder = { 'good': 1, 'better': 2, 'best': 3 };
    if (tierOrder[a.qualityTier] !== tierOrder[b.qualityTier]) {
      return tierOrder[a.qualityTier] - tierOrder[b.qualityTier];
    }
    return a.unitPrice - b.unitPrice;
  });

  // Filter by current quality tier preference
  const filteredOptions = sortedOptions.filter(option => {
    if (qualityTier === 'good') return option.qualityTier === 'good';
    if (qualityTier === 'better') return ['good', 'better'].includes(option.qualityTier);
    return true; // 'best' shows all options
  });

  useEffect(() => {
    if (selectedComponentId) {
      const component = components.find(c => c.id === selectedComponentId);
      if (component) {
        calculatePriceBreakdown(component);
      }
    }
  }, [selectedComponentId, qualityTier, location]);

  const calculatePriceBreakdown = (component: Component) => {
    const basePrice = component.unit_price;
    const qualityMultiplier = getQualityTierMultiplier(qualityTier);
    const regionalMultiplier = getRegionalMultiplier(location);
    const finalPrice = basePrice * qualityMultiplier * regionalMultiplier;

    setPriceBreakdown({
      basePrice,
      qualityMultiplier,
      regionalMultiplier,
      finalPrice
    });

    onSelectionChange(component.id, finalPrice);
  };

  const handleBrandSelection = (componentId: string) => {
    setSelectedBrand(componentId);
    const component = components.find(c => c.id === componentId);
    if (component) {
      calculatePriceBreakdown(component);
    }
  };

  return (
    <div className="space-y-4">
      {/* Quality Tier Indicator */}
      <div className="flex items-center justify-between">
        <h4 className="font-medium text-gray-900">{category} - {subCategory}</h4>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">Quality Tier:</span>
          <span className={`px-2 py-1 rounded text-xs font-medium ${
            qualityTier === 'good' ? 'bg-green-100 text-green-800' :
            qualityTier === 'better' ? 'bg-blue-100 text-blue-800' :
            'bg-purple-100 text-purple-800'
          }`}>
            {qualityTier.toUpperCase()}
          </span>
        </div>
      </div>

      {/* Brand Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredOptions.map(option => (
          <div
            key={option.id}
            className={`border rounded-lg p-4 cursor-pointer transition-all ${
              selectedBrand === option.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handleBrandSelection(option.id)}
          >
            <div className="flex items-start justify-between mb-2">
              <div>
                <h5 className="font-medium text-gray-900">{option.brand}</h5>
                <p className="text-sm text-gray-600">{option.name}</p>
              </div>
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                option.qualityTier === 'good' ? 'bg-green-100 text-green-700' :
                option.qualityTier === 'better' ? 'bg-blue-100 text-blue-700' :
                'bg-purple-100 text-purple-700'
              }`}>
                {option.qualityTier}
              </span>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Base Price:</span>
                <span className="font-semibold">₹{option.unitPrice}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Market Position:</span>
                <span className="text-sm">{option.marketPosition}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Warranty:</span>
                <span className="text-sm">{option.warranty}</span>
              </div>

              {option.features.length > 0 && (
                <div className="mt-2">
                  <p className="text-xs text-gray-600 mb-1">Key Features:</p>
                  <div className="flex flex-wrap gap-1">
                    {option.features.slice(0, 3).map((feature, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Price Breakdown */}
      {showPriceBreakdown && priceBreakdown && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h5 className="font-medium text-gray-900 mb-3">Price Calculation Breakdown</h5>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Base Price:</span>
              <span>₹{priceBreakdown.basePrice}</span>
            </div>
            <div className="flex justify-between">
              <span>Quality Tier Multiplier ({qualityTier}):</span>
              <span>×{priceBreakdown.qualityMultiplier}</span>
            </div>
            <div className="flex justify-between">
              <span>Regional Multiplier ({location}):</span>
              <span>×{priceBreakdown.regionalMultiplier}</span>
            </div>
            <hr className="my-2" />
            <div className="flex justify-between font-semibold">
              <span>Final Price:</span>
              <span>₹{priceBreakdown.finalPrice.toFixed(2)}</span>
            </div>
          </div>
        </div>
      )}

      {filteredOptions.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No {qualityTier} quality options available for {category} - {subCategory}</p>
          <p className="text-sm mt-1">Try selecting a different quality tier or check component database</p>
        </div>
      )}
    </div>
  );
}

// Helper functions
function getQualityTierFromSpecs(specifications: Record<string, any>): 'good' | 'better' | 'best' {
  if (specifications?.quality_tier) {
    return specifications.quality_tier;
  }
  
  // Infer from specifications
  const hasLuxuryFeatures = specifications?.finish?.includes('luxury') || 
                           specifications?.warranty?.includes('10 years') ||
                           specifications?.features?.includes('premium');
  
  const hasPremiumFeatures = specifications?.finish?.includes('premium') || 
                            specifications?.warranty?.includes('5 years') ||
                            specifications?.type?.includes('premium');
  
  if (hasLuxuryFeatures) return 'best';
  if (hasPremiumFeatures) return 'better';
  return 'good';
}

function getMarketPosition(price: number, allComponents: Component[]): string {
  const prices = allComponents.map(c => c.unit_price).sort((a, b) => a - b);
  const percentile = (prices.indexOf(price) / prices.length) * 100;
  
  if (percentile < 33) return 'Budget';
  if (percentile < 66) return 'Mid-range';
  return 'Premium';
}

function getFeaturesList(specifications: Record<string, any>): string[] {
  const features: string[] = [];
  
  if (specifications?.finish) features.push(specifications.finish);
  if (specifications?.type) features.push(specifications.type);
  if (specifications?.material) features.push(specifications.material);
  if (specifications?.features) {
    if (Array.isArray(specifications.features)) {
      features.push(...specifications.features);
    } else {
      features.push(specifications.features);
    }
  }
  
  return features.filter(Boolean).slice(0, 5);
}
