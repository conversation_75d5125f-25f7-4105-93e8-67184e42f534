/*
  # NirmaanAI v2.0 Core Database Schema

  1. New Tables
    - `components` - Heart of the pluggable system for materials/products
    - `tasks` - Knowledge core of installation recipes
    - `task_requirements` - Junction table defining task recipes
    - `labor_rates` - Tiered labor rates for different skill levels
    - `admin_users` - Admin panel access management
    - `ui_defaults` - JSON configuration for default selections
    - `user_projects` - Store user project configurations
    - `user_overrides` - Track manual user overrides with transparency

  2. Security
    - Enable RLS on all tables
    - Admin-only policies for management tables
    - User-specific policies for project data
*/

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Components Table: Heart of the pluggable system
CREATE TABLE IF NOT EXISTS components (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  category text NOT NULL,
  sub_category text,
  brand text,
  image_url text,
  cost_model text CHECK (cost_model IN ('per_unit', 'per_sqm', 'task_based')) DEFAULT 'per_sqm',
  unit_price numeric NOT NULL,
  unit text NOT NULL,
  associated_task_id uuid,
  specifications jsonb DEFAULT '{}',
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Tasks Table: Knowledge core of installation recipes
CREATE TABLE IF NOT EXISTS tasks (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  description text,
  category text NOT NULL,
  complexity_level text CHECK (complexity_level IN ('basic', 'intermediate', 'advanced')) DEFAULT 'intermediate',
  estimated_duration_hours numeric DEFAULT 8,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Labor Rates Table: Tiered pricing for different skill levels
CREATE TABLE IF NOT EXISTS labor_rates (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  name text NOT NULL,
  category text NOT NULL,
  skill_level text CHECK (skill_level IN ('unskilled', 'semiskilled', 'skilled', 'specialist')) DEFAULT 'skilled',
  unit text NOT NULL,
  rates jsonb NOT NULL DEFAULT '{"good": 0, "better": 0, "best": 0}',
  productivity jsonb DEFAULT '{"output_per_day": 1, "unit": "sqm"}',
  location text DEFAULT 'gurgaon',
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Task Requirements: Junction table defining recipes
CREATE TABLE IF NOT EXISTS task_requirements (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  task_id uuid REFERENCES tasks(id) ON DELETE CASCADE,
  component_id uuid REFERENCES components(id) ON DELETE CASCADE,
  labor_id uuid REFERENCES labor_rates(id) ON DELETE CASCADE,
  quantity_per_sqm numeric DEFAULT 1,
  is_optional boolean DEFAULT false,
  requirement_type text CHECK (requirement_type IN ('material', 'labor', 'tool')) DEFAULT 'material',
  created_at timestamptz DEFAULT now()
);

-- Admin Users Table: Access control for admin panel
CREATE TABLE IF NOT EXISTS admin_users (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  email text UNIQUE NOT NULL,
  full_name text NOT NULL,
  role text CHECK (role IN ('super_admin', 'content_admin', 'viewer')) DEFAULT 'content_admin',
  is_active boolean DEFAULT true,
  last_login timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- UI Defaults: JSON configuration for default selections
CREATE TABLE IF NOT EXISTS ui_defaults (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  config_name text UNIQUE NOT NULL,
  config_data jsonb NOT NULL,
  version integer DEFAULT 1,
  is_active boolean DEFAULT true,
  created_by uuid REFERENCES admin_users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- User Projects: Store user project configurations
CREATE TABLE IF NOT EXISTS user_projects (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  project_name text NOT NULL,
  project_data jsonb NOT NULL,
  room_configurations jsonb DEFAULT '[]',
  selected_components jsonb DEFAULT '{}',
  total_cost numeric DEFAULT 0,
  is_shared boolean DEFAULT false,
  share_token text UNIQUE,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- User Overrides: Track manual overrides with transparency
CREATE TABLE IF NOT EXISTS user_overrides (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id uuid REFERENCES user_projects(id) ON DELETE CASCADE,
  component_id uuid REFERENCES components(id),
  original_quantity numeric,
  override_quantity numeric,
  original_rate numeric,
  override_rate numeric,
  override_reason text,
  created_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE components ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_requirements ENABLE ROW LEVEL SECURITY;
ALTER TABLE labor_rates ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE ui_defaults ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_overrides ENABLE ROW LEVEL SECURITY;

-- RLS Policies for Components (Public read, admin write)
CREATE POLICY "Components are viewable by everyone"
  ON components FOR SELECT
  USING (is_active = true);

CREATE POLICY "Components are manageable by admins"
  ON components FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE email = auth.jwt() ->> 'email' 
      AND is_active = true
    )
  );

-- RLS Policies for Tasks (Public read, admin write)
CREATE POLICY "Tasks are viewable by everyone"
  ON tasks FOR SELECT
  USING (is_active = true);

CREATE POLICY "Tasks are manageable by admins"
  ON tasks FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE email = auth.jwt() ->> 'email' 
      AND is_active = true
    )
  );

-- RLS Policies for Task Requirements (Public read, admin write)
CREATE POLICY "Task requirements are viewable by everyone"
  ON task_requirements FOR SELECT
  USING (true);

CREATE POLICY "Task requirements are manageable by admins"
  ON task_requirements FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE email = auth.jwt() ->> 'email' 
      AND is_active = true
    )
  );

-- RLS Policies for Labor Rates (Public read, admin write)
CREATE POLICY "Labor rates are viewable by everyone"
  ON labor_rates FOR SELECT
  USING (is_active = true);

CREATE POLICY "Labor rates are manageable by admins"
  ON labor_rates FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE email = auth.jwt() ->> 'email' 
      AND is_active = true
    )
  );

-- RLS Policies for Admin Users (Admin only)
CREATE POLICY "Admin users are manageable by super admins"
  ON admin_users FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE email = auth.jwt() ->> 'email' 
      AND role = 'super_admin'
      AND is_active = true
    )
  );

-- RLS Policies for UI Defaults (Public read, admin write)
CREATE POLICY "UI defaults are viewable by everyone"
  ON ui_defaults FOR SELECT
  USING (is_active = true);

CREATE POLICY "UI defaults are manageable by admins"
  ON ui_defaults FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE email = auth.jwt() ->> 'email' 
      AND is_active = true
    )
  );

-- RLS Policies for User Projects (User owns their projects)
CREATE POLICY "Users can view their own projects"
  ON user_projects FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id OR is_shared = true);

CREATE POLICY "Users can manage their own projects"
  ON user_projects FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);

-- RLS Policies for User Overrides (User owns their overrides)
CREATE POLICY "Users can view their own overrides"
  ON user_overrides FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_projects 
      WHERE id = project_id 
      AND user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage their own overrides"
  ON user_overrides FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_projects 
      WHERE id = project_id 
      AND user_id = auth.uid()
    )
  );

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_components_category ON components(category);
CREATE INDEX IF NOT EXISTS idx_components_brand ON components(brand);
CREATE INDEX IF NOT EXISTS idx_components_active ON components(is_active);
CREATE INDEX IF NOT EXISTS idx_tasks_category ON tasks(category);
CREATE INDEX IF NOT EXISTS idx_task_requirements_task_id ON task_requirements(task_id);
CREATE INDEX IF NOT EXISTS idx_task_requirements_component_id ON task_requirements(component_id);
CREATE INDEX IF NOT EXISTS idx_user_projects_user_id ON user_projects(user_id);
CREATE INDEX IF NOT EXISTS idx_user_projects_share_token ON user_projects(share_token);
CREATE INDEX IF NOT EXISTS idx_user_overrides_project_id ON user_overrides(project_id);