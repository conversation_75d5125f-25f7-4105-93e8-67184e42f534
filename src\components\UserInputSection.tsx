import React, { useState, useEffect } from 'react';
import { UserInputs, Location, PlotUnit, PlotInputMode } from '../types/calculator';
import { Info, MapPin, Calculator, RotateCcw } from 'lucide-react';
import { locationData } from '../data/locationMultipliers';

interface UserInputSectionProps {
  inputs: UserInputs;
  onInputChange: (inputs: UserInputs) => void;
  onCalculate: () => void;
  isLoading: boolean;
}

// Unit conversion functions
const convertToSqft = (value: number, unit: PlotUnit): number => {
  switch (unit) {
    case 'sqyard':
      return value * 9; // 1 sq yard = 9 sq ft
    case 'sqmeter':
      return value * 10.764; // 1 sq meter = 10.764 sq ft
    default:
      return value; // already in sq ft
  }
};

const convertFromSqft = (value: number, unit: PlotUnit): number => {
  switch (unit) {
    case 'sqyard':
      return value / 9;
    case 'sqmeter':
      return value / 10.764;
    default:
      return value;
  }
};

const getUnitLabel = (unit: PlotUnit): string => {
  switch (unit) {
    case 'sqyard':
      return 'Square Yards';
    case 'sqmeter':
      return 'Square Meters';
    default:
      return 'Square Feet';
  }
};

const getUnitAbbr = (unit: PlotUnit): string => {
  switch (unit) {
    case 'sqyard':
      return 'sq yd';
    case 'sqmeter':
      return 'sq m';
    default:
      return 'sq ft';
  }
};

export function UserInputSection({ inputs, onInputChange, onCalculate, isLoading }: UserInputSectionProps) {
  const [localPlotSize, setLocalPlotSize] = useState(convertFromSqft(inputs.plotSize, inputs.plotUnit || 'sqft'));
  const [localLength, setLocalLength] = useState(inputs.plotLength || 0);
  const [localWidth, setLocalWidth] = useState(inputs.plotWidth || 0);

  // Update local values when inputs change
  useEffect(() => {
    setLocalPlotSize(convertFromSqft(inputs.plotSize, inputs.plotUnit || 'sqft'));
    if (inputs.plotLength) setLocalLength(inputs.plotLength);
    if (inputs.plotWidth) setLocalWidth(inputs.plotWidth);
  }, [inputs.plotSize, inputs.plotUnit, inputs.plotLength, inputs.plotWidth]);

  const handleInputChange = (field: keyof UserInputs, value: number | boolean | Location | PlotUnit | PlotInputMode) => {
    onInputChange({ ...inputs, [field]: value });
  };

  const handlePlotSizeChange = (value: number) => {
    setLocalPlotSize(value);
    const plotSizeInSqft = convertToSqft(value, inputs.plotUnit || 'sqft');
    handleInputChange('plotSize', plotSizeInSqft);
  };

  const handlePlotUnitChange = (unit: PlotUnit) => {
    // Convert current plot size to new unit
    const newLocalSize = convertFromSqft(inputs.plotSize, unit);
    setLocalPlotSize(newLocalSize);
    handleInputChange('plotUnit', unit);
    
    // Also update dimension unit if in dimension mode
    if (inputs.plotInputMode === 'dimensions') {
      handleInputChange('plotDimensionUnit', unit);
    }
  };

  const handleDimensionChange = (field: 'length' | 'width', value: number) => {
    if (field === 'length') {
      setLocalLength(value);
      handleInputChange('plotLength', value);
    } else {
      setLocalWidth(value);
      handleInputChange('plotWidth', value);
    }

    // Calculate area from dimensions
    const length = field === 'length' ? value : localLength;
    const width = field === 'width' ? value : localWidth;
    
    if (length > 0 && width > 0) {
      const areaInCurrentUnit = length * width;
      const adjustedArea = areaInCurrentUnit * (inputs.plotShapeAdjustment / 100);
      const areaInSqft = convertToSqft(adjustedArea, inputs.plotDimensionUnit || inputs.plotUnit || 'sqft');

      setLocalPlotSize(areaInCurrentUnit);
      handleInputChange('plotSize', areaInSqft);
    }
  };

  const handlePlotInputModeChange = (mode: PlotInputMode) => {
    handleInputChange('plotInputMode', mode);
    
    if (mode === 'dimensions') {
      // Initialize dimensions based on current plot size (assuming square plot)
      const currentAreaInUnit = convertFromSqft(inputs.plotSize, inputs.plotUnit || 'sqft');
      const sideLength = Math.sqrt(currentAreaInUnit);
      
      setLocalLength(sideLength);
      setLocalWidth(sideLength);
      handleInputChange('plotLength', sideLength);
      handleInputChange('plotWidth', sideLength);
      handleInputChange('plotDimensionUnit', inputs.plotUnit || 'sqft');
      handleInputChange('plotShapeAdjustment', 100);
    } else {
      // Switching back to area mode - ensure plot size is correct
      setLocalPlotSize(convertFromSqft(inputs.plotSize, inputs.plotUnit || 'sqft'));
    }
  };

  const resetToSquare = () => {
    if (inputs.plotInputMode === 'dimensions') {
      const currentAreaInUnit = convertFromSqft(inputs.plotSize, inputs.plotDimensionUnit || inputs.plotUnit || 'sqft');
      const sideLength = Math.sqrt(currentAreaInUnit);
      
      setLocalLength(sideLength);
      setLocalWidth(sideLength);
      handleInputChange('plotLength', sideLength);
      handleInputChange('plotWidth', sideLength);
      handleInputChange('plotShapeAdjustment', 100);
    }
  };

  // Calculate ground coverage area in real-time
  const groundCoverageArea = Math.round((inputs.plotSize * inputs.constructionPercentage) / 100);

  // Calculate current plot area from dimensions if in dimension mode
  const currentPlotArea = inputs.plotInputMode === 'dimensions' && localLength > 0 && localWidth > 0 
    ? localLength * localWidth * (inputs.plotShapeAdjustment / 100)
    : localPlotSize;

  return (
    <div className="bg-white rounded-xl shadow-lg p-4 sm:p-6 mb-6 sm:mb-8">
      <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4 sm:mb-6">Project Configuration</h3>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        {/* Location Selection */}
        <div>
          <label 
            htmlFor="location-select"
            className="flex items-center text-sm font-semibold text-gray-700 mb-2"
          >
            <MapPin className="w-4 h-4 mr-2 text-blue-600" aria-hidden="true" />
            Project Location
          </label>
          <select
            id="location-select"
            value={inputs.location}
            onChange={(e) => handleInputChange('location', e.target.value as Location)}
            className="enhanced-dropdown w-full"
            aria-describedby="location-description"
          >
            {Object.values(locationData).map((location) => (
              <option key={location.id} value={location.id} className="dropdown-option-visible">
                {location.name}
              </option>
            ))}
          </select>
          <p id="location-description" className="text-xs text-gray-500 mt-1">
            {locationData[inputs.location].description}
          </p>
        </div>

        {/* Plot Input Mode Selection */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-2">
            Plot Input Method
          </label>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={() => handlePlotInputModeChange('area')}
              className={`flex-1 px-3 py-2 text-sm rounded-lg border-2 transition-all min-h-[44px] font-medium ${
                inputs.plotInputMode === 'area' 
                  ? 'bg-blue-600 text-white border-blue-600 shadow-lg' 
                  : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50'
              }`}
            >
              Total Area
            </button>
            <button
              type="button"
              onClick={() => handlePlotInputModeChange('dimensions')}
              className={`flex-1 px-3 py-2 text-sm rounded-lg border-2 transition-all min-h-[44px] font-medium ${
                inputs.plotInputMode === 'dimensions' 
                  ? 'bg-blue-600 text-white border-blue-600 shadow-lg' 
                  : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50'
              }`}
            >
              Length × Width
            </button>
          </div>
        </div>

        {/* Plot Unit Selection */}
        <div>
          <label 
            htmlFor="plot-unit"
            className="block text-sm font-semibold text-gray-700 mb-2"
          >
            Measurement Unit
          </label>
          <select
            id="plot-unit"
            value={inputs.plotUnit || 'sqft'}
            onChange={(e) => handlePlotUnitChange(e.target.value as PlotUnit)}
            className="enhanced-dropdown w-full"
          >
            <option value="sqft" className="dropdown-option-visible">Square Feet (sq ft)</option>
            <option value="sqyard" className="dropdown-option-visible">Square Yards (sq yd)</option>
            <option value="sqmeter" className="dropdown-option-visible">Square Meters (sq m)</option>
          </select>
        </div>

        {/* Construction Area Percentage with Real-time Display */}
        <div>
          <label 
            htmlFor="construction-percentage"
            className="flex items-center text-sm font-semibold text-gray-700 mb-2"
          >
            Construction Area on Plot
            <button
              type="button"
              className="ml-2 p-1 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-describedby="construction-percentage-tooltip"
              onMouseEnter={(e) => {
                const tooltip = document.getElementById('construction-percentage-tooltip');
                if (tooltip) tooltip.classList.remove('opacity-0', 'invisible');
              }}
              onMouseLeave={(e) => {
                const tooltip = document.getElementById('construction-percentage-tooltip');
                if (tooltip) tooltip.classList.add('opacity-0', 'invisible');
              }}
            >
              <Info className="w-4 h-4 text-gray-400" aria-hidden="true" />
              <span className="sr-only">More information about construction area percentage</span>
            </button>
            <div 
              id="construction-percentage-tooltip"
              className="absolute z-10 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg opacity-0 invisible transition-opacity pointer-events-none whitespace-nowrap"
              role="tooltip"
            >
              Percentage of plot area covered by ground floor construction
            </div>
          </label>
          <select
            id="construction-percentage"
            value={inputs.constructionPercentage}
            onChange={(e) => handleInputChange('constructionPercentage', Number(e.target.value))}
            className="enhanced-dropdown w-full"
          >
            <option value={70} className="dropdown-option-visible">70%</option>
            <option value={80} className="dropdown-option-visible">80%</option>
            <option value={90} className="dropdown-option-visible">90%</option>
            <option value={100} className="dropdown-option-visible">100%</option>
          </select>
          {/* Real-time Ground Coverage Display */}
          <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="text-sm text-blue-800 font-medium">
              Equals: {groundCoverageArea.toLocaleString()} sq ft
            </div>
            <div className="text-xs text-blue-600">Ground coverage area</div>
          </div>
        </div>
      </div>

      {/* Plot Size/Dimensions Input Section */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg border-2 border-gray-200">
        <h4 className="text-base font-semibold text-gray-800 mb-4">
          Plot {inputs.plotInputMode === 'area' ? 'Area' : 'Dimensions'}
        </h4>

        {inputs.plotInputMode === 'area' ? (
          /* Total Area Input */
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label 
                htmlFor="plot-size"
                className="block text-sm font-semibold text-gray-700 mb-2"
              >
                Total Plot Area
              </label>
              <div className="flex gap-2">
                <input
                  id="plot-size"
                  type="number"
                  value={localPlotSize || ''}
                  onChange={(e) => handlePlotSizeChange(Number(e.target.value))}
                  className="form-input flex-1"
                  placeholder={`e.g., ${inputs.plotUnit === 'sqyard' ? '111' : inputs.plotUnit === 'sqmeter' ? '93' : '1000'}`}
                  min="10"
                  max={inputs.plotUnit === 'sqyard' ? '1111' : inputs.plotUnit === 'sqmeter' ? '929' : '10000'}
                />
                <div className="flex items-center px-3 py-2 bg-gray-100 border-2 border-gray-300 rounded-lg text-sm font-medium text-gray-700 min-w-[80px] justify-center">
                  {getUnitAbbr(inputs.plotUnit || 'sqft')}
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                ({getUnitLabel(inputs.plotUnit || 'sqft')})
              </p>
            </div>

            <div className="flex items-end">
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg w-full">
                <div className="text-sm text-blue-800 font-medium">
                  = {inputs.plotSize.toLocaleString()} sq ft
                </div>
                <div className="text-xs text-blue-600">Converted to square feet</div>
              </div>
            </div>
          </div>
        ) : (
          /* Length × Width Input */
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label 
                  htmlFor="plot-length"
                  className="block text-sm font-semibold text-gray-700 mb-2"
                >
                  Plot Length
                </label>
                <div className="flex gap-2">
                  <input
                    id="plot-length"
                    type="number"
                    value={localLength || ''}
                    onChange={(e) => handleDimensionChange('length', Number(e.target.value))}
                    className="form-input flex-1"
                    placeholder="e.g., 40"
                    min="1"
                    max="500"
                    step="0.1"
                  />
                  <div className="flex items-center px-3 py-2 bg-gray-100 border-2 border-gray-300 rounded-lg text-sm font-medium text-gray-700 min-w-[60px] justify-center">
                    {inputs.plotDimensionUnit === 'sqyard' ? 'yd' : inputs.plotDimensionUnit === 'sqmeter' ? 'm' : 'ft'}
                  </div>
                </div>
              </div>

              <div>
                <label 
                  htmlFor="plot-width"
                  className="block text-sm font-semibold text-gray-700 mb-2"
                >
                  Plot Width
                </label>
                <div className="flex gap-2">
                  <input
                    id="plot-width"
                    type="number"
                    value={localWidth || ''}
                    onChange={(e) => handleDimensionChange('width', Number(e.target.value))}
                    className="form-input flex-1"
                    placeholder="e.g., 25"
                    min="1"
                    max="500"
                    step="0.1"
                  />
                  <div className="flex items-center px-3 py-2 bg-gray-100 border-2 border-gray-300 rounded-lg text-sm font-medium text-gray-700 min-w-[60px] justify-center">
                    {inputs.plotDimensionUnit === 'sqyard' ? 'yd' : inputs.plotDimensionUnit === 'sqmeter' ? 'm' : 'ft'}
                  </div>
                </div>
              </div>
            </div>

            {/* Plot Shape Adjustment */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label 
                  htmlFor="shape-adjustment"
                  className="block text-sm font-semibold text-gray-700 mb-2"
                >
                  Plot Shape Adjustment
                  <Info className="inline w-3 h-3 ml-1 text-gray-400" title="Adjust for irregular plot shapes" />
                </label>
                <div className="flex gap-2">
                  <input
                    id="shape-adjustment"
                    type="number"
                    value={inputs.plotShapeAdjustment || 100}
                    onChange={(e) => {
                      const newAdjustment = Number(e.target.value);
                      handleInputChange('plotShapeAdjustment', newAdjustment);
                      
                      // Recalculate area with new adjustment
                      if (localLength > 0 && localWidth > 0) {
                        const areaInCurrentUnit = localLength * localWidth;
                        const adjustedArea = areaInCurrentUnit * (newAdjustment / 100);
                        const areaInSqft = convertToSqft(adjustedArea, inputs.plotDimensionUnit || inputs.plotUnit || 'sqft');
                        handleInputChange('plotSize', areaInSqft);
                      }
                    }}
                    className="form-input flex-1"
                    min="50"
                    max="100"
                    step="5"
                  />
                  <div className="flex items-center px-3 py-2 bg-gray-100 border-2 border-gray-300 rounded-lg text-sm font-medium text-gray-700 min-w-[40px] justify-center">
                    %
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  100% = Perfect rectangle, 90% = Slightly irregular, 80% = Very irregular
                </p>
              </div>

              <div className="flex flex-col justify-end">
                <button
                  type="button"
                  onClick={resetToSquare}
                  className="flex items-center justify-center gap-2 px-3 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg transition-colors text-sm min-h-[44px] border-2 border-gray-300"
                >
                  <RotateCcw className="w-4 h-4" />
                  Make Square
                </button>
              </div>
            </div>

            {/* Calculated Area Display */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="text-sm text-green-800 font-medium">
                  Calculated Area: {currentPlotArea.toLocaleString()} {getUnitAbbr(inputs.plotDimensionUnit || inputs.plotUnit || 'sqft')}
                </div>
                <div className="text-xs text-green-600">
                  {localLength.toFixed(1)} × {localWidth.toFixed(1)} × {inputs.plotShapeAdjustment}%
                </div>
              </div>

              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="text-sm text-blue-800 font-medium">
                  = {inputs.plotSize.toLocaleString()} sq ft
                </div>
                <div className="text-xs text-blue-600">Converted for calculation</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Number of Residential Floors */}
      <div className="mt-6">
        <label 
          htmlFor="number-of-floors"
          className="block text-sm font-semibold text-gray-700 mb-2"
        >
          Number of Residential Floors
        </label>
        <select
          id="number-of-floors"
          value={inputs.numberOfFloors}
          onChange={(e) => handleInputChange('numberOfFloors', Number(e.target.value))}
          className="enhanced-dropdown w-full max-w-md"
        >
          <option value={1} className="dropdown-option-visible">Ground Floor Only</option>
          <option value={2} className="dropdown-option-visible">G + 1 (2 Floors)</option>
          <option value={3} className="dropdown-option-visible">G + 2 (3 Floors)</option>
          <option value={4} className="dropdown-option-visible">G + 3 (4 Floors)</option>
          <option value={5} className="dropdown-option-visible">G + 4 (5 Floors)</option>
        </select>
      </div>

      {/* Structural Options */}
      <fieldset className="mt-6">
        <legend className="text-base sm:text-lg font-semibold text-gray-800 mb-4">Structural Options</legend>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {/* Basement Option */}
          <div className="flex items-start space-x-3 p-4 border-2 border-blue-200 rounded-lg hover:bg-blue-50 transition-colors">
            <input
              type="checkbox"
              id="basement"
              checked={inputs.hasBasement}
              onChange={(e) => handleInputChange('hasBasement', e.target.checked)}
              className="mt-1 w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
              aria-describedby="basement-description"
            />
            <div className="flex-1">
              <label htmlFor="basement" className="block text-sm font-medium text-gray-700 cursor-pointer">
                Include Basement
              </label>
              <p id="basement-description" className="text-xs text-gray-500 mt-1 leading-relaxed">
                Adds excavation, retaining walls, and waterproofing. Increases foundation requirements.
              </p>
            </div>
          </div>

          {/* Stilt Parking Option */}
          <div className="flex items-start space-x-3 p-4 border-2 border-blue-200 rounded-lg hover:bg-blue-50 transition-colors">
            <input
              type="checkbox"
              id="stilt"
              checked={inputs.hasStiltParking}
              onChange={(e) => handleInputChange('hasStiltParking', e.target.checked)}
              className="mt-1 w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
              aria-describedby="stilt-description"
            />
            <div className="flex-1">
              <label htmlFor="stilt" className="block text-sm font-medium text-gray-700 cursor-pointer">
                Stilt Parking
              </label>
              <p id="stilt-description" className="text-xs text-gray-500 mt-1 leading-relaxed">
                Ground floor used for parking. No walls or finishing on ground level.
              </p>
            </div>
          </div>
        </div>
      </fieldset>

      <div className="mt-6">
        <button
          onClick={onCalculate}
          disabled={isLoading || !inputs.plotSize}
          className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-400 disabled:to-gray-400 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] disabled:transform-none disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-base sm:text-lg min-h-[44px]"
          aria-describedby={isLoading ? "loading-status" : undefined}
        >
          {isLoading ? 'Calculating...' : 'Calculate My Estimated Cost'}
        </button>
        {isLoading && (
          <div id="loading-status" className="sr-only" aria-live="polite">
            Calculation in progress, please wait
          </div>
        )}
      </div>
    </div>
  );
}