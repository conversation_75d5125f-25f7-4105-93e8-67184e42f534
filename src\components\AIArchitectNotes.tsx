import React, { useState, useEffect } from 'react';
import { Brain, AlertTriangle, CheckCircle, Lightbulb } from 'lucide-react';
import { CalculationResult, RoomMaterialSelection } from '../types/calculator';

interface AIInsight {
  id: string;
  type: 'warning' | 'suggestion' | 'optimization' | 'consistency';
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
  actionable: boolean;
}

interface AIArchitectNotesProps {
  result: CalculationResult;
  roomSelections: RoomMaterialSelection[];
}

export function AIArchitectNotes({ result, roomSelections }: AIArchitectNotesProps) {
  const [insights, setInsights] = useState<AIInsight[]>([]);

  useEffect(() => {
    generateAIInsights();
  }, [result, roomSelections]);

  const generateAIInsights = () => {
    const newInsights: AIInsight[] = [];

    // Quality Tier Consistency Analysis
    analyzeQualityConsistency(newInsights);
    
    // Room Material Mismatch Analysis
    analyzeRoomMaterialMismatches(newInsights);
    
    // Cost Optimization Suggestions
    analyzeCostOptimizations(newInsights);
    
    // Structural Efficiency Analysis
    analyzeStructuralEfficiency(newInsights);

    setInsights(newInsights);
  };

  const analyzeQualityConsistency = (insights: AIInsight[]) => {
    const qualityTier = result.qualityTier;
    
    // Check for inconsistent material selections
    const inconsistentItems = result.sections.flatMap(section => 
      section.items.filter(item => {
        if (item.selectedMaterial) {
          const isHighEnd = item.selectedMaterial.includes('Premium') || 
                           item.selectedMaterial.includes('Luxury') || 
                           item.selectedMaterial.includes('Jaquar') ||
                           item.selectedMaterial.includes('Schneider');
          const isBasic = item.selectedMaterial.includes('Basic') || 
                         item.selectedMaterial.includes('Local') ||
                         item.selectedMaterial.includes('Economy');
          
          if (qualityTier === 'good' && isHighEnd) return true;
          if (qualityTier === 'best' && isBasic) return true;
        }
        return false;
      })
    );

    if (inconsistentItems.length > 0) {
      insights.push({
        id: 'quality_consistency',
        type: 'warning',
        title: 'Quality Tier Inconsistency Detected',
        message: `You've selected ${qualityTier} quality tier but chosen ${inconsistentItems.length} items that don't match this standard. Consider aligning all selections for a cohesive project quality.`,
        severity: 'medium',
        actionable: true
      });
    }
  };

  const analyzeRoomMaterialMismatches = (insights: AIInsight[]) => {
    if (roomSelections.length === 0) return;

    // Group rooms by type for analysis
    const bathroomSelections = roomSelections.filter(room => room.roomType === 'Bathroom');
    const bedroomSelections = roomSelections.filter(room => room.roomType === 'Bedroom');

    // Check for bathroom material mismatches
    bathroomSelections.forEach(bathroom => {
      const isExpensiveFlooring = bathroom.flooringMaterial.includes('Italian Marble') || 
                                 bathroom.flooringMaterial.includes('Granite');
      const isCheapPaint = bathroom.paintMaterial.includes('Economy');

      if (isExpensiveFlooring && isCheapPaint) {
        insights.push({
          id: `bathroom_mismatch_${bathroom.roomId}`,
          type: 'suggestion',
          title: 'Bathroom Finish Mismatch',
          message: `In your bathroom, you've chosen premium ${bathroom.flooringMaterial} flooring but ${bathroom.paintMaterial} paint. Consider upgrading to moisture-resistant premium paint for better durability and aesthetics.`,
          severity: 'medium',
          actionable: true
        });
      }
    });

    // Check for bedroom consistency
    if (bedroomSelections.length > 1) {
      const flooringTypes = [...new Set(bedroomSelections.map(room => room.flooringMaterial))];
      if (flooringTypes.length > 2) {
        insights.push({
          id: 'bedroom_flooring_variety',
          type: 'suggestion',
          title: 'Too Many Flooring Types in Bedrooms',
          message: `You've selected ${flooringTypes.length} different flooring materials for bedrooms. For design consistency and cost efficiency, consider using 1-2 flooring types across all bedrooms.`,
          severity: 'low',
          actionable: true
        });
      }
    }
  };

  const analyzeCostOptimizations = (insights: AIInsight[]) => {
    const totalCost = result.totalCost;
    const structureCost = result.sections.find(s => s.id === 'foundation')?.subtotal || 0;
    const finishesCost = result.sections.find(s => s.id === 'masonry')?.subtotal || 0;

    // Check if finishes cost is disproportionately high
    const finishesPercentage = (finishesCost / totalCost) * 100;
    if (finishesPercentage > 45) {
      insights.push({
        id: 'high_finishes_cost',
        type: 'optimization',
        title: 'High Finishing Costs Detected',
        message: `Your finishing costs are ${finishesPercentage.toFixed(1)}% of total budget. Consider optimizing material selections in non-critical areas to balance your budget better.`,
        severity: 'medium',
        actionable: true
      });
    }

    // Check for potential savings in electrical points
    const electricalItem = result.sections
      .flatMap(s => s.items)
      .find(item => item.id === 'electrical');
    
    if (electricalItem && electricalItem.quantity > result.quantities.totalBuiltUpArea * 0.1) {
      insights.push({
        id: 'excessive_electrical_points',
        type: 'optimization',
        title: 'Electrical Points Optimization',
        message: `You have ${electricalItem.quantity} electrical points for ${result.quantities.totalBuiltUpArea} sq ft. Consider if all points are necessary - reducing by 10-15% could save ₹${((electricalItem.quantity * 0.15 * electricalItem.rate)).toLocaleString()}.`,
        severity: 'low',
        actionable: true
      });
    }
  };

  const analyzeStructuralEfficiency = (insights: AIInsight[]) => {
    const hasBasement = result.quantities.basementArea > 0;
    const hasStilt = result.quantities.stiltArea > 0;
    const floors = Math.ceil(result.quantities.totalBuiltUpArea / result.quantities.groundCoverageArea);

    if (hasBasement && floors <= 2) {
      insights.push({
        id: 'basement_efficiency',
        type: 'suggestion',
        title: 'Basement Utilization Opportunity',
        message: `You have a basement but only ${floors} floors above. Consider adding another floor instead of the basement for better cost efficiency and natural lighting.`,
        severity: 'medium',
        actionable: true
      });
    }

    if (hasStilt && result.quantities.plotSize < 1500) {
      insights.push({
        id: 'stilt_small_plot',
        type: 'warning',
        title: 'Stilt Parking on Small Plot',
        message: `Stilt parking on a ${result.quantities.plotSize} sq ft plot may not be cost-effective. Consider a basement or optimize the ground floor layout for parking.`,
        severity: 'medium',
        actionable: true
      });
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-orange-500" />;
      case 'suggestion':
        return <Lightbulb className="w-5 h-5 text-blue-500" />;
      case 'optimization':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      default:
        return <Brain className="w-5 h-5 text-purple-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'border-red-200 bg-red-50';
      case 'medium':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-blue-200 bg-blue-50';
    }
  };

  if (insights.length === 0) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
        <div className="flex items-center gap-3">
          <CheckCircle className="w-5 h-5 text-green-600" />
          <div>
            <h3 className="font-semibold text-green-800">Excellent Configuration!</h3>
            <p className="text-green-600 text-sm">Your material selections and project configuration are well-balanced and optimized.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
      <div className="flex items-center gap-3 mb-4">
        <Brain className="w-6 h-6 text-purple-600" />
        <div>
          <h3 className="text-xl font-semibold text-gray-800">AI Architect's Analysis</h3>
          <p className="text-gray-600 text-sm">Smart insights to optimize your project</p>
        </div>
      </div>

      <div className="space-y-4">
        {insights.map((insight) => (
          <div
            key={insight.id}
            className={`border rounded-lg p-4 ${getSeverityColor(insight.severity)}`}
          >
            <div className="flex items-start gap-3">
              {getInsightIcon(insight.type)}
              <div className="flex-1">
                <h4 className="font-semibold text-gray-800 mb-1">{insight.title}</h4>
                <p className="text-gray-700 text-sm leading-relaxed">{insight.message}</p>
                {insight.actionable && (
                  <button className="mt-2 text-xs text-blue-600 hover:text-blue-800 font-medium">
                    View Recommendations →
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}