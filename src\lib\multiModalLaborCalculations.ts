import { LaborRate } from './supabase';

export interface EnhancedLaborRate extends LaborRate {
  charging_models?: {
    primary_model: 'per_unit' | 'lump_sum' | 'per_day' | 'per_sqft';
    lump_sum_rates?: {
      small_project: { good: number; better: number; best: number };
      medium_project: { good: number; better: number; best: number };
      large_project: { good: number; better: number; best: number };
    };
    daily_rates?: { good: number; better: number; best: number };
    per_sqft_rates?: { good: number; better: number; best: number };
    recommended_for?: {
      project_sizes: string[];
      project_types: string[];
      notes: string;
    };
  };
}

export interface ProjectContext {
  total_area: number;
  project_type: 'residential' | 'commercial' | 'industrial';
  duration_months: number;
  quality_tier: 'good' | 'better' | 'best';
  budget_preference: 'cost_effective' | 'balanced' | 'premium';
}

export interface AlternativeCost {
  model: string;
  cost: number;
  savings: number;
  savings_percentage: number;
  recommended: boolean;
  reasoning: string;
  timeline_days?: number;
}

export interface LaborCostResult {
  primary_cost: number;
  model_used: string;
  alternatives: AlternativeCost[];
  breakdown: {
    base_rate: number;
    quantity: number;
    total: number;
    unit: string;
    timeline_days: number;
  };
  recommendations: string[];
  efficiency_rating: 'excellent' | 'good' | 'fair' | 'poor';
}

export function getProjectSize(area: number): 'small_project' | 'medium_project' | 'large_project' {
  if (area < 1000) return 'small_project';
  if (area <= 2000) return 'medium_project';
  return 'large_project';
}

export function calculateTimelineForModel(
  model: string,
  quantity: number,
  laborRate: EnhancedLaborRate,
  projectContext: ProjectContext
): number {
  switch (model) {
    case 'per_unit':
      return quantity / (laborRate.productivity?.output_per_day || 1);
    
    case 'per_day':
      return quantity / (laborRate.productivity?.output_per_day || 1);
    
    case 'lump_sum':
      // Lump sum projects typically have optimized timelines
      const baseTimeline = quantity / (laborRate.productivity?.output_per_day || 1);
      return baseTimeline * 0.85; // 15% faster due to dedicated team
    
    case 'per_sqft':
      // Area-based work has different productivity patterns
      const coverage = getCoverageFactorForCategory(laborRate.category);
      return projectContext.total_area / coverage;
    
    default:
      return quantity / (laborRate.productivity?.output_per_day || 1);
  }
}

function getCoverageFactorForCategory(category: string): number {
  const coverageFactors: Record<string, number> = {
    'electrical': 50, // sqft per day for electrical work
    'plumbing': 40,   // sqft per day for plumbing work
    'flooring': 80,   // sqft per day for flooring
    'finishes': 100,  // sqft per day for finishes
    'masonry': 60,    // sqft per day for masonry
    'foundation': 30, // sqft per day for foundation
    'specialized': 35 // sqft per day for specialized work
  };

  return coverageFactors[category] || 50;
}

function getAreaForCategory(category: string, quantity: number, totalProjectArea: number): number {
  // For area-based categories, use the quantity as area
  // For non-area categories, use a proportion of total project area
  const areaBased = ['masonry', 'finishes', 'flooring', 'foundation'];

  if (areaBased.includes(category)) {
    return quantity; // quantity already represents area for these categories
  }

  // For electrical/plumbing/specialized, use total project area
  return totalProjectArea;
}

export function selectOptimalChargingModel(
  laborRate: EnhancedLaborRate,
  projectContext: ProjectContext
): { primary: string; reasoning: string } {
  
  // If no charging models defined, use per_unit
  if (!laborRate.charging_models) {
    return {
      primary: 'per_unit',
      reasoning: 'Default per-unit pricing (no alternative models configured)'
    };
  }

  const { total_area, project_type, duration_months, budget_preference } = projectContext;
  const category = laborRate.category;

  // Electrical work logic
  if (category === 'electrical') {
    if (total_area < 1000 && duration_months < 2) {
      return {
        primary: 'per_unit',
        reasoning: 'Small electrical project - per point pricing most accurate'
      };
    }
    
    if (duration_months > 6) {
      return {
        primary: 'per_day',
        reasoning: 'Long duration project - daily rates more economical'
      };
    }
    
    if (project_type === 'commercial' && laborRate.charging_models.per_sqft_rates) {
      return {
        primary: 'per_sqft',
        reasoning: 'Commercial electrical work - area-based pricing standard'
      };
    }
    
    if (budget_preference === 'cost_effective' && laborRate.charging_models.lump_sum_rates) {
      return {
        primary: 'lump_sum',
        reasoning: 'Cost-effective option - lump sum typically 15-20% lower'
      };
    }
  }

  // Plumbing work logic
  if (category === 'plumbing') {
    if (total_area > 2000 && laborRate.charging_models.lump_sum_rates) {
      return {
        primary: 'lump_sum',
        reasoning: 'Large plumbing project - package deal more efficient'
      };
    }
    
    if (project_type === 'commercial' && laborRate.charging_models.per_sqft_rates) {
      return {
        primary: 'per_sqft',
        reasoning: 'Commercial plumbing - area-based pricing common'
      };
    }
  }

  // Masonry work logic
  if (category === 'masonry') {
    if (laborRate.charging_models.per_sqft_rates) {
      return {
        primary: 'per_sqft',
        reasoning: 'Masonry work - per sqft pricing most common'
      };
    }
    
    if (duration_months > 4 && laborRate.charging_models.daily_rates) {
      return {
        primary: 'per_day',
        reasoning: 'Long masonry project - daily rates for flexibility'
      };
    }
  }

  // Finishes and flooring logic
  if (['finishes', 'flooring'].includes(category)) {
    if (laborRate.charging_models.per_sqft_rates) {
      return {
        primary: 'per_sqft',
        reasoning: 'Finishes/flooring - area-based pricing standard'
      };
    }
  }

  // Default to primary model or per_unit
  return {
    primary: laborRate.charging_models.primary_model || 'per_unit',
    reasoning: 'Using configured primary model'
  };
}

export function calculateLaborCost(
  laborRate: EnhancedLaborRate,
  quantity: number,
  projectContext: ProjectContext
): LaborCostResult {

  console.log(`🧮 calculateLaborCost called for ${laborRate.name}:`, {
    quantity,
    quality_tier: projectContext.quality_tier,
    rates: laborRate.rates,
    rate_for_tier: laborRate.rates?.[projectContext.quality_tier]
  });

  const optimalModel = selectOptimalChargingModel(laborRate, projectContext);
  let primaryCost = 0;
  let timeline = 0;
  const alternatives: AlternativeCost[] = [];

  console.log(`🎯 Optimal model selected: ${optimalModel.primary}`);

  // Calculate primary cost
  switch (optimalModel.primary) {
    case 'per_unit':
      const rateForTier = laborRate.rates[projectContext.quality_tier];
      primaryCost = quantity * rateForTier;
      console.log(`💰 Per-unit calculation: ${quantity} × ₹${rateForTier} = ₹${primaryCost}`);
      timeline = calculateTimelineForModel('per_unit', quantity, laborRate, projectContext);
      break;
      
    case 'lump_sum':
      const projectSize = getProjectSize(projectContext.total_area);
      const lumpSumRates = laborRate.charging_models?.lump_sum_rates?.[projectSize];
      if (lumpSumRates) {
        primaryCost = lumpSumRates[projectContext.quality_tier];
        timeline = calculateTimelineForModel('lump_sum', quantity, laborRate, projectContext);
      } else {
        // Fallback to per_unit
        primaryCost = quantity * laborRate.rates[projectContext.quality_tier];
        timeline = calculateTimelineForModel('per_unit', quantity, laborRate, projectContext);
      }
      break;
      
    case 'per_day':
      const dailyRates = laborRate.charging_models?.daily_rates;
      if (dailyRates) {
        const estimatedDays = calculateTimelineForModel('per_day', quantity, laborRate, projectContext);
        primaryCost = estimatedDays * dailyRates[projectContext.quality_tier];
        timeline = estimatedDays;
      } else {
        // Fallback to per_unit
        primaryCost = quantity * laborRate.rates[projectContext.quality_tier];
        timeline = calculateTimelineForModel('per_unit', quantity, laborRate, projectContext);
      }
      break;
      
    case 'per_sqft':
      const perSqftRates = laborRate.charging_models?.per_sqft_rates;
      if (perSqftRates) {
        // For per_sqft pricing, use the quantity as the area (since quantity represents area for area-based work)
        const areaToUse = getAreaForCategory(laborRate.category, quantity, projectContext.total_area);
        primaryCost = areaToUse * perSqftRates[projectContext.quality_tier];
        timeline = calculateTimelineForModel('per_sqft', quantity, laborRate, projectContext);
      } else {
        // Fallback to per_unit
        primaryCost = quantity * laborRate.rates[projectContext.quality_tier];
        timeline = calculateTimelineForModel('per_unit', quantity, laborRate, projectContext);
      }
      break;
  }

  // Calculate alternatives
  const models = ['per_unit', 'lump_sum', 'per_day', 'per_sqft'];
  
  for (const model of models) {
    if (model === optimalModel.primary) continue;
    
    let altCost = 0;
    let altTimeline = 0;
    let canCalculate = true;
    
    switch (model) {
      case 'per_unit':
        altCost = quantity * laborRate.rates[projectContext.quality_tier];
        altTimeline = calculateTimelineForModel('per_unit', quantity, laborRate, projectContext);
        break;
        
      case 'lump_sum':
        const projectSize = getProjectSize(projectContext.total_area);
        const lumpSumRates = laborRate.charging_models?.lump_sum_rates?.[projectSize];
        if (lumpSumRates) {
          altCost = lumpSumRates[projectContext.quality_tier];
          altTimeline = calculateTimelineForModel('lump_sum', quantity, laborRate, projectContext);
        } else {
          canCalculate = false;
        }
        break;
        
      case 'per_day':
        const dailyRates = laborRate.charging_models?.daily_rates;
        if (dailyRates) {
          const estimatedDays = calculateTimelineForModel('per_day', quantity, laborRate, projectContext);
          altCost = estimatedDays * dailyRates[projectContext.quality_tier];
          altTimeline = estimatedDays;
        } else {
          canCalculate = false;
        }
        break;
        
      case 'per_sqft':
        const perSqftRates = laborRate.charging_models?.per_sqft_rates;
        if (perSqftRates) {
          const areaToUse = getAreaForCategory(laborRate.category, quantity, projectContext.total_area);
          altCost = areaToUse * perSqftRates[projectContext.quality_tier];
          altTimeline = calculateTimelineForModel('per_sqft', quantity, laborRate, projectContext);
        } else {
          canCalculate = false;
        }
        break;
    }
    
    if (canCalculate && altCost > 0) {
      const savings = primaryCost - altCost;
      const savingsPercentage = (savings / primaryCost) * 100;
      
      alternatives.push({
        model,
        cost: altCost,
        savings,
        savings_percentage: savingsPercentage,
        recommended: Math.abs(savingsPercentage) > 10, // Recommend if >10% difference
        reasoning: generateAlternativeReasoning(model, savingsPercentage, laborRate.category),
        timeline_days: altTimeline
      });
    }
  }

  // Sort alternatives by savings (highest savings first)
  alternatives.sort((a, b) => b.savings - a.savings);

  // Generate recommendations
  const recommendations = generateRecommendations(
    optimalModel.primary,
    alternatives,
    laborRate.category,
    projectContext
  );

  // Calculate efficiency rating
  const efficiencyRating = calculateEfficiencyRating(
    optimalModel.primary,
    alternatives,
    projectContext
  );

  return {
    primary_cost: primaryCost,
    model_used: optimalModel.primary,
    alternatives,
    breakdown: {
      base_rate: laborRate.rates[projectContext.quality_tier],
      quantity,
      total: primaryCost,
      unit: laborRate.unit,
      timeline_days: timeline
    },
    recommendations,
    efficiency_rating
  };
}

function generateAlternativeReasoning(
  model: string,
  savingsPercentage: number,
  category: string
): string {
  const modelNames = {
    'per_unit': 'Per Unit',
    'lump_sum': 'Lump Sum',
    'per_day': 'Daily Rate',
    'per_sqft': 'Per Sqft'
  };

  if (savingsPercentage > 15) {
    return `${modelNames[model]} could save ${Math.abs(savingsPercentage).toFixed(1)}% - significant cost reduction`;
  } else if (savingsPercentage > 5) {
    return `${modelNames[model]} offers ${Math.abs(savingsPercentage).toFixed(1)}% savings - moderate cost benefit`;
  } else if (savingsPercentage < -15) {
    return `${modelNames[model]} costs ${Math.abs(savingsPercentage).toFixed(1)}% more - premium option`;
  } else {
    return `${modelNames[model]} similar cost - alternative pricing structure`;
  }
}

function generateRecommendations(
  primaryModel: string,
  alternatives: AlternativeCost[],
  category: string,
  projectContext: ProjectContext
): string[] {
  const recommendations: string[] = [];
  
  // Check for significant savings opportunities
  const bestSavings = alternatives.find(alt => alt.savings > 0 && alt.savings_percentage > 15);
  if (bestSavings) {
    recommendations.push(
      `Consider ${bestSavings.model} pricing - could save ₹${bestSavings.savings.toLocaleString()} (${bestSavings.savings_percentage.toFixed(1)}%)`
    );
  }

  // Category-specific recommendations
  if (category === 'electrical' && projectContext.project_type === 'residential') {
    recommendations.push('For residential electrical work, per-point pricing typically provides most accurate estimates');
  }
  
  if (category === 'plumbing' && projectContext.total_area > 2000) {
    recommendations.push('Large plumbing projects often benefit from package deals (lump sum pricing)');
  }
  
  if (category === 'masonry' && projectContext.duration_months > 3) {
    recommendations.push('Extended masonry work may benefit from daily rate pricing for flexibility');
  }

  // Budget-based recommendations
  if (projectContext.budget_preference === 'cost_effective') {
    const cheapestOption = alternatives.reduce((min, alt) => 
      alt.cost < min.cost ? alt : min, 
      { cost: Infinity, model: primaryModel }
    );
    
    if (cheapestOption.model !== primaryModel) {
      recommendations.push(`For maximum cost savings, consider ${cheapestOption.model} pricing`);
    }
  }

  return recommendations;
}

function calculateEfficiencyRating(
  primaryModel: string,
  alternatives: AlternativeCost[],
  projectContext: ProjectContext
): 'excellent' | 'good' | 'fair' | 'poor' {
  
  // Check if we're using the most cost-effective model
  const hasSignificantSavings = alternatives.some(alt => alt.savings_percentage > 20);
  const hasModerateOptions = alternatives.some(alt => Math.abs(alt.savings_percentage) > 10);
  
  if (!hasSignificantSavings && !hasModerateOptions) {
    return 'excellent'; // Optimal choice
  } else if (!hasSignificantSavings) {
    return 'good'; // Reasonable choice with minor alternatives
  } else if (hasSignificantSavings) {
    return 'fair'; // Could be optimized
  } else {
    return 'poor'; // Suboptimal choice
  }
}

export function calculateProjectLaborCosts(
  laborRequirements: Array<{
    labor_rate: EnhancedLaborRate;
    quantity: number;
  }>,
  projectContext: ProjectContext
): {
  total_cost: number;
  detailed_breakdown: Array<{
    labor_name: string;
    result: LaborCostResult;
  }>;
  optimization_summary: {
    potential_savings: number;
    recommended_changes: string[];
    efficiency_score: number;
  };
} {
  
  const detailedBreakdown = laborRequirements.map(req => ({
    labor_name: req.labor_rate.name,
    result: calculateLaborCost(req.labor_rate, req.quantity, projectContext)
  }));

  const totalCost = detailedBreakdown.reduce((sum, item) => sum + item.result.primary_cost, 0);
  
  // Calculate optimization opportunities
  const potentialSavings = detailedBreakdown.reduce((savings, item) => {
    const bestAlternative = item.result.alternatives.find(alt => alt.savings > 0);
    return savings + (bestAlternative?.savings || 0);
  }, 0);

  const recommendedChanges: string[] = [];
  detailedBreakdown.forEach(item => {
    const bestAlternative = item.result.alternatives.find(alt => alt.savings > 0 && alt.savings_percentage > 15);
    if (bestAlternative) {
      recommendedChanges.push(
        `${item.labor_name}: Switch to ${bestAlternative.model} (save ₹${bestAlternative.savings.toLocaleString()})`
      );
    }
  });

  // Calculate efficiency score (0-100)
  const efficiencyScores = detailedBreakdown.map(item => {
    switch (item.result.efficiency_rating) {
      case 'excellent': return 100;
      case 'good': return 80;
      case 'fair': return 60;
      case 'poor': return 40;
      default: return 70;
    }
  });
  
  const efficiencyScore = efficiencyScores.reduce((sum, score) => sum + score, 0) / efficiencyScores.length;

  return {
    total_cost: totalCost,
    detailed_breakdown: detailedBreakdown,
    optimization_summary: {
      potential_savings: potentialSavings,
      recommended_changes: recommendedChanges,
      efficiency_score: Math.round(efficiencyScore)
    }
  };
}
