/**
 * DATABASE CONNECTION TEST COMPONENT
 * 
 * This component tests the new configurable parameters database implementation
 * and displays the results to verify everything is working correctly.
 */

import React, { useState, useEffect } from 'react';
import { Database, CheckCircle, XCircle, Loader, Settings } from 'lucide-react';
import { materialRatesAPI, calculationFactorsAPI, openingSizesAPI, engineeringStandardsAPI } from '../../lib/supabase';

interface TestResult {
  name: string;
  status: 'loading' | 'success' | 'error';
  data?: any;
  error?: string;
  count?: number;
}

export function DatabaseTest() {
  const [testResults, setTestResults] = useState<TestResult[]>([
    { name: 'Material Rates', status: 'loading' },
    { name: 'Calculation Factors', status: 'loading' },
    { name: 'Opening Sizes', status: 'loading' },
    { name: 'Structural Parameters', status: 'loading' },
    { name: 'Waterproofing Rates', status: 'loading' },
    { name: 'Door Dimensions', status: 'loading' },
    { name: 'Window Dimensions', status: 'loading' },
    { name: 'Plaster Coverage Factor', status: 'loading' }
  ]);

  useEffect(() => {
    runDatabaseTests();
  }, []);

  const updateTestResult = (name: string, updates: Partial<TestResult>) => {
    setTestResults(prev => prev.map(test => 
      test.name === name ? { ...test, ...updates } : test
    ));
  };

  const runDatabaseTests = async () => {
    console.log('🔍 Starting Database Tests...');

    // Test 1: Material Rates
    try {
      const materialRates = await materialRatesAPI.getAll('delhi');
      updateTestResult('Material Rates', {
        status: 'success',
        data: materialRates.slice(0, 3),
        count: materialRates.length
      });
      console.log('✅ Material Rates:', materialRates.length, 'records');
    } catch (error) {
      updateTestResult('Material Rates', {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('❌ Material Rates Error:', error);
    }

    // Test 2: Calculation Factors
    try {
      const factors = await calculationFactorsAPI.getAll();
      updateTestResult('Calculation Factors', {
        status: 'success',
        data: factors.slice(0, 3),
        count: factors.length
      });
      console.log('✅ Calculation Factors:', factors.length, 'records');
    } catch (error) {
      updateTestResult('Calculation Factors', {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('❌ Calculation Factors Error:', error);
    }

    // Test 3: Opening Sizes
    try {
      const openings = await openingSizesAPI.getAll();
      updateTestResult('Opening Sizes', {
        status: 'success',
        data: openings.slice(0, 3),
        count: openings.length
      });
      console.log('✅ Opening Sizes:', openings.length, 'records');
    } catch (error) {
      updateTestResult('Opening Sizes', {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('❌ Opening Sizes Error:', error);
    }

    // Test 4: Structural Parameters
    try {
      const structural = await engineeringStandardsAPI.getStructuralAssumptions();
      const newParams = structural.filter(s => 
        ['Floor Height', 'Beam Width', 'Beam Depth', 'Column Size Low Rise', 'Column Size Mid Rise', 'Column Size High Rise'].includes(s.name)
      );
      updateTestResult('Structural Parameters', {
        status: 'success',
        data: newParams.slice(0, 3),
        count: newParams.length
      });
      console.log('✅ Structural Parameters:', newParams.length, 'new parameters');
    } catch (error) {
      updateTestResult('Structural Parameters', {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('❌ Structural Parameters Error:', error);
    }

    // Test 5: Waterproofing Rates
    try {
      const waterproofingRates = await materialRatesAPI.getWaterproofingRates('delhi');
      updateTestResult('Waterproofing Rates', {
        status: 'success',
        data: waterproofingRates,
        count: Object.keys(waterproofingRates).length
      });
      console.log('✅ Waterproofing Rates:', waterproofingRates);
    } catch (error) {
      updateTestResult('Waterproofing Rates', {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('❌ Waterproofing Rates Error:', error);
    }

    // Test 6: Door Dimensions
    try {
      const doorDimensions = await openingSizesAPI.getDoorDimensions();
      updateTestResult('Door Dimensions', {
        status: 'success',
        data: doorDimensions,
        count: Object.keys(doorDimensions).length
      });
      console.log('✅ Door Dimensions:', doorDimensions);
    } catch (error) {
      updateTestResult('Door Dimensions', {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('❌ Door Dimensions Error:', error);
    }

    // Test 7: Window Dimensions
    try {
      const windowDimensions = await openingSizesAPI.getWindowDimensions();
      updateTestResult('Window Dimensions', {
        status: 'success',
        data: windowDimensions,
        count: Object.keys(windowDimensions).length
      });
      console.log('✅ Window Dimensions:', windowDimensions);
    } catch (error) {
      updateTestResult('Window Dimensions', {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('❌ Window Dimensions Error:', error);
    }

    // Test 8: Specific Calculation Factor
    try {
      const plasterFactor = await calculationFactorsAPI.getFactorValue('plaster_coverage_factor');
      updateTestResult('Plaster Coverage Factor', {
        status: 'success',
        data: { plaster_coverage_factor: plasterFactor },
        count: 1
      });
      console.log('✅ Plaster Coverage Factor:', plasterFactor);
    } catch (error) {
      updateTestResult('Plaster Coverage Factor', {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      console.error('❌ Plaster Coverage Factor Error:', error);
    }

    console.log('🎉 Database Tests Complete!');
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'loading':
        return <Loader className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
    }
  };

  const successCount = testResults.filter(t => t.status === 'success').length;
  const errorCount = testResults.filter(t => t.status === 'error').length;
  const loadingCount = testResults.filter(t => t.status === 'loading').length;

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 max-w-4xl mx-auto">
      <div className="flex items-center gap-3 mb-6">
        <Database className="w-6 h-6 text-blue-600" />
        <h2 className="text-2xl font-bold text-gray-800">Database Connection Test</h2>
        <Settings className="w-5 h-5 text-gray-500" />
      </div>

      {/* Test Summary */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-600">{successCount}</div>
          <div className="text-sm text-green-700">Successful</div>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-red-600">{errorCount}</div>
          <div className="text-sm text-red-700">Failed</div>
        </div>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{loadingCount}</div>
          <div className="text-sm text-blue-700">Loading</div>
        </div>
      </div>

      {/* Test Results */}
      <div className="space-y-4">
        {testResults.map((test, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-3">
                {getStatusIcon(test.status)}
                <h3 className="font-semibold text-gray-800">{test.name}</h3>
                {test.count !== undefined && (
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                    {test.count} records
                  </span>
                )}
              </div>
            </div>

            {test.status === 'success' && test.data && (
              <div className="mt-2 p-3 bg-green-50 rounded border border-green-200">
                <pre className="text-sm text-green-800 overflow-x-auto">
                  {JSON.stringify(test.data, null, 2)}
                </pre>
              </div>
            )}

            {test.status === 'error' && test.error && (
              <div className="mt-2 p-3 bg-red-50 rounded border border-red-200">
                <p className="text-sm text-red-800">{test.error}</p>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Retry Button */}
      <div className="mt-6 text-center">
        <button
          onClick={runDatabaseTests}
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Retry Tests
        </button>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-semibold text-blue-800 mb-2">💡 What This Tests:</h4>
        <ul className="text-blue-700 text-sm space-y-1">
          <li>• Material rates for excavation, plaster, electrical, plumbing, waterproofing</li>
          <li>• Calculation factors like plaster coverage, door/window density</li>
          <li>• Opening sizes (doors, windows) per IS 1200:2012 standards</li>
          <li>• New structural parameters (floor height, beam dimensions, column sizes)</li>
          <li>• API functions for fetching configurable parameters</li>
        </ul>
      </div>
    </div>
  );
}
