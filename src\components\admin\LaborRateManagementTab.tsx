import React, { useState, useEffect } from 'react';
import { Save, Building2, Plus, Trash2, Edit, Info, Clock } from 'lucide-react';
import { supabase } from '../../lib/supabase';

interface LaborRate {
  id?: string;
  category: string;
  name: string;
  skill_level: 'unskilled' | 'semiskilled' | 'skilled' | 'specialist';
  daily_rate: number;
  productivity_value: number;
  productivity_unit: string;
  location: string;
}

export function LaborRateManagementTab() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [laborRates, setLaborRates] = useState<LaborRate[]>([
    { category: 'Supervisor', name: 'Site Supervisor', skill_level: 'specialist', daily_rate: 1500, productivity_value: 0, productivity_unit: 'N/A', location: 'Delhi NCR' },
    { category: 'Mason', name: 'Brick Mason', skill_level: 'skilled', daily_rate: 1200, productivity_value: 1.25, productivity_unit: 'm³ Brickwork', location: 'Delhi NCR' },
    { category: 'Steel Fixer', name: 'Steel Bar Bender & Fixer', skill_level: 'skilled', daily_rate: 1300, productivity_value: 150, productivity_unit: 'kg', location: 'Delhi NCR' },
    { category: 'Labor', name: 'General Labor', skill_level: 'unskilled', daily_rate: 800, productivity_value: 0.75, productivity_unit: 'm³ Excavation', location: 'Delhi NCR' }
  ]);
  const [editingRate, setEditingRate] = useState<LaborRate | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);

  // Load data from database
  useEffect(() => {
    loadLaborRates();
  }, []);

  const loadLaborRates = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, this would fetch from the database
      // For now, we'll use the default values
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // In a real implementation, we would update state with fetched data
      // setLaborRates(data);
    } catch (error) {
      console.error('Error loading labor rates:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveLaborRates = async () => {
    setIsSaving(true);
    try {
      // In a real implementation, this would save to the database
      console.log('Saving labor rates:', laborRates);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Labor rates saved successfully!');
    } catch (error) {
      console.error('Error saving labor rates:', error);
      alert('Error saving labor rates');
    } finally {
      setIsSaving(false);
    }
  };

  const addLaborRate = () => {
    setEditingRate({
      category: '',
      name: '',
      skill_level: 'skilled',
      daily_rate: 0,
      productivity_value: 0,
      productivity_unit: '',
      location: 'Delhi NCR'
    });
    setShowEditModal(true);
  };

  const editLaborRate = (rate: LaborRate) => {
    setEditingRate({ ...rate });
    setShowEditModal(true);
  };

  const removeLaborRate = (index: number) => {
    setLaborRates(laborRates.filter((_, i) => i !== index));
  };

  const handleSaveRate = () => {
    if (!editingRate) return;
    
    if (editingRate.id) {
      // Update existing rate
      setLaborRates(laborRates.map(rate => 
        rate.id === editingRate.id ? editingRate : rate
      ));
    } else {
      // Add new rate
      setLaborRates([...laborRates, editingRate]);
    }
    
    setShowEditModal(false);
    setEditingRate(null);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">Labor Rate Management</h3>
        <div className="flex items-center gap-2 text-sm text-purple-600 bg-purple-50 px-3 py-1 rounded-lg">
          <Building2 className="w-4 h-4" />
          <span>2025 Market Rates</span>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-2">
          <Info className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="font-semibold text-blue-800">Labor Rate Information</h4>
            <p className="text-blue-700 text-sm mt-1">
              Labor rates are a major cost driver and vary by skill level and location.
              Productivity values determine how much work can be completed per day, affecting timeline and labor costs.
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <h4 className="font-semibold text-gray-800">Labor Rates (2025 Market)</h4>
        <button
          onClick={addLaborRate}
          className="flex items-center gap-1 px-3 py-1 bg-purple-100 hover:bg-purple-200 text-purple-700 rounded-lg text-sm transition-colors"
        >
          <Plus className="w-4 h-4" />
          Add Labor Rate
        </button>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Skill Level</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Daily Rate (₹)</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Productivity</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {laborRates.map((rate, index) => (
              <tr key={index}>
                <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                  {rate.category}
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                  {rate.name}
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    rate.skill_level === 'specialist' ? 'bg-purple-100 text-purple-800' :
                    rate.skill_level === 'skilled' ? 'bg-blue-100 text-blue-800' :
                    rate.skill_level === 'semiskilled' ? 'bg-green-100 text-green-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {rate.skill_level}
                  </span>
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                  ₹{rate.daily_rate.toLocaleString()}
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                  {rate.productivity_value > 0 ? (
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3 text-gray-400" />
                      <span>{rate.productivity_value} {rate.productivity_unit}/day</span>
                    </div>
                  ) : (
                    <span className="text-gray-400">N/A</span>
                  )}
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                  {rate.location}
                </td>
                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                  <div className="flex gap-2">
                    <button
                      onClick={() => editLaborRate(rate)}
                      className="text-blue-500 hover:text-blue-700 transition-colors"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => removeLaborRate(index)}
                      className="text-red-500 hover:text-red-700 transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="flex justify-end">
        <button
          onClick={handleSaveLaborRates}
          disabled={isSaving}
          className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white rounded-lg font-medium transition-colors"
        >
          {isSaving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4" />
              Save Labor Rates
            </>
          )}
        </button>
      </div>

      {/* Edit Modal */}
      {showEditModal && editingRate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              {editingRate.id ? 'Edit Labor Rate' : 'Add Labor Rate'}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <input
                  type="text"
                  value={editingRate.category}
                  onChange={(e) => setEditingRate({...editingRate, category: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="e.g., Mason, Carpenter, Electrician"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input
                  type="text"
                  value={editingRate.name}
                  onChange={(e) => setEditingRate({...editingRate, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="e.g., Brick Mason, Senior Carpenter"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Skill Level</label>
                <select
                  value={editingRate.skill_level}
                  onChange={(e) => setEditingRate({...editingRate, skill_level: e.target.value as any})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="unskilled">Unskilled</option>
                  <option value="semiskilled">Semi-skilled</option>
                  <option value="skilled">Skilled</option>
                  <option value="specialist">Specialist</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Daily Rate (₹)</label>
                <input
                  type="number"
                  value={editingRate.daily_rate}
                  onChange={(e) => setEditingRate({...editingRate, daily_rate: Number(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  min="0"
                  step="50"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Productivity Value</label>
                  <input
                    type="number"
                    value={editingRate.productivity_value}
                    onChange={(e) => setEditingRate({...editingRate, productivity_value: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    min="0"
                    step="0.1"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Productivity Unit</label>
                  <input
                    type="text"
                    value={editingRate.productivity_unit}
                    onChange={(e) => setEditingRate({...editingRate, productivity_unit: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="e.g., sqm, kg, m³"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                <input
                  type="text"
                  value={editingRate.location}
                  onChange={(e) => setEditingRate({...editingRate, location: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="e.g., Delhi NCR, Gurgaon, Noida"
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveRate}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}