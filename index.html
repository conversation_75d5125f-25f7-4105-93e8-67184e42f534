<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
    <meta name="description" content="NirmaanAI Construction Cost Calculator - Get detailed, transparent estimates for building your dream home in Delhi/NCR with real-time pricing and complete cost breakdown." />
    <meta name="keywords" content="construction cost calculator, building cost estimate, Delhi NCR construction, home building cost, construction planning" />
    <meta name="author" content="NirmaanAI" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="NirmaanAI Construction Cost Calculator" />
    <meta property="og:description" content="Calculate your residential construction cost with detailed, transparent estimates for Delhi/NCR region." />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="NirmaanAI Construction Cost Calculator" />
    <meta property="twitter:description" content="Calculate your residential construction cost with detailed, transparent estimates for Delhi/NCR region." />
    
    <!-- Accessibility -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="color-scheme" content="light dark" />
    
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <title>NirmaanAI Construction Cost Calculator - Transparent Building Cost Estimates</title>
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <style>
      /* Critical CSS for above-the-fold content */
      body {
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        margin: 0;
        padding: 0;
      }
      
      /* Loading state */
      .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        font-size: 18px;
        color: #374151;
      }
      
      /* Skip link styles */
      .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: #3b82f6;
        color: white;
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1000;
      }
      
      .skip-link:focus {
        top: 6px;
      }
    </style>
  </head>
  <body>
    <noscript>
      <div style="padding: 20px; text-align: center; font-family: system-ui, sans-serif;">
        <h1>JavaScript Required</h1>
        <p>This application requires JavaScript to function properly. Please enable JavaScript in your browser settings and reload the page.</p>
        <p>For assistance, please contact our support team.</p>
      </div>
    </noscript>
    
    <div id="root">
      <div class="loading" role="status" aria-label="Loading application">
        Loading NirmaanAI Calculator...
      </div>
    </div>
    
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>