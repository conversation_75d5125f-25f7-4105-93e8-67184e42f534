import React, { useState, useEffect } from 'react';
import { Save, Settings, Info, AlertTriangle } from 'lucide-react';
import { supabase } from '../../lib/supabase';

interface SystemConfig {
  id?: string;
  gst_materials: number;
  gst_services: number;
  default_contingency: number;
  default_profit_margin: number;
}

export function SystemConfigurationTab() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [config, setConfig] = useState<SystemConfig>({
    gst_materials: 18.0,
    gst_services: 18.0,
    default_contingency: 10.0,
    default_profit_margin: 15.0
  });

  // Load data from database
  useEffect(() => {
    loadSystemConfig();
  }, []);

  const loadSystemConfig = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, this would fetch from the database
      // For now, we'll use the default values
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // In a real implementation, we would update state with fetched data
      // setConfig(data);
    } catch (error) {
      console.error('Error loading system configuration:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveConfig = async () => {
    setIsSaving(true);
    try {
      // In a real implementation, this would save to the database
      console.log('Saving system configuration:', config);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('System configuration saved successfully!');
    } catch (error) {
      console.error('Error saving system configuration:', error);
      alert('Error saving system configuration');
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (field: keyof SystemConfig, value: number) => {
    setConfig({ ...config, [field]: value });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">System Configuration</h3>
        <div className="flex items-center gap-2 text-sm text-indigo-600 bg-indigo-50 px-3 py-1 rounded-lg">
          <Settings className="w-4 h-4" />
          <span>Global Settings</span>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-2">
          <Info className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="font-semibold text-blue-800">System-wide Parameters</h4>
            <p className="text-blue-700 text-sm mt-1">
              These global settings affect all cost estimates generated by the system.
              They include tax rates, contingency, and profit margin defaults.
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* GST on Materials */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              GST on Materials (%)
            </label>
            <div className="flex items-center">
              <input
                type="number"
                value={config.gst_materials}
                onChange={(e) => handleInputChange('gst_materials', Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                step="0.1"
                min="0"
                max="30"
              />
              <span className="ml-2 text-gray-500">%</span>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Applied to all material costs as per current GST regulations
            </p>
          </div>

          {/* GST on Labor/Services */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              GST on Labor/Services (%)
            </label>
            <div className="flex items-center">
              <input
                type="number"
                value={config.gst_services}
                onChange={(e) => handleInputChange('gst_services', Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                step="0.1"
                min="0"
                max="30"
              />
              <span className="ml-2 text-gray-500">%</span>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Applied to all labor and professional fees
            </p>
          </div>

          {/* Default Contingency */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Default Contingency (%)
            </label>
            <div className="flex items-center">
              <input
                type="number"
                value={config.default_contingency}
                onChange={(e) => handleInputChange('default_contingency', Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                step="0.5"
                min="0"
                max="20"
              />
              <span className="ml-2 text-gray-500">%</span>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Buffer for unforeseen costs during construction
            </p>
          </div>

          {/* Default Profit Margin */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Default Profit Margin (%)
            </label>
            <div className="flex items-center">
              <input
                type="number"
                value={config.default_profit_margin}
                onChange={(e) => handleInputChange('default_profit_margin', Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                step="0.5"
                min="0"
                max="30"
              />
              <span className="ml-2 text-gray-500">%</span>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Standard contractor's profit margin
            </p>
          </div>
        </div>

        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-2">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="font-semibold text-yellow-800">Tax Compliance Note</h4>
              <p className="text-yellow-700 text-sm mt-1">
                GST rates should be updated whenever there are changes in tax regulations.
                The current rates are based on GST Council notifications as of June 2025.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <button
          onClick={handleSaveConfig}
          disabled={isSaving}
          className="flex items-center gap-2 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-400 text-white rounded-lg font-medium transition-colors"
        >
          {isSaving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4" />
              Save System Configuration
            </>
          )}
        </button>
      </div>
    </div>
  );
}