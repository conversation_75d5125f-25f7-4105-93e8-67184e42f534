export interface ItemBreakdown {
  id: string;
  title: string;
  breakdown: string[];
}

export const itemBreakdowns: Record<string, ItemBreakdown> = {
  electrical: {
    id: 'electrical',
    title: 'What\'s included in each Electrical Point:',
    breakdown: [
      'Up to 10 meters of FR-LSH copper wire from distribution board',
      'PVC conduit for concealed wiring installation',
      'Modular switch plate and mechanism (brand as selected)',
      'Installation labor including wall chasing and plastering',
      'Basic electrical accessories (wire nuts, tape, etc.)',
      'Note: Light fixtures, fans, and appliances are separate'
    ]
  },
  
  plumbing: {
    id: 'plumbing',
    title: 'What\'s included in each Plumbing Point:',
    breakdown: [
      'CPVC pipes for hot/cold water supply (up to 8 meters)',
      'UPVC pipes for waste water drainage',
      'Basic fittings (elbows, tees, reducers)',
      'Installation labor including wall chasing',
      'Pressure testing and leak checking',
      'Note: Fixtures like taps, showers, and sanitary ware are separate'
    ]
  },
  
  bathroom_fittings: {
    id: 'bathroom_fittings',
    title: 'Complete Bathroom Set includes:',
    breakdown: [
      'Wall-mounted or floor-mounted water closet with seat cover',
      'Wash basin with pedestal or wall mounting',
      'Single-lever basin mixer tap',
      'Health faucet with wall bracket',
      'Overhead shower with arm and wall mixer',
      'Basic accessories: soap dish, towel rod, paper holder'
    ]
  },
  
  main_door: {
    id: 'main_door',
    title: 'Main Door Package includes:',
    breakdown: [
      'Solid wood door frame (teak/hardwood as selected)',
      'Designer panel door shutter with veneer finish',
      'Premium hardware: hinges, handle set, lock mechanism',
      'Door closer and safety chain',
      'Installation with frame fixing and alignment',
      'Basic wood polish/paint finish'
    ]
  },
  
  internal_doors: {
    id: 'internal_doors',
    title: 'Internal Door Package includes:',
    breakdown: [
      'Hardwood door frame with architrave',
      'Flush door shutter or panel door (as selected)',
      'Standard hardware: hinges, handle, lock',
      'Installation with proper alignment',
      'Basic primer and paint finish',
      'Door stopper and floor door guard'
    ]
  },
  
  windows: {
    id: 'windows',
    title: 'Window Package includes:',
    breakdown: [
      'Frame material (aluminum/UPVC as selected)',
      'Clear float glass (5mm thickness)',
      'Sliding or casement mechanism with hardware',
      'Mosquito mesh (fiber glass)',
      'Weather sealing and water drainage',
      'Installation with wall anchoring and sealing'
    ]
  }
};