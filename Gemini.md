
This project, NirmaanAI v2.1, is a construction cost calculator for the Delhi/NCR region of India. It uses a "Pluggable Component Architecture" with a Supabase backend.

The core of the application is the V2 calculation engine, located in `src/utils/v2CalculationEngine.ts`. This engine calculates construction costs based on a variety of factors, including:

*   **Components:** Materials and products, stored in the `components` table in Supabase.
*   **Tasks:** Installation recipes with associated materials and labor, stored in the `tasks` table.
*   **Labor Rates:** Labor costs for different skill levels and locations, stored in the `labor_rates` table.
*   **Engineering Standards:** Technical parameters and assumptions, stored in the `engineering_standards` table.
*   **Regional Data:** Location-specific cost multipliers and fees.

The application features a comprehensive admin panel for managing all of this data, as well as a user-friendly interface for creating and customizing construction estimates.

Key files and directories:

*   `src/utils/v2CalculationEngine.ts`: The core calculation engine.
*   `src/lib/supabase.ts`: Supabase client and API functions.
*   `src/components/admin/AdminDashboard.tsx`: The main admin panel component.
*   `src/components/room/VisualRoomConfigurator.tsx`: The room customization component.
*   `src/components/facade/FacadeDesignModule.tsx`: The facade design component.
*   `supabase/migrations`: Database migration files.
