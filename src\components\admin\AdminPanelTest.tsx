import React, { useState, useEffect } from 'react';
import { supabase, componentAPI, Component } from '../../lib/supabase';

export function AdminPanelTest() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [authUser, setAuthUser] = useState<any>(null);
  const [testComponent, setTestComponent] = useState<Component | null>(null);

  useEffect(() => {
    checkAuth();
  }, []);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const checkAuth = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      setAuthUser(user);
      addResult(`Auth Status: ${user ? `Logged in as ${user.email}` : 'Not authenticated'}`);
    } catch (error) {
      addResult(`Auth Error: ${error}`);
    }
  };

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      // Test 1: Check authentication
      addResult('=== AUTHENTICATION TEST ===');
      await checkAuth();

      // Test 2: Check admin user status
      addResult('=== ADMIN USER CHECK ===');
      try {
        const { data: adminUser, error } = await supabase
          .from('admin_users')
          .select('*')
          .eq('email', authUser?.email || '<EMAIL>')
          .single();
        
        if (error) {
          addResult(`Admin User Error: ${error.message}`);
        } else {
          addResult(`Admin User Found: ${adminUser.full_name} (${adminUser.role})`);
        }
      } catch (error) {
        addResult(`Admin User Query Failed: ${error}`);
      }

      // Test 3: Try to read components
      addResult('=== COMPONENT READ TEST ===');
      try {
        const components = await componentAPI.getAll();
        addResult(`✅ Successfully read ${components.length} components`);
        
        if (components.length > 0) {
          setTestComponent(components[0]);
          addResult(`Test component: ${components[0].name} (₹${components[0].unit_price})`);
        }
      } catch (error) {
        addResult(`❌ Component read failed: ${error}`);
      }

      // Test 4: Try to update a component price
      if (testComponent) {
        addResult('=== COMPONENT UPDATE TEST ===');
        try {
          const originalPrice = testComponent.unit_price;
          const newPrice = originalPrice + 1; // Small increment
          
          addResult(`Attempting to update ${testComponent.name} price from ₹${originalPrice} to ₹${newPrice}`);
          
          const updatedComponent = await componentAPI.update(testComponent.id, {
            unit_price: newPrice
          });
          
          addResult(`✅ Successfully updated component price to ₹${updatedComponent.unit_price}`);
          
          // Revert the change
          await componentAPI.update(testComponent.id, {
            unit_price: originalPrice
          });
          addResult(`✅ Reverted price back to ₹${originalPrice}`);
          
        } catch (error) {
          addResult(`❌ Component update failed: ${error}`);
        }
      }

      // Test 5: Check RLS policies
      addResult('=== RLS POLICY TEST ===');
      try {
        const { data, error } = await supabase.rpc('check_admin_access');
        if (error) {
          addResult(`RLS Check Error: ${error.message}`);
        } else {
          addResult(`RLS Check Result: ${data}`);
        }
      } catch (error) {
        addResult(`RLS Check Failed: ${error}`);
      }

      // Test 6: Direct database query test
      addResult('=== DIRECT DATABASE TEST ===');
      try {
        const { data, error } = await supabase
          .from('components')
          .select('id, name, unit_price')
          .limit(1);
        
        if (error) {
          addResult(`Direct query error: ${error.message}`);
        } else {
          addResult(`✅ Direct query successful: ${data?.length} records`);
        }
      } catch (error) {
        addResult(`Direct query failed: ${error}`);
      }

    } catch (error) {
      addResult(`Test suite error: ${error}`);
    } finally {
      setIsRunning(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="p-6 bg-white rounded-lg border">
      <h2 className="text-xl font-bold mb-4">Admin Panel Diagnostic Test</h2>
      
      <div className="mb-4 space-x-2">
        <button
          onClick={runTests}
          disabled={isRunning}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {isRunning ? 'Running Tests...' : 'Run Diagnostic Tests'}
        </button>
        
        <button
          onClick={clearResults}
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
        >
          Clear Results
        </button>
      </div>

      <div className="bg-gray-100 p-4 rounded max-h-96 overflow-y-auto">
        <h3 className="font-semibold mb-2">Test Results:</h3>
        {testResults.length === 0 ? (
          <p className="text-gray-500">No tests run yet. Click "Run Diagnostic Tests" to start.</p>
        ) : (
          <div className="space-y-1">
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`text-sm font-mono ${
                  result.includes('✅') ? 'text-green-600' :
                  result.includes('❌') ? 'text-red-600' :
                  result.includes('===') ? 'text-blue-600 font-bold' :
                  'text-gray-700'
                }`}
              >
                {result}
              </div>
            ))}
          </div>
        )}
      </div>

      {authUser && (
        <div className="mt-4 p-3 bg-blue-50 rounded">
          <h4 className="font-semibold text-blue-800">Current User Info:</h4>
          <p className="text-sm text-blue-700">Email: {authUser.email}</p>
          <p className="text-sm text-blue-700">ID: {authUser.id}</p>
        </div>
      )}
    </div>
  );
}
