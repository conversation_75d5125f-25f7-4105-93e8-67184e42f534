import React, { useEffect, useState } from 'react';

interface CostHighlightProps {
  children: React.ReactNode;
  shouldHighlight: boolean;
  isIncrease: boolean;
  className?: string;
}

export function CostHighlight({ children, shouldHighlight, isIncrease, className = '' }: CostHighlightProps) {
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (shouldHighlight) {
      setIsAnimating(true);
      const timer = setTimeout(() => {
        setIsAnimating(false);
      }, 2500);
      return () => clearTimeout(timer);
    }
  }, [shouldHighlight]);

  const highlightClass = isAnimating 
    ? isIncrease 
      ? 'bg-red-100 border-red-200' 
      : 'bg-green-100 border-green-200'
    : '';

  return (
    <div className={`transition-all duration-500 ease-out ${highlightClass} ${className}`}>
      {children}
    </div>
  );
}