export interface GurgaonRegulation {
  id: string;
  name: string;
  applicableArea: string;
  costPerSqft: number;
  timelineWeeks: number;
  authority: string;
  mandatory: boolean;
}

export const gurgaonRegulations: GurgaonRegulation[] = [
  {
    id: 'dtcp_approval',
    name: 'DTCP Building Plan Approval',
    applicableArea: 'All plots',
    costPerSqft: 12,
    timelineWeeks: 8,
    authority: 'DTCP Haryana',
    mandatory: true
  },
  {
    id: 'fire_noc',
    name: 'Fire NOC',
    applicableArea: 'G+2 and above',
    costPerSqft: 8,
    timelineWeeks: 6,
    authority: 'Haryana Fire Services',
    mandatory: true
  },
  {
    id: 'rainwater_harvesting',
    name: 'Rainwater Harvesting System',
    applicableArea: 'Plot > 300 sqm',
    costPerSqft: 25,
    timelineWeeks: 2,
    authority: 'CGWA',
    mandatory: true
  },
  {
    id: 'sewage_treatment',
    name: 'Sewage Treatment Plant',
    applicableArea: 'Plot > 500 sqm',
    costPerSqft: 45,
    timelineWeeks: 4,
    authority: 'HSPCB',
    mandatory: true
  }
];

export interface MarketRateData {
  materialId: string;
  currentRate: number;
  lastUpdated: string;
  trend: 'increasing' | 'decreasing' | 'stable';
  seasonalFactor: number;
  transportationCost: number;
  vendorTiers: {
    local: number;
    branded: number;
    premium: number;
  };
}

export const gurgaonMarketRates: MarketRateData[] = [
  {
    materialId: 'cement',
    currentRate: 385,
    lastUpdated: '2024-01-15',
    trend: 'increasing',
    seasonalFactor: 1.1, // 10% higher in monsoon
    transportationCost: 15,
    vendorTiers: {
      local: 365,
      branded: 385,
      premium: 420
    }
  },
  {
    materialId: 'steel',
    currentRate: 52,
    lastUpdated: '2024-01-15',
    trend: 'stable',
    seasonalFactor: 1.0,
    transportationCost: 3,
    vendorTiers: {
      local: 48,
      branded: 52,
      premium: 58
    }
  }
];

export interface LaborEscalation {
  category: string;
  baseRate: number;
  monthlyEscalation: number; // percentage
  skillLevel: 'unskilled' | 'semiskilled' | 'skilled' | 'supervisor';
  availability: 'abundant' | 'moderate' | 'scarce';
}

export const gurgaonLaborRates: LaborEscalation[] = [
  {
    category: 'Mason',
    baseRate: 650,
    monthlyEscalation: 2.5,
    skillLevel: 'skilled',
    availability: 'moderate'
  },
  {
    category: 'Helper',
    baseRate: 350,
    monthlyEscalation: 3.0,
    skillLevel: 'unskilled',
    availability: 'abundant'
  },
  {
    category: 'Electrician',
    baseRate: 750,
    monthlyEscalation: 2.0,
    skillLevel: 'skilled',
    availability: 'scarce'
  },
  {
    category: 'Plumber',
    baseRate: 700,
    monthlyEscalation: 2.2,
    skillLevel: 'skilled',
    availability: 'moderate'
  }
];