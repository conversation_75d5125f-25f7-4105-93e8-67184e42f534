import React, { useState, useEffect } from 'react';
import { Save, Plus, Edit, Trash2, Calculator, TrendingUp, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { LaborRate } from '../../lib/supabase';

interface MultiModalLaborRateEditorProps {
  laborRate: LaborRate | null;
  onSave: (laborRateData: Partial<EnhancedLaborRate>) => Promise<void>;
  onCancel: () => void;
}

interface EnhancedLaborRate extends LaborRate {
  charging_models?: {
    primary_model: 'per_unit' | 'lump_sum' | 'per_day' | 'per_sqft';
    lump_sum_rates?: {
      small_project: { good: number; better: number; best: number };
      medium_project: { good: number; better: number; best: number };
      large_project: { good: number; better: number; best: number };
    };
    daily_rates?: { good: number; better: number; best: number };
    per_sqft_rates?: { good: number; better: number; best: number };
    recommended_for?: {
      project_sizes: string[];
      project_types: string[];
      notes: string;
    };
  };
}

export function MultiModalLaborRateEditor({ laborRate, onSave, onCancel }: MultiModalLaborRateEditorProps) {
  const [activeTab, setActiveTab] = useState<'per_unit' | 'lump_sum' | 'daily' | 'per_sqft' | 'recommendations'>('per_unit');
  const [formData, setFormData] = useState<EnhancedLaborRate>({
    id: '',
    name: '',
    category: '',
    skill_level: 'skilled',
    unit: 'sqm',
    rates: { good: 0, better: 0, best: 0 },
    productivity: { output_per_day: 1, unit: 'sqm' },
    location: 'gurgaon',
    is_active: true,
    created_at: '',
    updated_at: '',
    charging_models: {
      primary_model: 'per_unit',
      recommended_for: {
        project_sizes: ['medium'],
        project_types: ['residential'],
        notes: ''
      }
    }
  });

  const [validation, setValidation] = useState<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }>({ isValid: true, errors: [], warnings: [] });

  useEffect(() => {
    if (laborRate) {
      setFormData({
        ...laborRate,
        charging_models: {
          primary_model: 'per_unit',
          recommended_for: {
            project_sizes: ['medium'],
            project_types: ['residential'],
            notes: ''
          },
          ...(laborRate as any).charging_models
        }
      });
    }
  }, [laborRate]);

  useEffect(() => {
    validateFormData();
  }, [formData]);

  const validateFormData = () => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation
    if (!formData.name.trim()) errors.push('Labor name is required');
    if (!formData.category.trim()) errors.push('Category is required');
    if (formData.rates.good <= 0) errors.push('Good rate must be greater than 0');
    if (formData.rates.better <= formData.rates.good) errors.push('Better rate must be higher than Good rate');
    if (formData.rates.best <= formData.rates.better) errors.push('Best rate must be higher than Better rate');
    if (formData.productivity.output_per_day <= 0) errors.push('Productivity must be greater than 0');

    // Market rate validation (Delhi/NCR 2024)
    const avgRate = (formData.rates.good + formData.rates.better + formData.rates.best) / 3;
    const marketRanges: Record<string, { min: number; max: number }> = {
      'foundation': { min: 400, max: 1800 },
      'masonry': { min: 80, max: 400 },
      'finishes': { min: 20, max: 180 },
      'flooring': { min: 100, max: 260 },
      'electrical': { min: 70, max: 210 },
      'plumbing': { min: 200, max: 520 },
      'specialized': { min: 120, max: 300 }
    };

    const range = marketRanges[formData.category];
    if (range && (avgRate < range.min || avgRate > range.max)) {
      warnings.push(`Rate outside typical market range (₹${range.min}-${range.max})`);
    }

    // Lump sum validation
    if (formData.charging_models?.lump_sum_rates) {
      const lumpSum = formData.charging_models.lump_sum_rates;
      if (lumpSum.small_project && lumpSum.medium_project && lumpSum.large_project) {
        if (lumpSum.small_project.good >= lumpSum.medium_project.good) {
          warnings.push('Small project rates should be lower than medium project rates');
        }
        if (lumpSum.medium_project.good >= lumpSum.large_project.good) {
          warnings.push('Medium project rates should be lower than large project rates');
        }
      }
    }

    setValidation({
      isValid: errors.length === 0,
      errors,
      warnings
    });
  };

  const handleSave = async () => {
    if (!validation.isValid) return;

    try {
      await onSave(formData);
    } catch (error) {
      console.error('Error saving labor rate:', error);
    }
  };

  const updateFormData = (path: string, value: any) => {
    const keys = path.split('.');
    const newData = { ...formData };
    let current: any = newData;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) current[keys[i]] = {};
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    setFormData(newData);
  };

  const calculateEstimatedCosts = () => {
    const baseArea = 1500; // sqft
    const baseQuantity = 25; // units

    const perUnitCost = baseQuantity * formData.rates.better;
    const lumpSumCost = formData.charging_models?.lump_sum_rates?.medium_project?.better || 0;
    const dailyCost = formData.charging_models?.daily_rates?.better ? 
      (baseQuantity / formData.productivity.output_per_day) * formData.charging_models.daily_rates.better : 0;
    const perSqftCost = formData.charging_models?.per_sqft_rates?.better ? 
      baseArea * formData.charging_models.per_sqft_rates.better : 0;

    return { perUnitCost, lumpSumCost, dailyCost, perSqftCost };
  };

  const estimatedCosts = calculateEstimatedCosts();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {laborRate ? 'Edit Labor Rate' : 'Add New Labor Rate'}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Configure multiple charging models for flexible pricing
              </p>
            </div>
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
              title="Close"
            >
              ×
            </button>
          </div>
        </div>

        {/* Validation Status */}
        {(validation.errors.length > 0 || validation.warnings.length > 0) && (
          <div className="px-6 py-3 border-b border-gray-200">
            {validation.errors.length > 0 && (
              <div className="flex items-center text-red-600 mb-2">
                <AlertCircle className="w-4 h-4 mr-2" />
                <span className="text-sm font-medium">Errors: {validation.errors.join(', ')}</span>
              </div>
            )}
            {validation.warnings.length > 0 && (
              <div className="flex items-center text-yellow-600">
                <Info className="w-4 h-4 mr-2" />
                <span className="text-sm">Warnings: {validation.warnings.join(', ')}</span>
              </div>
            )}
          </div>
        )}

        {/* Content */}
        <div className="flex h-[calc(90vh-200px)]">
          {/* Left Panel - Basic Info */}
          <div className="w-1/3 p-6 border-r border-gray-200 overflow-y-auto">
            <h4 className="font-medium text-gray-900 mb-4">Basic Information</h4>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Labor Name</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => updateFormData('name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="e.g., Tile Installation"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select
                  value={formData.category}
                  onChange={(e) => updateFormData('category', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="">Select Category</option>
                  <option value="foundation">Foundation</option>
                  <option value="masonry">Masonry</option>
                  <option value="finishes">Finishes</option>
                  <option value="flooring">Flooring</option>
                  <option value="electrical">Electrical</option>
                  <option value="plumbing">Plumbing</option>
                  <option value="specialized">Specialized</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Skill Level</label>
                <select
                  value={formData.skill_level}
                  onChange={(e) => updateFormData('skill_level', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="unskilled">Unskilled</option>
                  <option value="semiskilled">Semi-skilled</option>
                  <option value="skilled">Skilled</option>
                  <option value="specialist">Specialist</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Primary Model</label>
                <select
                  value={formData.charging_models?.primary_model || 'per_unit'}
                  onChange={(e) => updateFormData('charging_models.primary_model', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="per_unit">Per Unit</option>
                  <option value="lump_sum">Lump Sum</option>
                  <option value="per_day">Per Day</option>
                  <option value="per_sqft">Per Sqft</option>
                </select>
              </div>

              {/* Cost Comparison */}
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h5 className="font-medium text-gray-900 mb-3">Cost Comparison (1500 sqft project)</h5>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Per Unit:</span>
                    <span className="font-medium">₹{estimatedCosts.perUnitCost.toLocaleString()}</span>
                  </div>
                  {estimatedCosts.lumpSumCost > 0 && (
                    <div className="flex justify-between">
                      <span>Lump Sum:</span>
                      <span className="font-medium">₹{estimatedCosts.lumpSumCost.toLocaleString()}</span>
                    </div>
                  )}
                  {estimatedCosts.dailyCost > 0 && (
                    <div className="flex justify-between">
                      <span>Daily Rate:</span>
                      <span className="font-medium">₹{estimatedCosts.dailyCost.toLocaleString()}</span>
                    </div>
                  )}
                  {estimatedCosts.perSqftCost > 0 && (
                    <div className="flex justify-between">
                      <span>Per Sqft:</span>
                      <span className="font-medium">₹{estimatedCosts.perSqftCost.toLocaleString()}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel - Charging Models */}
          <div className="w-2/3 flex flex-col">
            {/* Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                {[
                  { id: 'per_unit', label: 'Per Unit', desc: 'Standard unit-based pricing' },
                  { id: 'lump_sum', label: 'Lump Sum', desc: 'Project-based pricing' },
                  { id: 'daily', label: 'Daily Rate', desc: 'Time-based pricing' },
                  { id: 'per_sqft', label: 'Per Sqft', desc: 'Area-based pricing' },
                  { id: 'recommendations', label: 'Usage', desc: 'When to use' }
                ].map(tab => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <div>
                      <div>{tab.label}</div>
                      <div className="text-xs text-gray-400">{tab.desc}</div>
                    </div>
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="flex-1 p-6 overflow-y-auto">
              {activeTab === 'per_unit' && (
                <div className="space-y-6">
                  <h4 className="font-medium text-gray-900">Per Unit Pricing</h4>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Unit</label>
                      <input
                        type="text"
                        value={formData.unit}
                        onChange={(e) => updateFormData('unit', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                        placeholder="e.g., sqm, point, set"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Output per Day</label>
                      <input
                        type="number"
                        value={formData.productivity.output_per_day}
                        onChange={(e) => updateFormData('productivity.output_per_day', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Good Rate (₹)</label>
                      <input
                        type="number"
                        value={formData.rates.good}
                        onChange={(e) => updateFormData('rates.good', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Better Rate (₹)</label>
                      <input
                        type="number"
                        value={formData.rates.better}
                        onChange={(e) => updateFormData('rates.better', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Best Rate (₹)</label>
                      <input
                        type="number"
                        value={formData.rates.best}
                        onChange={(e) => updateFormData('rates.best', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'lump_sum' && (
                <div className="space-y-6">
                  <h4 className="font-medium text-gray-900">Lump Sum Pricing</h4>
                  <p className="text-sm text-gray-600">
                    Fixed pricing for different project sizes. Usually 15-25% lower than per-unit pricing.
                  </p>

                  {['small_project', 'medium_project', 'large_project'].map(size => (
                    <div key={size} className="border border-gray-200 rounded-lg p-4">
                      <h5 className="font-medium text-gray-900 mb-3 capitalize">
                        {size.replace('_', ' ')} ({size === 'small_project' ? '<1000 sqft' : size === 'medium_project' ? '1000-2000 sqft' : '>2000 sqft'})
                      </h5>
                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Good (₹)</label>
                          <input
                            type="number"
                            value={formData.charging_models?.lump_sum_rates?.[size as keyof typeof formData.charging_models.lump_sum_rates]?.good || 0}
                            onChange={(e) => updateFormData(`charging_models.lump_sum_rates.${size}.good`, Number(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Better (₹)</label>
                          <input
                            type="number"
                            value={formData.charging_models?.lump_sum_rates?.[size as keyof typeof formData.charging_models.lump_sum_rates]?.better || 0}
                            onChange={(e) => updateFormData(`charging_models.lump_sum_rates.${size}.better`, Number(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Best (₹)</label>
                          <input
                            type="number"
                            value={formData.charging_models?.lump_sum_rates?.[size as keyof typeof formData.charging_models.lump_sum_rates]?.best || 0}
                            onChange={(e) => updateFormData(`charging_models.lump_sum_rates.${size}.best`, Number(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {activeTab === 'daily' && (
                <div className="space-y-6">
                  <h4 className="font-medium text-gray-900">Daily Rate Pricing</h4>
                  <p className="text-sm text-gray-600">
                    Time-based pricing for maintenance, modifications, or when scope is unclear.
                  </p>

                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Good Rate (₹/day)</label>
                      <input
                        type="number"
                        value={formData.charging_models?.daily_rates?.good || 0}
                        onChange={(e) => updateFormData('charging_models.daily_rates.good', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Better Rate (₹/day)</label>
                      <input
                        type="number"
                        value={formData.charging_models?.daily_rates?.better || 0}
                        onChange={(e) => updateFormData('charging_models.daily_rates.better', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Best Rate (₹/day)</label>
                      <input
                        type="number"
                        value={formData.charging_models?.daily_rates?.best || 0}
                        onChange={(e) => updateFormData('charging_models.daily_rates.best', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'per_sqft' && (
                <div className="space-y-6">
                  <h4 className="font-medium text-gray-900">Per Sqft Pricing</h4>
                  <p className="text-sm text-gray-600">
                    Area-based pricing common in commercial projects and electrical/plumbing work.
                  </p>

                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Good Rate (₹/sqft)</label>
                      <input
                        type="number"
                        value={formData.charging_models?.per_sqft_rates?.good || 0}
                        onChange={(e) => updateFormData('charging_models.per_sqft_rates.good', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Better Rate (₹/sqft)</label>
                      <input
                        type="number"
                        value={formData.charging_models?.per_sqft_rates?.better || 0}
                        onChange={(e) => updateFormData('charging_models.per_sqft_rates.better', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Best Rate (₹/sqft)</label>
                      <input
                        type="number"
                        value={formData.charging_models?.per_sqft_rates?.best || 0}
                        onChange={(e) => updateFormData('charging_models.per_sqft_rates.best', Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'recommendations' && (
                <div className="space-y-6">
                  <h4 className="font-medium text-gray-900">Usage Recommendations</h4>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Recommended for Project Sizes</label>
                    <div className="space-y-2">
                      {['small', 'medium', 'large'].map(size => (
                        <label key={size} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.charging_models?.recommended_for?.project_sizes?.includes(size) || false}
                            onChange={(e) => {
                              const current = formData.charging_models?.recommended_for?.project_sizes || [];
                              const updated = e.target.checked 
                                ? [...current, size]
                                : current.filter(s => s !== size);
                              updateFormData('charging_models.recommended_for.project_sizes', updated);
                            }}
                            className="mr-2"
                          />
                          <span className="capitalize">{size} Projects</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Recommended for Project Types</label>
                    <div className="space-y-2">
                      {['residential', 'commercial', 'industrial'].map(type => (
                        <label key={type} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.charging_models?.recommended_for?.project_types?.includes(type) || false}
                            onChange={(e) => {
                              const current = formData.charging_models?.recommended_for?.project_types || [];
                              const updated = e.target.checked 
                                ? [...current, type]
                                : current.filter(t => t !== type);
                              updateFormData('charging_models.recommended_for.project_types', updated);
                            }}
                            className="mr-2"
                          />
                          <span className="capitalize">{type}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Usage Notes</label>
                    <textarea
                      value={formData.charging_models?.recommended_for?.notes || ''}
                      onChange={(e) => updateFormData('charging_models.recommended_for.notes', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      rows={4}
                      placeholder="When to use this labor rate, special considerations, etc."
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-between items-center">
          <div className="flex items-center">
            {validation.isValid ? (
              <div className="flex items-center text-green-600">
                <CheckCircle className="w-4 h-4 mr-2" />
                <span className="text-sm">Ready to save</span>
              </div>
            ) : (
              <div className="flex items-center text-red-600">
                <AlertCircle className="w-4 h-4 mr-2" />
                <span className="text-sm">Please fix errors before saving</span>
              </div>
            )}
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={onCancel}
              className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={!validation.isValid}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors flex items-center"
            >
              <Save className="w-4 h-4 mr-2" />
              Save Labor Rate
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
