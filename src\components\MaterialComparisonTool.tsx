import React, { useState, useEffect } from 'react';
import { Component } from '../lib/supabase';
import { validatePrice } from '../utils/priceValidation';

interface MaterialComparison {
  component: Component;
  adjustedPrice: number;
  pricePerSqft: number;
  qualityScore: number;
  valueScore: number;
  pros: string[];
  cons: string[];
  bestFor: string[];
}

interface MaterialComparisonToolProps {
  category: string;
  subCategory: string;
  components: Component[];
  quantity: number;
  unit: string;
  location: string;
  qualityTier: 'good' | 'better' | 'best';
  onMaterialSelect: (componentId: string, adjustedPrice: number) => void;
}

export function MaterialComparisonTool({
  category,
  subCategory,
  components,
  quantity,
  unit,
  location,
  qualityTier,
  onMaterialSelect
}: MaterialComparisonToolProps) {
  const [comparisons, setComparisons] = useState<MaterialComparison[]>([]);
  const [sortBy, setSortBy] = useState<'price' | 'quality' | 'value'>('value');
  const [selectedMaterial, setSelectedMaterial] = useState<string>('');

  useEffect(() => {
    generateComparisons();
  }, [components, category, subCategory, quantity, location, qualityTier]);

  const generateComparisons = () => {
    const relevantComponents = components.filter(
      c => c.category === category && (c.sub_category === subCategory || !subCategory)
    );

    const materialComparisons: MaterialComparison[] = relevantComponents.map(component => {
      const adjustedPrice = calculateAdjustedPrice(component);
      const pricePerSqft = unit === 'sqft' ? adjustedPrice : adjustedPrice; // Normalize if needed
      const qualityScore = calculateQualityScore(component);
      const valueScore = calculateValueScore(adjustedPrice, qualityScore);
      
      return {
        component,
        adjustedPrice,
        pricePerSqft,
        qualityScore,
        valueScore,
        pros: generatePros(component),
        cons: generateCons(component),
        bestFor: generateBestFor(component)
      };
    });

    // Sort by selected criteria
    materialComparisons.sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return a.adjustedPrice - b.adjustedPrice;
        case 'quality':
          return b.qualityScore - a.qualityScore;
        case 'value':
          return b.valueScore - a.valueScore;
        default:
          return 0;
      }
    });

    setComparisons(materialComparisons);
  };

  const calculateAdjustedPrice = (component: Component): number => {
    // Apply regional and quality tier adjustments
    const regionalMultiplier = getRegionalMultiplier(location);
    const qualityMultiplier = getQualityTierMultiplier(qualityTier);
    return component.unit_price * regionalMultiplier * qualityMultiplier;
  };

  const calculateQualityScore = (component: Component): number => {
    let score = 50; // Base score
    
    const specs = component.specifications || {};
    
    // Brand reputation
    const premiumBrands = ['UltraTech', 'Kajaria', 'Jaquar', 'Legrand', 'Schneider', 'Asian Paints'];
    if (premiumBrands.includes(component.brand || '')) score += 20;
    
    // Warranty
    const warranty = specs.warranty || '';
    if (warranty.includes('10 years') || warranty.includes('lifetime')) score += 15;
    else if (warranty.includes('5 years')) score += 10;
    else if (warranty.includes('2 years')) score += 5;
    
    // Quality tier
    if (specs.quality_tier === 'best') score += 20;
    else if (specs.quality_tier === 'better') score += 10;
    
    // Certifications and standards
    if (specs.standard || specs.certification) score += 10;
    
    // Special features
    if (specs.features && Array.isArray(specs.features)) {
      score += Math.min(specs.features.length * 2, 10);
    }
    
    return Math.min(score, 100);
  };

  const calculateValueScore = (price: number, qualityScore: number): number => {
    // Value = Quality / Price ratio, normalized to 0-100
    const priceScore = Math.max(0, 100 - (price / 100)); // Inverse price score
    return (qualityScore * 0.6) + (priceScore * 0.4);
  };

  const generatePros = (component: Component): string[] => {
    const pros: string[] = [];
    const specs = component.specifications || {};
    
    if (component.brand && ['UltraTech', 'Kajaria', 'Jaquar'].includes(component.brand)) {
      pros.push('Trusted brand reputation');
    }
    
    if (specs.warranty?.includes('10 years')) {
      pros.push('Long warranty period');
    }
    
    if (specs.quality_tier === 'best') {
      pros.push('Premium quality materials');
    }
    
    if (specs.finish?.includes('premium') || specs.finish?.includes('luxury')) {
      pros.push('Superior finish quality');
    }
    
    if (specs.durability || specs.strength) {
      pros.push('High durability');
    }
    
    return pros.slice(0, 4);
  };

  const generateCons = (component: Component): string[] => {
    const cons: string[] = [];
    const specs = component.specifications || {};
    
    const validation = validatePrice(component.unit_price, component.category, component.sub_category || '', component.name);
    
    if (validation.deviationPercentage > 30) {
      cons.push('Above market average pricing');
    }
    
    if (specs.quality_tier === 'good') {
      cons.push('Basic quality tier');
    }
    
    if (!specs.warranty || specs.warranty.includes('1 year')) {
      cons.push('Limited warranty');
    }
    
    if (component.brand === 'Generic' || !component.brand) {
      cons.push('Unknown brand');
    }
    
    return cons.slice(0, 3);
  };

  const generateBestFor = (component: Component): string[] => {
    const bestFor: string[] = [];
    const specs = component.specifications || {};
    
    if (specs.quality_tier === 'good') {
      bestFor.push('Budget-conscious projects');
    } else if (specs.quality_tier === 'best') {
      bestFor.push('Luxury homes');
      bestFor.push('High-end commercial projects');
    } else {
      bestFor.push('Mid-range residential');
      bestFor.push('Quality-focused projects');
    }
    
    if (category === 'Flooring') {
      if (subCategory?.includes('Anti-Skid')) {
        bestFor.push('Bathrooms and wet areas');
      } else if (subCategory?.includes('Vitrified')) {
        bestFor.push('Living areas and bedrooms');
      }
    }
    
    return bestFor.slice(0, 3);
  };

  const handleMaterialSelect = (comparison: MaterialComparison) => {
    setSelectedMaterial(comparison.component.id);
    onMaterialSelect(comparison.component.id, comparison.adjustedPrice);
  };

  const getScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBackground = (score: number): string => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Material Comparison</h3>
          <p className="text-sm text-gray-600">{category} - {subCategory} ({quantity} {unit})</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700">Sort by:</label>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm"
          >
            <option value="value">Best Value</option>
            <option value="price">Lowest Price</option>
            <option value="quality">Highest Quality</option>
          </select>
        </div>
      </div>

      {/* Comparison Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {comparisons.map((comparison, index) => (
          <div
            key={comparison.component.id}
            className={`border rounded-lg p-4 cursor-pointer transition-all ${
              selectedMaterial === comparison.component.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handleMaterialSelect(comparison)}
          >
            {/* Header */}
            <div className="flex items-start justify-between mb-3">
              <div>
                <h4 className="font-medium text-gray-900">{comparison.component.brand || 'Generic'}</h4>
                <p className="text-sm text-gray-600">{comparison.component.name}</p>
              </div>
              {index === 0 && sortBy === 'value' && (
                <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">
                  BEST VALUE
                </span>
              )}
              {index === 0 && sortBy === 'price' && (
                <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">
                  LOWEST PRICE
                </span>
              )}
              {index === 0 && sortBy === 'quality' && (
                <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded">
                  HIGHEST QUALITY
                </span>
              )}
            </div>

            {/* Price */}
            <div className="mb-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Cost:</span>
                <span className="text-lg font-bold">₹{(comparison.adjustedPrice * quantity).toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">Per {unit}:</span>
                <span className="text-sm">₹{comparison.adjustedPrice}</span>
              </div>
            </div>

            {/* Scores */}
            <div className="grid grid-cols-2 gap-2 mb-3">
              <div className={`p-2 rounded text-center ${getScoreBackground(comparison.qualityScore)}`}>
                <div className={`text-lg font-bold ${getScoreColor(comparison.qualityScore)}`}>
                  {comparison.qualityScore}
                </div>
                <div className="text-xs text-gray-600">Quality</div>
              </div>
              <div className={`p-2 rounded text-center ${getScoreBackground(comparison.valueScore)}`}>
                <div className={`text-lg font-bold ${getScoreColor(comparison.valueScore)}`}>
                  {Math.round(comparison.valueScore)}
                </div>
                <div className="text-xs text-gray-600">Value</div>
              </div>
            </div>

            {/* Pros */}
            {comparison.pros.length > 0 && (
              <div className="mb-2">
                <p className="text-xs font-medium text-green-700 mb-1">Advantages:</p>
                <ul className="text-xs text-green-600 space-y-1">
                  {comparison.pros.map((pro, i) => (
                    <li key={i}>• {pro}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Cons */}
            {comparison.cons.length > 0 && (
              <div className="mb-2">
                <p className="text-xs font-medium text-red-700 mb-1">Considerations:</p>
                <ul className="text-xs text-red-600 space-y-1">
                  {comparison.cons.map((con, i) => (
                    <li key={i}>• {con}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Best For */}
            <div>
              <p className="text-xs font-medium text-blue-700 mb-1">Best for:</p>
              <div className="flex flex-wrap gap-1">
                {comparison.bestFor.map((use, i) => (
                  <span key={i} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                    {use}
                  </span>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {comparisons.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No materials available for comparison</p>
          <p className="text-sm mt-1">Check if components are available for {category} - {subCategory}</p>
        </div>
      )}
    </div>
  );
}

// Helper functions (simplified versions)
function getRegionalMultiplier(location: string): number {
  const multipliers: Record<string, number> = {
    'delhi': 1.0,
    'gurgaon': 1.15,
    'noida': 1.05,
    'ghaziabad': 0.95
  };
  return multipliers[location.toLowerCase()] || 1.0;
}

function getQualityTierMultiplier(tier: 'good' | 'better' | 'best'): number {
  switch (tier) {
    case 'good': return 0.85;
    case 'better': return 1.0;
    case 'best': return 1.35;
    default: return 1.0;
  }
}
