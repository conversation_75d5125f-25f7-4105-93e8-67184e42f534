import React, { useState } from 'react';
import { supabase } from '../../lib/supabase';

interface AdminAuthHelperProps {
  onAuthSuccess?: () => void;
}

export function AdminAuthHelper({ onAuthSuccess }: AdminAuthHelperProps) {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleQuickAuth = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      // For development/testing purposes, we'll create a simple auth bypass
      // In production, this should use proper authentication
      
      const testEmail = email || '<EMAIL>';
      
      // Check if user exists in admin_users
      const { data: adminUser, error: adminError } = await supabase
        .from('admin_users')
        .select('*')
        .eq('email', testEmail)
        .single();

      if (adminError && adminError.code !== 'PGRST116') {
        throw adminError;
      }

      if (!adminUser) {
        // Add user to admin_users table
        const { error: insertError } = await supabase
          .from('admin_users')
          .insert({
            email: testEmail,
            full_name: 'Admin User',
            role: 'super_admin',
            is_active: true
          });

        if (insertError) {
          throw insertError;
        }

        setMessage(`✅ Added ${testEmail} as admin user`);
      } else {
        setMessage(`✅ ${testEmail} is already an admin user`);
      }

      // For testing, we'll simulate authentication
      // In a real app, you'd use supabase.auth.signInWithPassword or similar
      
      if (onAuthSuccess) {
        onAuthSuccess();
      }

    } catch (error: any) {
      console.error('Auth helper error:', error);
      setMessage(`❌ Error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestConnection = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      // Test basic database connection
      const { data, error } = await supabase
        .from('components')
        .select('count')
        .limit(1);

      if (error) {
        throw error;
      }

      setMessage('✅ Database connection successful');

      // Test admin access
      const { data: adminData, error: adminError } = await supabase
        .from('admin_users')
        .select('*')
        .limit(1);

      if (adminError) {
        setMessage(prev => prev + `\n❌ Admin table access failed: ${adminError.message}`);
      } else {
        setMessage(prev => prev + `\n✅ Admin table accessible (${adminData.length} users)`);
      }

    } catch (error: any) {
      setMessage(`❌ Connection test failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDirectUpdate = async () => {
    setIsLoading(true);
    setMessage('');

    try {
      // Try to update a component directly
      const { data: components, error: fetchError } = await supabase
        .from('components')
        .select('id, name, unit_price')
        .limit(1);

      if (fetchError) {
        throw fetchError;
      }

      if (components.length === 0) {
        setMessage('❌ No components found to test update');
        return;
      }

      const testComponent = components[0];
      const originalPrice = testComponent.unit_price;
      const newPrice = parseFloat(originalPrice) + 0.01; // Small increment

      setMessage(`Testing update on ${testComponent.name} (${testComponent.id})`);

      // Try to update
      const { data: updateData, error: updateError } = await supabase
        .from('components')
        .update({ unit_price: newPrice })
        .eq('id', testComponent.id)
        .select();

      if (updateError) {
        throw updateError;
      }

      setMessage(prev => prev + `\n✅ Update successful! Price changed to ${newPrice}`);

      // Revert the change
      await supabase
        .from('components')
        .update({ unit_price: originalPrice })
        .eq('id', testComponent.id);

      setMessage(prev => prev + `\n✅ Reverted price back to ${originalPrice}`);

    } catch (error: any) {
      setMessage(prev => prev + `\n❌ Direct update failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg border">
      <h3 className="text-lg font-semibold mb-4">Admin Authentication Helper</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email for Admin Access:
          </label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div className="flex space-x-2">
          <button
            onClick={handleQuickAuth}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {isLoading ? 'Processing...' : 'Setup Admin Access'}
          </button>

          <button
            onClick={handleTestConnection}
            disabled={isLoading}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
          >
            Test Connection
          </button>

          <button
            onClick={handleDirectUpdate}
            disabled={isLoading}
            className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:opacity-50"
          >
            Test Update
          </button>
        </div>

        {message && (
          <div className="p-3 bg-gray-100 rounded text-sm font-mono whitespace-pre-line">
            {message}
          </div>
        )}
      </div>

      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h4 className="font-semibold text-yellow-800 mb-2">Troubleshooting Steps:</h4>
        <ol className="text-sm text-yellow-700 space-y-1">
          <li>1. Click "Test Connection" to verify database access</li>
          <li>2. Click "Setup Admin Access" to add your email to admin_users table</li>
          <li>3. Click "Test Update" to verify component update permissions</li>
          <li>4. If issues persist, check the Debug Panel tab for detailed diagnostics</li>
        </ol>
      </div>
    </div>
  );
}
