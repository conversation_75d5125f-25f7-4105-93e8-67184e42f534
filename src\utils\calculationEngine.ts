import { UserInputs, GeometricQuantities, CalculatedQuantities, CostSection, CostItem, QualityTier, CalculationResult, RoomConfiguration, RoomMaterialSelection, ProjectSummary, CalculationBreakdown } from '../types/calculator';
import { EngineeringStandards } from '../lib/supabase';

/**
 * PART 1: DIGITAL TAKEDOWN - COMPLETE GEOMETRIC ANALYSIS
 * This function performs engineering-grade geometric analysis using SI units internally
 */
export function performDigitalTakedown(
  inputs: UserInputs,
  standards?: EngineeringStandards
): GeometricQuantities {
  // Convert input from sqft to sqm for internal calculations
  const plotSizeSqm = convertSqftToSqm(inputs.plotSize);
  const groundCoverageAreaSqm = plotSizeSqm * (inputs.constructionPercentage / 100);
  
  // Basic area calculations in sqm
  const basementAreaSqm = inputs.hasBasement ? groundCoverageAreaSqm : 0;
  const stiltAreaSqm = inputs.hasStiltParking ? groundCoverageAreaSqm : 0;
  
  // Calculate residential floors (excluding stilt)
  const residentialFloors = inputs.hasStiltParking ? inputs.numberOfFloors - 1 : inputs.numberOfFloors;
  const totalBuiltUpAreaSqm = groundCoverageAreaSqm * residentialFloors;
  const totalConstructionAreaSqm = totalBuiltUpAreaSqm + basementAreaSqm + stiltAreaSqm;

  // STRUCTURAL GRID SIMULATION
  const buildingLength = Math.sqrt(groundCoverageAreaSqm * 1.5); // Rectangular assumption
  const buildingWidth = groundCoverageAreaSqm / buildingLength;
  
  // Grid spacing (from standards or default)
  const gridSpacing = standards ? standards.structuralAssumptions.gridSpacing : 4.0; // 4 meters typical span
  const numberOfBaysLength = Math.ceil(buildingLength / gridSpacing);
  const numberOfBaysWidth = Math.ceil(buildingWidth / gridSpacing);
  
  // FOUNDATION CALCULATIONS
  const foundationDepth = standards 
    ? standards.structuralAssumptions.foundationDepth 
    : (inputs.hasBasement ? 3.5 : 1.5); // meters
    
  const foundationType = inputs.hasBasement ? 'raft_foundation' : 'strip_footings';
  
  // Foundation volume calculation
  let concreteVolume_Foundations_m3: number;
  if (inputs.hasBasement) {
    // Raft foundation + retaining walls
    const raftThickness = 0.3; // 300mm thick raft
    const retainingWallThickness = 0.25; // 250mm thick walls
    const retainingWallHeight = 3.0; // 3m height
    const perimeter = 2 * (buildingLength + buildingWidth);
    
    concreteVolume_Foundations_m3 = 
      groundCoverageAreaSqm * raftThickness + // Raft slab
      perimeter * retainingWallHeight * retainingWallThickness; // Retaining walls
  } else {
    // Strip footings
    const footingWidth = 0.6; // 600mm wide
    const footingDepth = 0.3; // 300mm deep
    const totalFootingLength = (numberOfBaysLength + 1) * buildingWidth + 
                              (numberOfBaysWidth + 1) * buildingLength;
    
    concreteVolume_Foundations_m3 = totalFootingLength * footingWidth * footingDepth;
  }

  // COLUMN CALCULATIONS
  const totalFloors = inputs.numberOfFloors + (inputs.hasBasement ? 1 : 0);
  const floorHeight = 3.0; // 3m floor height
  
  // Get column size from standards or use default
  let columnSize = 0.3; // Default 300mm x 300mm columns
  if (standards && standards.structuralAssumptions.columnSizing.length > 0) {
    // Find appropriate column size based on number of floors
    const columnSizing = standards.structuralAssumptions.columnSizing;
    let selectedSize;
    
    if (inputs.numberOfFloors <= 2) {
      // G+1
      selectedSize = columnSizing.find(s => s.floors.includes('G+1'));
    } else if (inputs.numberOfFloors <= 4) {
      // G+2 to G+3
      selectedSize = columnSizing.find(s => s.floors.includes('G+2') || s.floors.includes('G+3'));
    } else {
      // G+4 and above
      selectedSize = columnSizing.find(s => s.floors.includes('G+4') || s.floors.includes('above'));
    }
    
    if (selectedSize) {
      // Parse column size (e.g., "350 x 350" -> 0.35)
      const dimensions = selectedSize.column_size.split('x').map(d => parseInt(d.trim()) / 1000);
      columnSize = dimensions[0]; // Assuming square columns
    }
  }
  
  const numberOfColumns = (numberOfBaysLength + 1) * (numberOfBaysWidth + 1);
  
  const columns_verticalM = numberOfColumns * totalFloors * floorHeight;
  const concreteVolume_Columns_m3 = numberOfColumns * totalFloors * floorHeight * 
                                   columnSize * columnSize;

  // BEAM CALCULATIONS
  // Get beam width from standards or use default
  let beamWidth = 0.23; // Default 230mm
  let beamDepth = 0.45; // Default 450mm
  
  if (standards && standards.structuralAssumptions.columnSizing.length > 0) {
    // Find appropriate beam size based on number of floors
    const columnSizing = standards.structuralAssumptions.columnSizing;
    let selectedSize;
    
    if (inputs.numberOfFloors <= 2) {
      selectedSize = columnSizing.find(s => s.floors.includes('G+1'));
    } else if (inputs.numberOfFloors <= 4) {
      selectedSize = columnSizing.find(s => s.floors.includes('G+2') || s.floors.includes('G+3'));
    } else {
      selectedSize = columnSizing.find(s => s.floors.includes('G+4') || s.floors.includes('above'));
    }
    
    if (selectedSize) {
      beamWidth = selectedSize.beam_width / 1000; // Convert mm to meters
      beamDepth = beamWidth * 2; // Typical beam depth is twice the width
    }
  }
  
  // Main beams (along length)
  const mainBeamLength = numberOfBaysLength * gridSpacing * (numberOfBaysWidth + 1);
  // Secondary beams (along width)  
  const secondaryBeamLength = numberOfBaysWidth * gridSpacing * (numberOfBaysLength + 1);
  
  const totalBeamLength = (mainBeamLength + secondaryBeamLength) * totalFloors;
  const floorBeams_linearM = totalBeamLength;
  const foundationBeams_linearM = mainBeamLength + secondaryBeamLength; // Plinth level
  const plinthBeams_linearM = foundationBeams_linearM;
  
  const concreteVolume_Beams_m3 = totalBeamLength * beamWidth * beamDepth;

  // SLAB CALCULATIONS
  // Get slab thickness from standards or use default
  const slabThickness = standards 
    ? standards.structuralAssumptions.slabThickness / 1000 // Convert mm to meters
    : 0.125; // Default 125mm thick slab
    
  const totalSlabArea = groundCoverageAreaSqm * totalFloors;
  const concreteVolume_Slabs_m3 = totalSlabArea * slabThickness;

  // TOTAL CONCRETE VOLUME
  const totalConcreteVolume_m3 = concreteVolume_Foundations_m3 + 
                                concreteVolume_Columns_m3 + 
                                concreteVolume_Beams_m3 + 
                                concreteVolume_Slabs_m3;

  // STEEL CALCULATIONS (using engineering ratios from standards)
  // Get steel ratios from standards or use defaults
  const foundationSteelRatio = standards && standards.steelRatios.foundations[foundationType]
    ? standards.steelRatios.foundations[foundationType]
    : 120;
    
  const columnSteelRatio = standards && standards.steelRatios.columns
    ? (inputs.numberOfFloors > 3 
      ? standards.steelRatios.columns.ground_floor || 200
      : standards.steelRatios.columns.typical_floor || 180)
    : (inputs.numberOfFloors > 3 ? 200 : 180);
    
  const beamSteelRatio = standards && standards.steelRatios.beams
    ? standards.steelRatios.beams.main_beams || 160
    : 160;
    
  const slabSteelRatio = standards && standards.steelRatios.slabs
    ? standards.steelRatios.slabs.two_way_slab || 120
    : 120;

  const steel_foundations_kg = concreteVolume_Foundations_m3 * foundationSteelRatio;
  const steel_columns_kg = concreteVolume_Columns_m3 * columnSteelRatio;
  const steel_beams_kg = concreteVolume_Beams_m3 * beamSteelRatio;
  const steel_slabs_kg = concreteVolume_Slabs_m3 * slabSteelRatio;
  const totalSteel_kg = steel_foundations_kg + steel_columns_kg + 
                       steel_beams_kg + steel_slabs_kg;

  // WALL AREA CALCULATIONS
  const wallHeight = floorHeight - 0.45; // Minus beam depth
  const externalPerimeter = 2 * (buildingLength + buildingWidth);
  
  // External walls (all floors)
  const externalWallArea_sqm = externalPerimeter * wallHeight * residentialFloors;
  
  // Internal walls (estimated as 60% of external wall area)
  const internalWallArea_sqm = externalWallArea_sqm * 0.6;
  const totalWallArea_sqm = externalWallArea_sqm + internalWallArea_sqm;

  // FLOOR AND CEILING AREAS
  const totalFloorArea_sqm = totalBuiltUpAreaSqm;
  const totalCeilingArea_sqm = totalFloorArea_sqm;

  // CONCRETE GRADE SELECTION
  let concreteGrade = 'M25'; // Default
  if (inputs.numberOfFloors > 3 || inputs.hasBasement) {
    concreteGrade = 'M30'; // Higher grade for complex structures
  }

  // STRUCTURAL SYSTEM
  const structuralSystem = inputs.numberOfFloors > 2 ? 'Frame Structure' : 'Load Bearing';

  // Generate room configuration
  const roomConfiguration = generateDefaultRoomConfiguration(convertSqmToSqft(totalBuiltUpAreaSqm));

  // Convert back to sqft for display (maintaining internal consistency)
  return {
    // Basic Areas (converted back to sqft for display)
    groundCoverageArea: convertSqmToSqft(groundCoverageAreaSqm),
    totalBuiltUpArea: convertSqmToSqft(totalBuiltUpAreaSqm),
    basementArea: convertSqmToSqft(basementAreaSqm),
    stiltArea: convertSqmToSqft(stiltAreaSqm),
    totalConstructionArea: convertSqmToSqft(totalConstructionAreaSqm),
    
    // Structural Elements (in cubic meters - SI units)
    concreteVolume_Foundations_m3,
    concreteVolume_Columns_m3,
    concreteVolume_Beams_m3,
    concreteVolume_Slabs_m3,
    totalConcreteVolume_m3,
    
    // Linear Measurements (in meters - SI units)
    foundationBeams_linearM,
    plinthBeams_linearM,
    floorBeams_linearM,
    columns_verticalM,
    
    // Surface Areas (in square meters - SI units)
    totalWallArea_sqm,
    totalFloorArea_sqm,
    totalCeilingArea_sqm,
    externalWallArea_sqm,
    internalWallArea_sqm,
    
    // Steel Requirements (in kg)
    steel_foundations_kg,
    steel_columns_kg,
    steel_beams_kg,
    steel_slabs_kg,
    totalSteel_kg,
    
    // Configuration
    roomConfiguration,
    concreteGrade,
    foundationType,
    structuralSystem
  };
}

/**
 * PART 2: STEEL REQUIREMENTS CALCULATION
 * Component-wise steel calculation with full transparency
 */
function calculateSteelRequirements(
  geometricQuantities: GeometricQuantities,
  qualityTier: QualityTier,
  standards?: EngineeringStandards
): {
  totalSteel_kg: number;
  breakdown: CalculationBreakdown;
} {
  const {
    concreteVolume_Foundations_m3,
    concreteVolume_Columns_m3,
    concreteVolume_Beams_m3,
    concreteVolume_Slabs_m3,
    steel_foundations_kg,
    steel_columns_kg,
    steel_beams_kg,
    steel_slabs_kg,
    totalSteel_kg,
    foundationType
  } = geometricQuantities;

  // Get steel ratios from standards or use defaults
  const foundationRatio = standards && standards.steelRatios.foundations[foundationType]
    ? standards.steelRatios.foundations[foundationType]
    : 120;
    
  const columnRatio = standards && standards.steelRatios.columns.typical_floor
    ? standards.steelRatios.columns.typical_floor
    : 180;
    
  const beamRatio = standards && standards.steelRatios.beams.main_beams
    ? standards.steelRatios.beams.main_beams
    : 160;
    
  const slabRatio = standards && standards.steelRatios.slabs.two_way_slab
    ? standards.steelRatios.slabs.two_way_slab
    : 120;

  const breakdown: CalculationBreakdown = {
    formula: 'Steel Quantity = Concrete Volume × Steel Ratio (as per IS 456:2000)',
    components: [
      {
        name: 'Foundation Steel',
        quantity: concreteVolume_Foundations_m3,
        unit: 'm³',
        rate: foundationRatio,
        total: steel_foundations_kg,
        source: 'IS 456:2000 - Foundation reinforcement'
      },
      {
        name: 'Column Steel',
        quantity: concreteVolume_Columns_m3,
        unit: 'm³',
        rate: columnRatio,
        total: steel_columns_kg,
        source: 'IS 456:2000 - Column reinforcement'
      },
      {
        name: 'Beam Steel',
        quantity: concreteVolume_Beams_m3,
        unit: 'm³',
        rate: beamRatio,
        total: steel_beams_kg,
        source: 'IS 456:2000 - Beam reinforcement'
      },
      {
        name: 'Slab Steel',
        quantity: concreteVolume_Slabs_m3,
        unit: 'm³',
        rate: slabRatio,
        total: steel_slabs_kg,
        source: 'IS 456:2000 - Slab reinforcement'
      }
    ],
    totalQuantity: totalSteel_kg,
    materialCost: 0, // Will be calculated later
    laborCost: 0, // Will be calculated later
    finalCost: 0,
    notes: [
      'Steel ratios based on IS 456:2000 standards',
      'Includes cutting, bending, and fixing wastage',
      'TMT Fe 500D grade steel recommended for seismic zones'
    ]
  };

  return { totalSteel_kg, breakdown };
}

/**
 * PART 3: CONCRETE REQUIREMENTS CALCULATION
 * Material breakdown using concrete mix designs from standards
 */
function calculateConcreteRequirements(
  geometricQuantities: GeometricQuantities,
  qualityTier: QualityTier,
  standards?: EngineeringStandards
): {
  totalCement_bags: number;
  totalSand_cft: number;
  totalAggregate_cft: number;
  breakdown: CalculationBreakdown;
} {
  const { totalConcreteVolume_m3, concreteGrade } = geometricQuantities;
  
  // Get concrete mix from standards or use defaults
  let cementBags_per_m3 = 8.0;
  let sand_cft_per_m3 = 15.0;
  let aggregate_cft_per_m3 = 30.0;
  
  if (standards && standards.concreteMixes[concreteGrade]) {
    const concreteMix = standards.concreteMixes[concreteGrade];
    cementBags_per_m3 = concreteMix.cement_bags_per_m3;
    sand_cft_per_m3 = concreteMix.sand_cft_per_m3;
    aggregate_cft_per_m3 = concreteMix.aggregate_cft_per_m3;
  }

  const totalCement_bags = totalConcreteVolume_m3 * cementBags_per_m3;
  const totalSand_cft = totalConcreteVolume_m3 * sand_cft_per_m3;
  const totalAggregate_cft = totalConcreteVolume_m3 * aggregate_cft_per_m3;

  // Default rates based on quality tier
  const cementRate = qualityTier === 'best' ? 380 : qualityTier === 'better' ? 345 : 305;
  const sandRate = qualityTier === 'best' ? 55 : qualityTier === 'better' ? 50 : 45;
  const aggregateRate = qualityTier === 'best' ? 65 : qualityTier === 'better' ? 60 : 55;

  const breakdown: CalculationBreakdown = {
    formula: `${concreteGrade} Mix Design: ${cementBags_per_m3} bags cement + ${sand_cft_per_m3} cft sand + ${aggregate_cft_per_m3} cft aggregate per m³`,
    components: [
      {
        name: `Cement (${qualityTier} quality)`,
        quantity: totalCement_bags,
        unit: 'bags',
        rate: cementRate,
        total: totalCement_bags * cementRate,
        source: `${concreteGrade} mix design as per IS 10262:2019`
      },
      {
        name: `Sand (${qualityTier} quality)`,
        quantity: totalSand_cft,
        unit: 'cft',
        rate: sandRate,
        total: totalSand_cft * sandRate,
        source: 'Fine aggregate as per IS 383:2016'
      },
      {
        name: `Aggregate (${qualityTier} quality)`,
        quantity: totalAggregate_cft,
        unit: 'cft',
        rate: aggregateRate,
        total: totalAggregate_cft * aggregateRate,
        source: 'Coarse aggregate as per IS 383:2016'
      }
    ],
    totalQuantity: totalConcreteVolume_m3,
    materialCost: 0, // Will be calculated
    laborCost: 0, // Will be calculated
    finalCost: 0
  };

  // Calculate total material cost
  breakdown.materialCost = breakdown.components.reduce((sum, comp) => sum + comp.total, 0);

  return { totalCement_bags, totalSand_cft, totalAggregate_cft, breakdown };
}

/**
 * PART 4: MASONRY CALCULATIONS
 * Bottom-up masonry calculation using consumption ratios from standards
 */
function calculateMasonryRequirements(
  geometricQuantities: GeometricQuantities,
  selectedMaterial: string = 'aac_blocks',
  standards?: EngineeringStandards
): {
  totalBricks: number;
  breakdown: CalculationBreakdown;
} {
  const { totalWallArea_sqm } = geometricQuantities;
  
  // Get consumption ratio from standards or use defaults
  let consumptionRatio = 0;
  let materialName = '';
  
  if (selectedMaterial.includes('AAC')) {
    consumptionRatio = standards ? standards.materialConsumption.aacBlocksPerSqm_200mm : 8.33;
    materialName = 'AAC Blocks (200mm wall)';
  } else {
    consumptionRatio = standards ? standards.materialConsumption.bricksPerSqm_230mm : 110;
    materialName = 'Red Clay Bricks (230mm wall)';
  }

  const totalBricks = totalWallArea_sqm * consumptionRatio;

  const breakdown: CalculationBreakdown = {
    formula: `${materialName}: ${consumptionRatio} pieces per sqm of wall`,
    components: [
      {
        name: materialName,
        quantity: totalWallArea_sqm,
        unit: 'sqm',
        rate: consumptionRatio,
        total: totalBricks,
        source: 'IS 1077:1992 for bricks, Manufacturer standards for AAC blocks'
      }
    ],
    totalQuantity: totalBricks,
    materialCost: 0,
    laborCost: 0,
    finalCost: 0
  };

  return { totalBricks, breakdown };
}

/**
 * PART 5: MAIN CALCULATION ENGINE
 * Orchestrates the entire bottom-up calculation process with tiered labor rates
 */
export function calculateDetailedCost(
  inputs: UserInputs,
  quantities: CalculatedQuantities,
  qualityTier: QualityTier,
  overrides: Record<string, Partial<CostItem>> = {},
  roomMaterialSelections: RoomMaterialSelection[] = [],
  standards?: EngineeringStandards
): CalculationResult {
  // Step 1: Get detailed geometric analysis using SI units
  const geometricQuantities = performDigitalTakedown(inputs, standards);
  
  // Step 2: Location multipliers from standards or defaults
  const materialMultiplier = standards ? standards.regionalData.materialMultiplier : 1.0;
  const laborMultiplier = standards ? standards.regionalData.laborMultiplier : 1.0;
  
  const sections: CostSection[] = [];

  // FOUNDATION & STRUCTURE SECTION
  const foundationItems: CostItem[] = [];

  // Steel calculation with full transparency and tiered labor rates
  const steelCalc = calculateSteelRequirements(geometricQuantities, qualityTier, standards);
  
  // Default rates based on quality tier
  const steelRate = qualityTier === 'best' ? 52 : qualityTier === 'better' ? 48 : 46;
  const steelLaborRate = qualityTier === 'best' ? 9.5 : qualityTier === 'better' ? 8 : 6.5;
  
  steelCalc.breakdown.materialCost = steelCalc.totalSteel_kg * steelRate * materialMultiplier;
  steelCalc.breakdown.laborCost = steelCalc.totalSteel_kg * steelLaborRate * laborMultiplier;
  steelCalc.breakdown.finalCost = steelCalc.breakdown.materialCost + steelCalc.breakdown.laborCost;

  foundationItems.push({
    id: 'steel_work',
    name: `TMT Steel Work (${qualityTier === 'best' ? 'Tata Tiscon' : qualityTier === 'better' ? 'Jindal Panther' : 'Standard'})`,
    quantity: steelCalc.totalSteel_kg,
    unit: 'kg',
    rate: (steelRate * materialMultiplier) + (steelLaborRate * laborMultiplier),
    total: (steelCalc.breakdown.materialCost) + (steelCalc.breakdown.laborCost),
    category: 'foundation',
    editable: false, // Not editable - calculated from geometry
    isCalculated: true,
    calculationBreakdown: steelCalc.breakdown,
    isRecommended: qualityTier === 'best',
    recommendationNote: 'TATA Tiscon steel offers superior strength and corrosion resistance.'
  });

  // Concrete calculation with full transparency and tiered labor rates
  const concreteCalc = calculateConcreteRequirements(geometricQuantities, qualityTier, standards);
  
  // Default labor rate based on quality tier
  const concreteLaborRate = qualityTier === 'best' ? 520 : qualityTier === 'better' ? 450 : 380;
  
  concreteCalc.breakdown.laborCost = geometricQuantities.totalConcreteVolume_m3 * concreteLaborRate * laborMultiplier;
  concreteCalc.breakdown.finalCost = (concreteCalc.breakdown.materialCost * materialMultiplier) + concreteCalc.breakdown.laborCost;

  foundationItems.push({
    id: 'concrete_work',
    name: `RCC Work (${geometricQuantities.concreteGrade})`,
    quantity: geometricQuantities.totalConcreteVolume_m3,
    unit: 'm³',
    rate: ((concreteCalc.breakdown.materialCost / geometricQuantities.totalConcreteVolume_m3) * materialMultiplier) + (concreteLaborRate * laborMultiplier),
    total: concreteCalc.breakdown.finalCost,
    category: 'foundation',
    editable: false,
    isCalculated: true,
    calculationBreakdown: concreteCalc.breakdown
  });

  // Excavation calculation using tiered labor rates
  const excavationVolume = geometricQuantities.concreteVolume_Foundations_m3 * 1.2; // 20% extra for working space
  
  // Default excavation rate based on quality tier, adjusted by labor multiplier
  const excavationRate = (qualityTier === 'best' ? 600 : qualityTier === 'better' ? 525 : 450) * laborMultiplier;
  const excavationCost = excavationVolume * excavationRate;

  const excavationBreakdown: CalculationBreakdown = {
    formula: 'Excavation Volume = Foundation Volume × 1.2 (working space)',
    components: [
      {
        name: `Manual Excavation (${qualityTier} tier labor)`,
        quantity: excavationVolume,
        unit: 'm³',
        rate: excavationRate,
        total: excavationCost,
        source: `Manual excavation - ${inputs.location} rates (labor multiplier: ${laborMultiplier})`
      }
    ],
    totalQuantity: excavationVolume,
    materialCost: 0,
    laborCost: excavationCost,
    finalCost: excavationCost
  };

  foundationItems.push({
    id: 'excavation',
    name: 'Excavation Work',
    quantity: excavationVolume,
    unit: 'm³',
    rate: excavationRate,
    total: excavationCost,
    category: 'foundation',
    editable: false,
    isCalculated: true,
    calculationBreakdown: excavationBreakdown
  });

  // MASONRY & FINISHES SECTION
  const masonryItems: CostItem[] = [];

  // Masonry calculation with material selection and tiered labor rates
  const selectedMasonryMaterial = 'AAC Blocks'; // Default, can be made dynamic
  const masonryCalc = calculateMasonryRequirements(geometricQuantities, selectedMasonryMaterial, standards);
  
  // Default rates based on quality tier
  const aacBlockRate = qualityTier === 'best' ? 95 : qualityTier === 'better' ? 85 : 75;
  const masonryLaborRate = qualityTier === 'best' ? 110 : qualityTier === 'better' ? 95 : 80;
  
  // Apply wastage factor from standards or default
  const wastagePercentage = standards && standards.materialConsumption.wastageFactors.aac_blocks
    ? standards.materialConsumption.wastageFactors.aac_blocks
    : 3;
    
  const wastageMultiplier = 1 + (wastagePercentage / 100);
  
  masonryCalc.breakdown.materialCost = (masonryCalc.totalBricks / 8.33) * aacBlockRate * materialMultiplier * wastageMultiplier;
  masonryCalc.breakdown.laborCost = geometricQuantities.totalWallArea_sqm * masonryLaborRate * laborMultiplier;
  masonryCalc.breakdown.finalCost = masonryCalc.breakdown.materialCost + masonryCalc.breakdown.laborCost;

  masonryItems.push({
    id: 'masonry_work',
    name: `Masonry Work (${qualityTier === 'best' ? 'Magicrete' : qualityTier === 'better' ? 'Siporex' : 'Standard AAC'})`,
    quantity: geometricQuantities.totalWallArea_sqm,
    unit: 'sqm',
    rate: (masonryCalc.breakdown.finalCost / geometricQuantities.totalWallArea_sqm),
    total: masonryCalc.breakdown.finalCost,
    category: 'masonry',
    editable: false,
    isCalculated: true,
    calculationBreakdown: masonryCalc.breakdown,
    materialOptions: ['Red Clay Bricks', 'AAC Blocks'],
    selectedMaterial: selectedMasonryMaterial,
    isRecommended: true,
    recommendationNote: 'AAC blocks provide superior thermal insulation and reduce structural load.'
  });

  // Plastering calculation using consumption ratios and tiered labor rates
  const plasterArea = geometricQuantities.totalWallArea_sqm;
  
  // Default rates based on quality tier, adjusted by multipliers
  const plasterMaterialRate = (qualityTier === 'best' ? 30 : qualityTier === 'better' ? 25 : 20) * materialMultiplier;
  const plasterLaborRate = (qualityTier === 'best' ? 100 : qualityTier === 'better' ? 85 : 70) * laborMultiplier;
  
  // Apply wastage factor from standards or default
  const cementWastagePercentage = standards && standards.materialConsumption.wastageFactors.cement
    ? standards.materialConsumption.wastageFactors.cement
    : 2.5;
    
  const cementWastageMultiplier = 1 + (cementWastagePercentage / 100);
  
  // Calculate plaster material quantity (3.6 kg of cement per sqm for 12mm plaster)
  const plasterCementQty = plasterArea * 3.6 * cementWastageMultiplier;
  const plasterMaterialCost = plasterCementQty * (plasterMaterialRate / 50); // 50kg per bag
  const plasterLaborCost = plasterArea * plasterLaborRate;

  const plasterBreakdown: CalculationBreakdown = {
    formula: '3.6 kg cement per sqm for 12mm thick plaster (1:4 ratio)',
    components: [
      {
        name: 'Cement for Plaster',
        quantity: plasterCementQty,
        unit: 'kg',
        rate: plasterMaterialRate / 50,
        total: plasterMaterialCost,
        source: 'IS 1661:1972 for cement plaster'
      },
      {
        name: `Plastering Labor (${qualityTier} tier)`,
        quantity: plasterArea,
        unit: 'sqm',
        rate: plasterLaborRate,
        total: plasterLaborCost,
        source: `Plastering labor - ${inputs.location} rates (labor multiplier: ${laborMultiplier})`
      }
    ],
    totalQuantity: plasterArea,
    materialCost: plasterMaterialCost,
    laborCost: plasterLaborCost,
    finalCost: plasterMaterialCost + plasterLaborCost,
    notes: [
      `Material wastage factor: ${cementWastagePercentage}%`,
      `Labor productivity based on ${inputs.location} market conditions`
    ]
  };

  masonryItems.push({
    id: 'plastering',
    name: 'Internal + External Plastering',
    quantity: plasterArea,
    unit: 'sqm',
    rate: (plasterMaterialCost + plasterLaborCost) / plasterArea,
    total: plasterMaterialCost + plasterLaborCost,
    category: 'masonry',
    editable: false,
    isCalculated: true,
    calculationBreakdown: plasterBreakdown
  });

  // Handle room-based customization or standard flooring/painting with tiered labor rates
  if (roomMaterialSelections.length > 0) {
    // Room-wise customization
    const totalFlooringCost = roomMaterialSelections.reduce((sum, selection) => sum + selection.flooringCost, 0);
    const totalPaintCost = roomMaterialSelections.reduce((sum, selection) => sum + selection.paintCost, 0);
    
    masonryItems.push(
      {
        id: 'customized_flooring',
        name: 'Room-wise Flooring (Customized)',
        quantity: 1,
        unit: 'lump sum',
        rate: totalFlooringCost,
        total: totalFlooringCost,
        category: 'finishes',
        editable: false,
        isCalculated: true,
        roomCustomizable: true
      },
      {
        id: 'customized_painting',
        name: 'Room-wise Painting (Customized)',
        quantity: 1,
        unit: 'lump sum',
        rate: totalPaintCost,
        total: totalPaintCost,
        category: 'finishes',
        editable: false,
        isCalculated: true,
        roomCustomizable: true
      }
    );
  } else {
    // Standard flooring and painting using consumption ratios and tiered labor rates
    const floorArea = geometricQuantities.totalFloorArea_sqm;
    
    // Default rates based on quality tier, adjusted by multipliers
    const flooringMaterialRate = (qualityTier === 'best' ? 160 : qualityTier === 'better' ? 75 : 35) * materialMultiplier;
    const flooringLaborRate = (qualityTier === 'best' ? 140 : qualityTier === 'better' ? 120 : 100) * laborMultiplier;
    
    // Apply wastage factor from standards or default
    const flooringWastagePercentage = standards && standards.materialConsumption.wastageFactors.flooring
      ? standards.materialConsumption.wastageFactors.flooring
      : 8;
      
    const flooringWastageMultiplier = 1 + (flooringWastagePercentage / 100);
    
    const actualFlooringArea = floorArea * flooringWastageMultiplier;
    const flooringMaterialCost = actualFlooringArea * flooringMaterialRate;
    const flooringLaborCost = floorArea * flooringLaborRate;

    const flooringBreakdown: CalculationBreakdown = {
      formula: `Flooring Area × (1 + ${flooringWastagePercentage}% wastage)`,
      components: [
        {
          name: `${qualityTier === 'best' ? 'Nitco Luxe' : qualityTier === 'better' ? 'Kajaria Vitrified' : 'Somany Ceramic'} Tiles`,
          quantity: actualFlooringArea,
          unit: 'sqm',
          rate: flooringMaterialRate,
          total: flooringMaterialCost,
          source: `${inputs.location} market rates (material multiplier: ${materialMultiplier})`
        },
        {
          name: `Tile Laying Labor (${qualityTier} tier)`,
          quantity: floorArea,
          unit: 'sqm',
          rate: flooringLaborRate,
          total: flooringLaborCost,
          source: `${inputs.location} labor rates (labor multiplier: ${laborMultiplier})`
        }
      ],
      totalQuantity: floorArea,
      materialCost: flooringMaterialCost,
      laborCost: flooringLaborCost,
      finalCost: flooringMaterialCost + flooringLaborCost,
      notes: [
        `Material wastage factor: ${flooringWastagePercentage}%`,
        'Includes tile adhesive and grouting materials',
        'Based on IS 15622:2017 for ceramic/vitrified tiles'
      ]
    };

    masonryItems.push({
      id: 'flooring',
      name: `Flooring (${qualityTier === 'best' ? 'Nitco Luxe' : qualityTier === 'better' ? 'Kajaria Vitrified' : 'Somany Ceramic'})`,
      quantity: floorArea,
      unit: 'sqm',
      rate: (flooringMaterialCost + flooringLaborCost) / floorArea,
      total: flooringMaterialCost + flooringLaborCost,
      category: 'finishes',
      editable: false,
      isCalculated: true,
      calculationBreakdown: flooringBreakdown,
      roomCustomizable: true,
      materialOptions: ['Somany Ceramic', 'Kajaria Vitrified', 'Nitco Luxe'],
      selectedMaterial: qualityTier === 'best' ? 'Nitco Luxe' : qualityTier === 'better' ? 'Kajaria Vitrified' : 'Somany Ceramic'
    });

    // Painting calculation using consumption ratios and tiered labor rates
    const paintArea = geometricQuantities.totalWallArea_sqm + geometricQuantities.totalCeilingArea_sqm;
    
    // Default rates based on quality tier, adjusted by multipliers
    const paintMaterialRate = (qualityTier === 'best' ? 50 : qualityTier === 'better' ? 35 : 25) * materialMultiplier;
    const paintLaborRate = (qualityTier === 'best' ? 55 : qualityTier === 'better' ? 45 : 35) * laborMultiplier;
    
    // Apply wastage factor from standards or default
    const paintWastagePercentage = standards && standards.materialConsumption.wastageFactors.paint
      ? standards.materialConsumption.wastageFactors.paint
      : 5;
      
    const paintWastageMultiplier = 1 + (paintWastagePercentage / 100);
    
    // Calculate paint coverage (0.13 liters per sqm for 2 coats)
    const paintLiters = paintArea * 0.13 * paintWastageMultiplier;
    const paintMaterialCost = paintLiters * (paintMaterialRate / 0.13); // Convert to per sqm
    const paintLaborCost = paintArea * paintLaborRate;

    const paintBreakdown: CalculationBreakdown = {
      formula: '0.13 liters per sqm for 2 coats of paint',
      components: [
        {
          name: `${qualityTier === 'best' ? 'Asian Paints Royale' : qualityTier === 'better' ? 'Asian Paints Apcolite' : 'Asian Paints Tractor'} Paint`,
          quantity: paintLiters,
          unit: 'liters',
          rate: paintMaterialRate / 0.13,
          total: paintMaterialCost,
          source: `${inputs.location} market rates (material multiplier: ${materialMultiplier})`
        },
        {
          name: `Painting Labor (${qualityTier} tier)`,
          quantity: paintArea,
          unit: 'sqm',
          rate: paintLaborRate,
          total: paintLaborCost,
          source: `${inputs.location} labor rates (labor multiplier: ${laborMultiplier})`
        }
      ],
      totalQuantity: paintArea,
      materialCost: paintMaterialCost,
      laborCost: paintLaborCost,
      finalCost: paintMaterialCost + paintLaborCost,
      notes: [
        `Material wastage factor: ${paintWastagePercentage}%`,
        'Includes primer and two coats of paint',
        'Based on IS 5411 for paint application'
      ]
    };

    masonryItems.push({
      id: 'interior_painting',
      name: `Interior Painting (${qualityTier === 'best' ? 'Asian Paints Royale' : qualityTier === 'better' ? 'Asian Paints Apcolite' : 'Asian Paints Tractor'})`,
      quantity: paintArea,
      unit: 'sqm',
      rate: (paintMaterialCost + paintLaborCost) / paintArea,
      total: paintMaterialCost + paintLaborCost,
      category: 'finishes',
      editable: false,
      isCalculated: true,
      calculationBreakdown: paintBreakdown,
      roomCustomizable: true,
      materialOptions: ['Asian Paints Tractor', 'Asian Paints Apcolite', 'Asian Paints Royale'],
      selectedMaterial: qualityTier === 'best' ? 'Asian Paints Royale' : qualityTier === 'better' ? 'Asian Paints Apcolite' : 'Asian Paints Tractor'
    });
  }

  // MEP SECTION - Calculate based on consumption ratios and tiered labor rates
  const mepItems: CostItem[] = [];

  // Electrical calculation using consumption ratios and tiered labor rates
  const electricalPoints = Math.ceil(geometricQuantities.totalFloorArea_sqm * 0.86);
  
  // Default rates based on quality tier, adjusted by multipliers
  const electricalMaterialRate = (qualityTier === 'best' ? 750 : qualityTier === 'better' ? 500 : 350) * materialMultiplier;
  const electricalLaborRate = (qualityTier === 'best' ? 210 : qualityTier === 'better' ? 180 : 150) * laborMultiplier;

  const electricalMaterialCost = electricalPoints * electricalMaterialRate;
  const electricalLaborCost = electricalPoints * electricalLaborRate;

  const electricalBreakdown: CalculationBreakdown = {
    formula: '0.86 electrical points per sqm of built-up area',
    components: [
      {
        name: `${qualityTier === 'best' ? 'Schneider Livia' : qualityTier === 'better' ? 'Legrand Myrius' : 'Anchor Roma'} Switches`,
        quantity: electricalPoints,
        unit: 'points',
        rate: electricalMaterialRate,
        total: electricalMaterialCost,
        source: `${inputs.location} market rates (material multiplier: ${materialMultiplier})`
      },
      {
        name: `Electrical Wiring Labor (${qualityTier} tier)`,
        quantity: electricalPoints,
        unit: 'points',
        rate: electricalLaborRate,
        total: electricalLaborCost,
        source: `${inputs.location} labor rates (labor multiplier: ${laborMultiplier})`
      }
    ],
    totalQuantity: electricalPoints,
    materialCost: electricalMaterialCost,
    laborCost: electricalLaborCost,
    finalCost: electricalMaterialCost + electricalLaborCost,
    notes: [
      'Based on IS 732:1989 for electrical installations',
      'Includes wiring, conduit, switches, and sockets',
      'Excludes light fixtures and appliances'
    ]
  };

  mepItems.push({
    id: 'electrical',
    name: `Electrical Work (${qualityTier === 'best' ? 'Schneider Livia' : qualityTier === 'better' ? 'Legrand Myrius' : 'Anchor Roma'})`,
    quantity: electricalPoints,
    unit: 'points',
    rate: (electricalMaterialCost + electricalLaborCost) / electricalPoints,
    total: electricalMaterialCost + electricalLaborCost,
    category: 'electrical',
    editable: false,
    isCalculated: true,
    calculationBreakdown: electricalBreakdown,
    roomCustomizable: true,
    materialOptions: ['Anchor Roma', 'Legrand Myrius', 'Schneider Livia'],
    selectedMaterial: qualityTier === 'best' ? 'Schneider Livia' : qualityTier === 'better' ? 'Legrand Myrius' : 'Anchor Roma'
  });

  // Plumbing calculation using consumption ratios and tiered labor rates
  const plumbingPoints = Math.ceil(geometricQuantities.totalFloorArea_sqm * 0.43);
  
  // Default labor rate based on quality tier, adjusted by labor multiplier
  const plumbingLaborRate = (qualityTier === 'best' ? 300 : qualityTier === 'better' ? 250 : 200) * laborMultiplier;
  const plumbingLaborCost = plumbingPoints * plumbingLaborRate;

  const plumbingBreakdown: CalculationBreakdown = {
    formula: '0.43 plumbing points per sqm of built-up area',
    components: [
      {
        name: `Plumbing Installation Labor (${qualityTier} tier)`,
        quantity: plumbingPoints,
        unit: 'points',
        rate: plumbingLaborRate,
        total: plumbingLaborCost,
        source: `${inputs.location} labor rates (labor multiplier: ${laborMultiplier})`
      }
    ],
    totalQuantity: plumbingPoints,
    materialCost: 0,
    laborCost: plumbingLaborCost,
    finalCost: plumbingLaborCost,
    notes: [
      'Based on IS 1172:1993 for water supply requirements',
      'Includes labor for pipe installation',
      'Excludes bathroom fittings and fixtures'
    ]
  };

  mepItems.push({
    id: 'plumbing',
    name: 'Plumbing Work (Complete)',
    quantity: plumbingPoints,
    unit: 'points',
    rate: plumbingLaborRate,
    total: plumbingLaborCost,
    category: 'plumbing',
    editable: false,
    isCalculated: true,
    calculationBreakdown: plumbingBreakdown,
    roomCustomizable: true
  });

  // Apply overrides to any items (only for non-calculated items)
  const allItems = [...foundationItems, ...masonryItems, ...mepItems];
  allItems.forEach(item => {
    if (overrides[item.id] && !item.isCalculated) {
      Object.assign(item, overrides[item.id]);
      item.total = item.quantity * item.rate;
    }
  });

  // Create sections
  sections.push(
    {
      id: 'foundation',
      title: 'Foundation & Structure',
      items: foundationItems,
      subtotal: foundationItems.reduce((sum, item) => sum + item.total, 0)
    },
    {
      id: 'masonry',
      title: 'Masonry & Finishes',
      items: masonryItems,
      subtotal: masonryItems.reduce((sum, item) => sum + item.total, 0)
    },
    {
      id: 'mep',
      title: 'MEP Work',
      items: mepItems,
      subtotal: mepItems.reduce((sum, item) => sum + item.total, 0)
    }
  );

  // Professional fees and other costs from standards
  const subtotalBeforeFees = sections.reduce((sum, section) => sum + section.subtotal, 0);
  
  // Get professional fee percentages from standards or use defaults
  const architectFeePercentage = standards 
    ? (qualityTier === 'best' 
      ? standards.regionalData.professionalFees.architect.tier3 / 100
      : qualityTier === 'better'
      ? standards.regionalData.professionalFees.architect.tier2 / 100
      : standards.regionalData.professionalFees.architect.tier1 / 100)
    : (qualityTier === 'best' ? 0.08 : qualityTier === 'better' ? 0.06 : 0.05);
  
  const otherItems: CostItem[] = [
    {
      id: 'architect_fees',
      name: 'Architect & Engineering Fees',
      quantity: 1,
      unit: 'lump sum',
      rate: subtotalBeforeFees * architectFeePercentage,
      total: subtotalBeforeFees * architectFeePercentage,
      category: 'fees',
      editable: true,
      isCalculated: false
    },
    {
      id: 'approval_fees',
      name: `Municipal Approval Fees (${standards ? standards.regionalData.authority : inputs.location})`,
      quantity: 1,
      unit: 'lump sum',
      rate: calculateApprovalFees(geometricQuantities.totalConstructionArea, inputs, standards),
      total: calculateApprovalFees(geometricQuantities.totalConstructionArea, inputs, standards),
      category: 'fees',
      editable: true,
      isCalculated: false
    }
  ];

  sections.push({
    id: 'other_costs',
    title: 'Professional & Regulatory Fees',
    items: otherItems,
    subtotal: otherItems.reduce((sum, item) => sum + item.total, 0)
  });

  const totalCost = sections.reduce((sum, section) => sum + section.subtotal, 0);
  const ratePerSqft = totalCost / geometricQuantities.totalBuiltUpArea;

  // Create backward-compatible quantities object
  const compatibleQuantities: CalculatedQuantities = {
    ...geometricQuantities,
    // Legacy fields
    totalRCCVolume: geometricQuantities.totalConcreteVolume_m3,
    totalTMTSteel: geometricQuantities.totalSteel_kg,
    totalBrickworkArea: convertSqmToSqft(geometricQuantities.totalWallArea_sqm),
    totalPlasterArea: convertSqmToSqft(geometricQuantities.totalWallArea_sqm),
    numberOfDoors: Math.ceil(geometricQuantities.totalBuiltUpArea / 200),
    numberOfWindows: Math.ceil(geometricQuantities.totalBuiltUpArea / 150),
    numberOfBathrooms: Math.max(1, Math.floor(inputs.numberOfFloors * 1.5)),
    electricalPoints: electricalPoints,
    plumbingPoints: plumbingPoints,
    steelRatioColumns: 160,
    foundationMultiplier: inputs.hasBasement ? 1.8 : 1.0
  };

  return {
    totalCost,
    ratePerSqft,
    sections,
    qualityTier,
    quantities: compatibleQuantities,
    location: inputs.location,
    roomMaterialSelections,
    geometricQuantities
  };
}

// Helper functions
function generateDefaultRoomConfiguration(totalBuiltUpArea: number): RoomConfiguration[] {
  const rooms: RoomConfiguration[] = [];
  
  if (totalBuiltUpArea >= 2400) {
    rooms.push(
      { id: 'bedroom-1', type: 'Bedroom', count: 4, areaPerRoom: 150, totalArea: 600 },
      { id: 'living-1', type: 'Living Room', count: 1, areaPerRoom: 300, totalArea: 300 },
      { id: 'kitchen-1', type: 'Kitchen', count: 1, areaPerRoom: 150, totalArea: 150 },
      { id: 'bathroom-1', type: 'Bathroom', count: 4, areaPerRoom: 60, totalArea: 240 },
      { id: 'dining-1', type: 'Dining Room', count: 1, areaPerRoom: 120, totalArea: 120 },
      { id: 'study-1', type: 'Study Room', count: 1, areaPerRoom: 100, totalArea: 100 },
      { id: 'balcony-1', type: 'Balcony', count: 2, areaPerRoom: 80, totalArea: 160 }
    );
  } else if (totalBuiltUpArea >= 1800) {
    rooms.push(
      { id: 'bedroom-1', type: 'Bedroom', count: 3, areaPerRoom: 140, totalArea: 420 },
      { id: 'living-1', type: 'Living Room', count: 1, areaPerRoom: 250, totalArea: 250 },
      { id: 'kitchen-1', type: 'Kitchen', count: 1, areaPerRoom: 120, totalArea: 120 },
      { id: 'bathroom-1', type: 'Bathroom', count: 3, areaPerRoom: 60, totalArea: 180 },
      { id: 'dining-1', type: 'Dining Room', count: 1, areaPerRoom: 100, totalArea: 100 },
      { id: 'balcony-1', type: 'Balcony', count: 2, areaPerRoom: 70, totalArea: 140 }
    );
  } else {
    rooms.push(
      { id: 'bedroom-1', type: 'Bedroom', count: 2, areaPerRoom: 120, totalArea: 240 },
      { id: 'living-1', type: 'Living Room', count: 1, areaPerRoom: 200, totalArea: 200 },
      { id: 'kitchen-1', type: 'Kitchen', count: 1, areaPerRoom: 100, totalArea: 100 },
      { id: 'bathroom-1', type: 'Bathroom', count: 2, areaPerRoom: 50, totalArea: 100 },
      { id: 'balcony-1', type: 'Balcony', count: 1, areaPerRoom: 60, totalArea: 60 }
    );
  }
  
  const currentTotal = rooms.reduce((sum, room) => sum + room.totalArea, 0);
  const scaleFactor = totalBuiltUpArea / currentTotal;
  
  return rooms.map(room => ({
    ...room,
    areaPerRoom: Math.round(room.areaPerRoom * scaleFactor),
    totalArea: Math.round(room.totalArea * scaleFactor)
  }));
}

function calculateApprovalFees(builtUpArea: number, inputs: UserInputs, standards?: EngineeringStandards): number {
  // Get approval fee rate from standards or use default
  const approvalFeeRate = standards ? standards.regionalData.approvalFeeRate : 400;
  
  let baseFee = convertSqftToSqm(builtUpArea) * approvalFeeRate;
  
  if (inputs.hasBasement) baseFee *= 1.3;
  if (inputs.numberOfFloors > 3) baseFee *= 1.2;
  
  return baseFee;
}

// Legacy function for backward compatibility
export function calculateArchitectAssumptions(inputs: UserInputs, standards?: EngineeringStandards): CalculatedQuantities {
  const geometricQuantities = performDigitalTakedown(inputs, standards);
  
  return {
    ...geometricQuantities,
    totalRCCVolume: geometricQuantities.totalConcreteVolume_m3,
    totalTMTSteel: geometricQuantities.totalSteel_kg,
    totalBrickworkArea: convertSqmToSqft(geometricQuantities.totalWallArea_sqm),
    totalPlasterArea: convertSqmToSqft(geometricQuantities.totalWallArea_sqm),
    numberOfDoors: Math.ceil(geometricQuantities.totalBuiltUpArea / 200),
    numberOfWindows: Math.ceil(geometricQuantities.totalBuiltUpArea / 150),
    numberOfBathrooms: Math.max(1, Math.floor(inputs.numberOfFloors * 1.5)),
    electricalPoints: Math.ceil(convertSqmToSqft(geometricQuantities.totalFloorArea_sqm) * 0.08),
    plumbingPoints: Math.ceil(convertSqmToSqft(geometricQuantities.totalFloorArea_sqm) * 0.04),
    steelRatioColumns: 160,
    foundationMultiplier: inputs.hasBasement ? 1.8 : 1.0
  };
}

export function generateProjectSummary(
  inputs: UserInputs,
  result: CalculationResult
): ProjectSummary {
  const structuralOptions: string[] = [];
  if (inputs.hasBasement) structuralOptions.push('Basement');
  if (inputs.hasStiltParking) structuralOptions.push('Stilt Parking');
  if (inputs.numberOfFloors > 2) structuralOptions.push(`${inputs.numberOfFloors} Floors`);

  const structureCost = result.sections.find(s => s.id === 'foundation')?.subtotal || 0;
  const finishesCost = result.sections.find(s => s.id === 'masonry')?.subtotal || 0;
  const mepCost = result.sections.find(s => s.id === 'mep')?.subtotal || 0;
  const feesCost = result.sections.find(s => s.id === 'other_costs')?.subtotal || 0;

  return {
    projectOverview: {
      plotSize: inputs.plotSize,
      totalBuiltUpArea: result.quantities.totalBuiltUpArea,
      location: inputs.location.charAt(0).toUpperCase() + inputs.location.slice(1),
      structuralOptions
    },
    finalCost: result.totalCost,
    qualityProfile: {
      overallTier: result.qualityTier,
      structure: result.qualityTier === 'best' ? 'Premium (TATA Steel, UltraTech)' : 
                result.qualityTier === 'better' ? 'Standard (Jindal, Ambuja)' : 'Basic (Local Brands)',
      finishing: result.roomMaterialSelections.length > 0 ? 'Custom Room-wise' : 
                result.qualityTier === 'best' ? 'Luxury Finishes' : 
                result.qualityTier === 'better' ? 'Premium Finishes' : 'Standard Finishes',
      fittings: result.qualityTier === 'best' ? 'Premium (Jaquar, Schneider)' : 
               result.qualityTier === 'better' ? 'Standard (Hindware, Legrand)' : 'Basic (Cera, Anchor)'
    },
    costBreakdown: {
      structure: structureCost,
      finishes: finishesCost,
      mep: mepCost,
      fees: feesCost
    },
    customChoices: result.roomMaterialSelections
  };
}

// Helper functions for unit conversion
export function convertSqftToSqm(sqft: number): number {
  return sqft / 10.764;
}

export function convertSqmToSqft(sqm: number): number {
  return sqm * 10.764;
}