import React, { useState, useEffect } from 'react';
import { X, Save, Info } from 'lucide-react';
import { Component, Task } from '../../lib/supabase';

interface ComponentFormProps {
  component: Component | null;
  tasks: Task[];
  onSave: (data: Partial<Component>) => void;
  onCancel: () => void;
}

export function ComponentForm({ 
  component, 
  tasks, 
  onSave, 
  onCancel 
}: ComponentFormProps) {
  const [formData, setFormData] = useState<Partial<Component>>({
    name: '',
    category: '',
    sub_category: '',
    brand: '',
    image_url: '',
    cost_model: 'per_sqm',
    unit_price: 0,
    unit: 'sqm',
    associated_task_id: '',
    specifications: {},
    is_active: true
  });
  
  const [specs, setSpecs] = useState<Array<{key: string; value: string}>>([]);
  const [showSpecsHelp, setShowSpecsHelp] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (component) {
      setFormData(component);
      
      // Convert specifications object to array for editing
      if (component.specifications) {
        const specsArray = Object.entries(component.specifications).map(([key, value]) => ({
          key,
          value: value as string
        }));
        setSpecs(specsArray);
      }
    }
  }, [component]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    
    try {
      // Convert specs array back to object
      const specificationsObject = specs.reduce((obj, spec) => {
        if (spec.key.trim() && spec.value.trim()) {
          obj[spec.key.trim()] = spec.value.trim();
        }
        return obj;
      }, {} as Record<string, string>);
      
      await onSave({
        ...formData,
        specifications: specificationsObject,
        unit_price: Number(formData.unit_price)
      });
    } catch (error) {
      console.error('Error saving component:', error);
      alert('Error saving component. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const addSpecification = () => {
    setSpecs([...specs, { key: '', value: '' }]);
  };

  const updateSpecification = (index: number, key: string, value: string) => {
    const newSpecs = [...specs];
    newSpecs[index] = { key, value };
    setSpecs(newSpecs);
  };

  const removeSpecification = (index: number) => {
    const newSpecs = [...specs];
    newSpecs.splice(index, 1);
    setSpecs(newSpecs);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-3xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <h3 className="text-xl font-bold text-gray-800">
            {component ? 'Edit Component' : 'Add New Component'}
          </h3>
          <button onClick={onCancel} className="p-2 hover:bg-gray-100 rounded-lg" disabled={isSaving}>
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
                disabled={isSaving}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Brand</label>
              <input
                type="text"
                value={formData.brand}
                onChange={(e) => setFormData(prev => ({ ...prev, brand: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isSaving}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <select
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
                disabled={isSaving}
              >
                <option value="">Select category...</option>
                <option value="Flooring">Flooring</option>
                <option value="Windows">Windows</option>
                <option value="Bathroom Fittings">Bathroom Fittings</option>
                <option value="Facade">Facade</option>
                <option value="Materials">Materials</option>
                <option value="Wall Finishes">Wall Finishes</option>
                <option value="Electrical">Electrical</option>
                <option value="Plumbing">Plumbing</option>
                <option value="Doors">Doors</option>
                <option value="Masonry">Masonry</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Sub Category</label>
              <input
                type="text"
                value={formData.sub_category}
                onChange={(e) => setFormData(prev => ({ ...prev, sub_category: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isSaving}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Cost Model</label>
              <select
                value={formData.cost_model}
                onChange={(e) => setFormData(prev => ({ ...prev, cost_model: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isSaving}
              >
                <option value="per_sqm">Per Square Meter</option>
                <option value="per_unit">Per Unit</option>
                <option value="task_based">Task Based</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Unit Price (₹)</label>
              <input
                type="number"
                value={formData.unit_price}
                onChange={(e) => setFormData(prev => ({ ...prev, unit_price: Number(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
                min="0"
                step="0.01"
                disabled={isSaving}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Unit</label>
              <select
                value={formData.unit}
                onChange={(e) => setFormData(prev => ({ ...prev, unit: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isSaving}
              >
                <option value="sqm">Square Meter</option>
                <option value="piece">Piece</option>
                <option value="set">Set</option>
                <option value="kg">Kilogram</option>
                <option value="liter">Liter</option>
                <option value="m">Meter</option>
                <option value="bag">Bag</option>
                <option value="points">Points</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Associated Task</label>
              <select
                value={formData.associated_task_id || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, associated_task_id: e.target.value || undefined }))}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isSaving}
              >
                <option value="">Select task...</option>
                {tasks.map(task => (
                  <option key={task.id} value={task.id}>
                    {task.name}
                  </option>
                ))}
              </select>
              <p className="text-xs text-gray-500 mt-1">
                This links the component to its installation recipe
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Image URL</label>
            <input
              type="url"
              value={formData.image_url || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, image_url: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="https://example.com/image.jpg"
              disabled={isSaving}
            />
          </div>

          {/* Specifications Section */}
          <div className="border-t border-gray-200 pt-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <h4 className="text-sm font-medium text-gray-700">Specifications</h4>
                <button
                  type="button"
                  onClick={() => setShowSpecsHelp(!showSpecsHelp)}
                  className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                  disabled={isSaving}
                >
                  <Info className="w-4 h-4 text-gray-400" />
                </button>
              </div>
              <button
                type="button"
                onClick={addSpecification}
                className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded transition-colors"
                disabled={isSaving}
              >
                Add Specification
              </button>
            </div>
            
            {showSpecsHelp && (
              <div className="mb-3 p-3 bg-blue-50 rounded-lg text-sm text-blue-700">
                Specifications are key-value pairs that describe the component's properties.
                For example, a tile might have specifications like "size: 600x600mm" and "thickness: 8mm".
              </div>
            )}
            
            <div className="space-y-2">
              {specs.map((spec, index) => (
                <div key={index} className="flex gap-2">
                  <input
                    type="text"
                    value={spec.key}
                    onChange={(e) => updateSpecification(index, e.target.value, spec.value)}
                    placeholder="Key (e.g., size)"
                    className="flex-1 px-3 py-2 border border-gray-200 rounded-lg text-sm"
                    disabled={isSaving}
                  />
                  <input
                    type="text"
                    value={spec.value}
                    onChange={(e) => updateSpecification(index, spec.key, e.target.value)}
                    placeholder="Value (e.g., 600x600mm)"
                    className="flex-1 px-3 py-2 border border-gray-200 rounded-lg text-sm"
                    disabled={isSaving}
                  />
                  <button
                    type="button"
                    onClick={() => removeSpecification(index)}
                    className="p-2 hover:bg-red-100 text-red-500 rounded-lg"
                    disabled={isSaving}
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ))}
              
              {specs.length === 0 && (
                <div className="text-center py-3 border border-dashed border-gray-300 rounded-lg text-sm text-gray-500">
                  No specifications added yet
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="is_active"
              checked={formData.is_active}
              onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              disabled={isSaving}
            />
            <label htmlFor="is_active" className="text-sm text-gray-700">Active</label>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="submit"
              disabled={isSaving}
              className="flex-1 flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors disabled:bg-blue-400"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  {component ? 'Update Component' : 'Create Component'}
                </>
              )}
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg font-medium transition-colors"
              disabled={isSaving}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}