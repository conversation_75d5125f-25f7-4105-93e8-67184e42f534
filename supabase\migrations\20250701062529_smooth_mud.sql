/*
  # Fix Missing Core Components and UI Defaults

  1. New Components
    - Add core material components (Cement, TMT Steel, AAC Blocks)
    - Add plumbing components
    - Add electrical components
    - Add masonry components

  2. Update UI Defaults
    - Update UI defaults to reference actual component IDs
    - Fix references to ensure proper component selection
*/

-- Add core material components
INSERT INTO components (name, category, sub_category, brand, cost_model, unit_price, unit, specifications) VALUES
-- Core structural materials
('Cement OPC 53 Grade', 'Materials', 'Cement', 'UltraTech', 'per_unit', 380, 'bag', 
 '{"weight": "50kg", "grade": "OPC 53", "strength": "53 MPa", "standard": "IS 12269:2013"}'),
('TMT Steel Bars Fe 500D', 'Materials', 'Steel', 'TATA Tiscon', 'per_unit', 52, 'kg', 
 '{"grade": "Fe 500D", "diameter": "8-25mm", "yield_strength": "500 MPa", "standard": "IS 1786:2008"}'),
('AAC Blocks 200mm', 'Masonry', 'AAC Blocks', 'Magicrete', 'per_sqm', 95, 'sqm', 
 '{"size": "600x200x200mm", "density": "650 kg/m³", "compressive_strength": "3.5 N/mm²", "thermal_conductivity": "0.16 W/mK"}'),

-- Plumbing components
('CPVC Plumbing Pipes', 'Plumbing', 'Pipes', 'Astral', 'per_unit', 110, 'meter', 
 '{"diameter": "15-25mm", "pressure_rating": "SDR 11", "temperature_rating": "93°C", "standard": "IS 15778:2007"}'),
('Plumbing Fixtures Set', 'Plumbing', 'Fixtures', 'Jaquar', 'per_unit', 15000, 'set', 
 '{"includes": "Taps, valves, connectors", "finish": "Chrome", "warranty": "10 years"}'),

-- Electrical components
('Electrical Wiring Bundle', 'Electrical', 'Wiring', 'Havells', 'per_unit', 350, 'point', 
 '{"wire_type": "FRLS", "size": "1.5-2.5 sq mm", "includes": "Conduit, junction boxes", "standard": "IS 732:1989"}');

-- Link components to tasks
UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Vitrified Tiles' LIMIT 1) 
WHERE name = 'Kajaria Vitrified Tiles 4x2';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Italian Marble' LIMIT 1) 
WHERE name = 'Italian Carrara Marble';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install Anti-Skid Tiles' LIMIT 1) 
WHERE name = 'Anti-Skid Bathroom Tiles';

UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'Install uPVC Windows' LIMIT 1) 
WHERE name = 'Standard uPVC Windows';

-- Update UI defaults to use actual component IDs
UPDATE ui_defaults 
SET config_data = jsonb_build_object(
  'qualityTier', 'better',
  'shell', jsonb_build_object(
    'externalWalls', (SELECT id FROM components WHERE name = 'AAC Blocks 200mm' LIMIT 1),
    'windows', (SELECT id FROM components WHERE name = 'Standard uPVC Windows' LIMIT 1)
  ),
  'rooms', jsonb_build_object(
    'Master Bedroom', jsonb_build_object(
      'flooring', (SELECT id FROM components WHERE name = 'Kajaria Vitrified Tiles 4x2' LIMIT 1)
    ),
    'Bedroom', jsonb_build_object(
      'flooring', (SELECT id FROM components WHERE name = 'Kajaria Vitrified Tiles 4x2' LIMIT 1)
    ),
    'Living Room', jsonb_build_object(
      'flooring', (SELECT id FROM components WHERE name = 'Kajaria Vitrified Tiles 4x2' LIMIT 1)
    ),
    'Kitchen', jsonb_build_object(
      'flooring', (SELECT id FROM components WHERE name = 'Anti-Skid Bathroom Tiles' LIMIT 1)
    ),
    'Bathroom', jsonb_build_object(
      'flooring', (SELECT id FROM components WHERE name = 'Anti-Skid Bathroom Tiles' LIMIT 1),
      'fittingsBundle', (SELECT id FROM components WHERE name = 'Jaquar Premium Bathroom Bundle' LIMIT 1)
    ),
    'Dining Room', jsonb_build_object(
      'flooring', (SELECT id FROM components WHERE name = 'Kajaria Vitrified Tiles 4x2' LIMIT 1)
    )
  ),
  'facade', jsonb_build_object(
    'primaryFinish', (SELECT id FROM components WHERE name = 'Natural Stone Cladding' LIMIT 1),
    'accentFinish', (SELECT id FROM components WHERE name = 'HPL Facade Sheets' LIMIT 1)
  )
)
WHERE config_name = 'default_selections';

-- Create task requirements for core materials
INSERT INTO task_requirements (task_id, component_id, labor_id, quantity_per_sqm, requirement_type) VALUES
-- Steel requirements
((SELECT id FROM tasks WHERE name = 'Install Vitrified Tiles' LIMIT 1), 
 (SELECT id FROM components WHERE name = 'TMT Steel Bars Fe 500D' LIMIT 1), 
 NULL, 0.5, 'material'),

-- Cement requirements
((SELECT id FROM tasks WHERE name = 'Install Vitrified Tiles' LIMIT 1), 
 (SELECT id FROM components WHERE name = 'Cement OPC 53 Grade' LIMIT 1), 
 NULL, 0.2, 'material');

-- Add more realistic material costs for foundation components
INSERT INTO components (name, category, sub_category, brand, cost_model, unit_price, unit, specifications) VALUES
('Ready Mix Concrete M25', 'Materials', 'Concrete', 'UltraTech', 'per_unit', 6500, 'm³', 
 '{"grade": "M25", "slump": "100-150mm", "standard": "IS 456:2000"}'),
('Shuttering Plywood', 'Materials', 'Formwork', 'Century', 'per_sqm', 120, 'sqm', 
 '{"thickness": "12mm", "type": "BWP", "reuse": "8-10 times"}'),
('Binding Wire', 'Materials', 'Steel Accessories', 'Generic', 'per_unit', 85, 'kg', 
 '{"gauge": "18", "material": "Annealed steel"}');

-- Add task for RCC work
INSERT INTO tasks (name, description, category, complexity_level, estimated_duration_hours) VALUES
('RCC Construction', 'Reinforced cement concrete work including formwork', 'foundation', 'advanced', 24);

-- Link RCC task to concrete component
UPDATE components SET associated_task_id = (SELECT id FROM tasks WHERE name = 'RCC Construction' LIMIT 1) 
WHERE name = 'Ready Mix Concrete M25';

-- Add task requirements for RCC work
INSERT INTO task_requirements (task_id, component_id, labor_id, quantity_per_sqm, requirement_type) VALUES
-- Concrete requirements
((SELECT id FROM tasks WHERE name = 'RCC Construction' LIMIT 1), 
 (SELECT id FROM components WHERE name = 'Ready Mix Concrete M25' LIMIT 1), 
 NULL, 1.0, 'material'),

-- Steel requirements
((SELECT id FROM tasks WHERE name = 'RCC Construction' LIMIT 1), 
 (SELECT id FROM components WHERE name = 'TMT Steel Bars Fe 500D' LIMIT 1), 
 NULL, 80.0, 'material'),

-- Formwork requirements
((SELECT id FROM tasks WHERE name = 'RCC Construction' LIMIT 1), 
 (SELECT id FROM components WHERE name = 'Shuttering Plywood' LIMIT 1), 
 NULL, 6.0, 'material'),

-- Labor requirements
((SELECT id FROM tasks WHERE name = 'RCC Construction' LIMIT 1), 
 NULL, 
 (SELECT id FROM labor_rates WHERE name = 'Tile Installation' LIMIT 1), 2.0, 'labor');